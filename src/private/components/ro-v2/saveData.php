<?php
require CONN;
require(INTEGRATIONS_PATH."/mandrill/src/Mandrill.php");
require(INTEGRATIONS_PATH."/twilio/autoload.php");


use Twilio\Rest\Client;

$shopid= $oshopid = (isset($_POST['shopid'])?$_POST['shopid']:'');
$roid=(isset($_POST['roid'])?$_POST['roid']:'');
$type=(isset($_POST['t'])?$_POST['t']:'');

if(in_array($shopid, array('13445','22957'))) $oshopid = '22865';

function grs($length = 10) {
    return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
}

function file_get_contents_curl( $url ) {

  $ch = curl_init();

curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_URL,$url);

  $data = curl_exec( $ch );
  curl_close( $ch );

  return $data;

}

function thicken_image($source, $destination, $offsetRange = 2) {

    $image = imagecreatefrompng($source);

// Get image dimensions
    $width = imagesx($image);
    $height = imagesy($image);

// Create a new image with the same size
    $thickenedImage = imagecreatetruecolor($width, $height);

// Set the background color to transparent
    imagesavealpha($thickenedImage, true);
    $transparency = imagecolorallocatealpha($thickenedImage, 0, 0, 0, 127);
    imagefill($thickenedImage, 0, 0, $transparency);

    for ($i = -$offsetRange; $i <= $offsetRange; $i++) {
        for ($j = -$offsetRange; $j <= $offsetRange; $j++) {
            if ($i !== 0 || $j !== 0) { // Skip the original position
                imagecopy($thickenedImage, $image, $i, $j, 0, 0, $width, $height);
            }
        }
    }

    imagecopy($thickenedImage, $image, 0, 0, 0, 0, $width, $height);

    imagepng($thickenedImage, $destination); // Save to file system

// Free memory
    imagedestroy($image);
    imagedestroy($thickenedImage);
}

switch($type){

	case "robreakdown":

		$runtotalpending = 0;

		// get the totals by status of concerns
		$stmt = "select complaintid from complaints where shopid = ? and roid = ? and acceptdecline = 'Pending'";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("si",$shopid,$roid);
			$query->execute();
			$r = $query->get_result();
			while ($rs = $r->fetch_assoc()){

				// get the parts with this concern
				$pstmt = "select sum(linettlprice) extprice from parts where shopid = ? and roid = ? and complaintid = ?";
				if ($pquery = $conn->prepare($pstmt)){
					$pquery->bind_param("sii",$shopid,$roid,$rs['complaintid']);
					$pquery->execute();
					$pquery->bind_result($ttlparts);
					$pquery->fetch();
					$pquery->close();
				}

				// get the labor with this concern
				$lstmt = "select sum(linetotal) extprice from labor where shopid = ? and roid = ? and complaintid = ?";
				if ($lquery = $conn->prepare($lstmt)){
					$lquery->bind_param("sii",$shopid,$roid,$rs['complaintid']);
					$lquery->execute();
					$lquery->bind_result($ttllbr);
					$lquery->fetch();
					$lquery->close();
				}

				// get the sublet with this concern
				$lstmt = "select sum(subletprice) extprice from sublet where shopid = ? and roid = ? and complaintid = ?";
				if ($lquery = $conn->prepare($lstmt)){
					$lquery->bind_param("sii",$shopid,$roid,$rs['complaintid']);
					$lquery->execute();
					$lquery->bind_result($ttlsublet);
					$lquery->fetch();
					$lquery->close();
				}

				$runtotalpending += $ttlsublet + $ttllbr + $ttlparts;
			}


		}

		$runtotalothers = 0;
		// get the totals by status of concerns
		$stmt = "select complaintid from complaints where shopid = ? and roid = ? and acceptdecline != 'Pending' and acceptdecline != 'declined'";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("si",$shopid,$roid);
			$query->execute();
			$r = $query->get_result();
			while ($rs = $r->fetch_assoc()){

				// get the parts with this concern
				$pstmt = "select sum(linettlprice) extprice from parts where shopid = ? and roid = ? and complaintid = ?";
				if ($pquery = $conn->prepare($pstmt)){
					$pquery->bind_param("sii",$shopid,$roid,$rs['complaintid']);
					$pquery->execute();
					$pquery->bind_result($ttlparts);
					$pquery->fetch();
					$pquery->close();
				}

				// get the labor with this concern
				$lstmt = "select sum(linetotal) extprice from labor where shopid = ? and roid = ? and complaintid = ?";
				if ($lquery = $conn->prepare($lstmt)){
					$lquery->bind_param("sii",$shopid,$roid,$rs['complaintid']);
					$lquery->execute();
					$lquery->bind_result($ttllbr);
					$lquery->fetch();
					$lquery->close();
				}

				// get the sublet with this concern
				$lstmt = "select sum(subletprice) extprice from sublet where shopid = ? and roid = ? and complaintid = ?";
				if ($lquery = $conn->prepare($lstmt)){
					$lquery->bind_param("sii",$shopid,$roid,$rs['complaintid']);
					$lquery->execute();
					$lquery->bind_result($ttlsublet);
					$lquery->fetch();
					$lquery->close();
				}

				$runtotalothers += $ttlsublet + $ttllbr + $ttlparts;
			}


		}


		echo $runtotalpending."|".$runtotalothers;

		break;

	case "clearlube":

		$vin = $_POST['vin'];

		$stmt = "delete from quicklubedata where shopid = ? and vin = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("ss",$shopid,$vin);
			$query->execute();
			$conn->commit();
			$query->close();
		}

		break;

	case "updatetech":

		$tech = $_POST['tech'];
		$tar = explode("|",$tech);
		$ln = $tar[0];
		$fn = $tar[1];
		$techid = $tar[2];
		$techrate = 0;
		if (isset($tar[3])){
			$techrate = $tar[3];
		}

		$tech = $ln.", ".$fn;

		$stmt = "update labor set techrate = ?, tech = ?, laborop = ? where shopid = ? and roid = ? and tech != 'DISCOUNT, DISCOUNT'";
		if ($query = $conn->prepare($stmt)){

			$query->bind_param("dsssi",$techrate,$tech,$techid,$shopid,$roid);
			$query->execute();
			$conn->commit();
			$query->close();

		}

		$stmt = "update repairorders set DefaultTech = ? where shopid = ? and roid = ?";
		if ($query = $conn->prepare($stmt)){

			$query->bind_param("ssi",$tech,$shopid,$roid);
			$query->execute();
			$conn->commit();
			$query->close();

		}

		break;
	case "updatetechoncomplaint":

		$tech = $_POST['tech'];
        $complaintids = $_POST['complaints'];
        $assigntoRO = !empty($_POST['assigntoRO']) ? $_POST['assigntoRO'] : 0;
		$tar = explode("|",$tech);
		$ln = $tar[0];
		$fn = $tar[1];
		$techid = $tar[2];
		$techrate = 0;
		if (isset($tar[3])){
			$techrate = $tar[3];
		}

		$tech = $ln.", ".$fn;
        if (!empty($complaintids)) {

            $stmt = "update labor set techrate = ?, tech = ?, laborop = ? where shopid = ? and roid = ? and complaintid IN ($complaintids) and tech != 'DISCOUNT, DISCOUNT'";
            if ($query = $conn->prepare($stmt)) {

                $query->bind_param("dsssi", $techrate, $tech, $techid, $shopid, $roid);
                $query->execute();
                $conn->commit();
                $query->close();

            }
            $stmt = "update complaints set tech = ? where shopid = ? and roid = ? and complaintid IN ($complaintids) and tech != 'DISCOUNT, DISCOUNT'";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("ssi", $tech, $shopid, $roid);
                $query->execute();
                $conn->commit();
                $query->close();
            }
        }

		break;

	case "updaterate":

		$rate = $_POST['rate'];
		$label = $_POST['label'];

        include_once "rostatus_check.php";

		$stmt = "select laborid,laborhours,discountpercent,lower(scheduletext) as lm,discount,linetotal,hourlyrate from labor where shopid=? and roid=? and deleted='no' and tech != 'DISCOUNT, DISCOUNT'";
		if ($query = $conn->prepare($stmt))
		{
			$query->bind_param("si",$shopid,$roid);
			$query->execute();
			$r = $query->get_result();
			while ($rs = $r->fetch_assoc())
			{
				$calclabor = round($rs['laborhours'] * $rate,2);

				$clabor = sbpround(($rs['laborhours'] * $rs['hourlyrate'])-$rs['discount']);

				if (abs(round($clabor - $rs['linetotal'],3)) >= 0.015)continue;

				if(!empty($rs['discountpercent']))$calclabor = round($calclabor - (($rs['discountpercent']/100)*$calclabor),2);

				if(!empty($rs['lm']) && $rs['lm']!='none')
				{
					$factor = '';

          $stmt = "select factor from labormatrix where shopid = ? and lower(category) = ? and start<= ? and end>=? order by Start limit 1";
					if ($query = $conn->prepare($stmt)){
						  $query->bind_param("ssdd",$shopid,$rs['lm'],$calclabor,$calclabor);
					    $query->execute();
					    $query->bind_result($factor);
					    $query->fetch();
					    $query->close();
					}

					if(!empty($factor))
					$calclabor = round($calclabor*$factor*100)/100;
				}

				$stmt = "update labor set hourlyrate = ?,ratelabel = ?,linetotal = ? where shopid = ? and laborid = ? limit 1";
				if ($query = $conn->prepare($stmt))
				{
					$query->bind_param("dsdsi",$rate,$label,$calclabor,$shopid,$rs['laborid']);
					$query->execute();
					$conn->commit();
					$query->close();
				}

			}
		}

		echo "success";


		break;

	case "addnewsupplier":

		$supp = $_POST['s'];
		$suppid = rand(111,999);
        $supplierid = $display_order = 0;
        $stmt = "select MAX(SupplierID) as supplier_id, MAX(displayorder) as display_order from supplier where shopid = ?";
        if ($query = $conn->prepare($stmt)){
            $query->bind_param("s",$shopid);
            $query->execute();
            $query->bind_result($supplierid, $display_order);
            $query->fetch();
            $query->close();
        }
        $suppid = $supplierid + 1;
        $display_order += 1;

		$stmt = "insert into supplier (supplierid,suppliername,shopid, displayorder) values (?,?,?,?)";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("issi",$suppid,$supp,$shopid, $display_order);
			$query->execute();
			$conn->commit();
			$query->close();
		}
		echo "success";

		break;

	case "storedquicklubedata":

		$vin = trim($_POST['vin']);
		$year = "";
		$stmt = "select `year`,`make`,`model`,`engine`,viscosity,capacity,capdesc,drainplugtorque,oilfilterbrandone,oilfilterpartnumberone,oilfilterbrandtwo,oilfilterpartnumbertwo,coolantcapacity,coolantcapdesc,shopid,vehid,vin from quicklubedata where vin = '$vin' and length(vin) > 0";
		if ($query = $conn->prepare($stmt)){
			$query->execute();
			$query->bind_result($year,$make,$model,$engine,$viscosity,$capacity,$capdesc,$drainplugtorque,$oilfilterbrandone,$oilfilterpartnumberone,$oilfilterbrandtwo,$oilfilterpartnumbertwo,$coolantcapacity,$coolantcapdesc,$shopid,$vehid,$vin);
			$query->fetch();
			$query->close();
		}else{
			echo $conn->error;
		}

		if ($year != ""){


			// now get any xrefs to match wix in oilfilterbrandone
			$pronto = "";
			$valvoline = "";
			$pg = "";
			$fvp = "";
			$mighty = "";
			$carquest = "";
			$napa = "";
			$superchamp = "";
			$warner = "";
			$fram = "";
			$mobil = "";
			$pennzoil = "";
			$performax = "";
			$purolator = "";
			$quakerstate = "";
			$servicepro = "";
			$ofonelc = strtolower($oilfilterbrandone);
			$oftwolc = strtolower($oilfilterbrandtwo);

			$stmt = "select pronto,valvoline,pg,fvp,mighty,carquest,napa,superchamp,warner,fram,mobil,pennzoil,performax,purolator,quakerstate,servicepro from pnxref where wix = '$oilfilterpartnumberone'";
			//echo $stmt;
			if ($query = $conn->prepare($stmt)){
				$query->execute();
				$query->store_result();
				$numrows = $query->num_rows();
				//echo $numrows."\r\n";

				if ($numrows > 0){
					$query->free_result();
					$query->execute();
					$query->bind_result($pronto,$valvoline,$pg,$fvp,$mighty,$carquest,$napa,$superchamp,$warner,$fram,$mobil,$pennzoil,$performax,$purolator,$quakerstate,$servicepro);
					$query->fetch();
				}
				$query->close();
			}

			$sstmt = "select pronto,valvoline,pg,fvp,mighty,carquest,napa,superchamp,warner,fram,mobil,pennzoil,performax,purolator,quakerstate,servicepro from pnxref where wix = '$oilfilterpartnumbertwo'";
			//echo $stmt;
			if ($squery = $conn->prepare($sstmt)){
				$squery->execute();
				$squery->store_result();
				$snumrows = $squery->num_rows();
				//echo $numrows."\r\n";

				if ($snumrows > 0){
					$squery->free_result();
					$squery->execute();
					$squery->bind_result($pronto,$valvoline,$pg,$fvp,$mighty,$carquest,$napa,$superchamp,$warner,$fram,$mobil,$pennzoil,$performax,$purolator,$quakerstate,$servicepro);
					$squery->fetch();
				}
				$squery->close();
			}

			if (is_null($pronto) || $pronto == ""){$pronto = "Not Found";}
			if (is_null($valvoline) || $valvoline == ""){$valvoline = "Not Found";}
			if (is_null($pg) || $pg == ""){$pg = "Not Found";}
			if (is_null($fvp) || $fvp == ""){$fvp = "Not Found";}
			if (is_null($mighty) || $mighty == ""){$mighty = "Not Found";}
			if (is_null($napa) || $napa == ""){$napa = "Not Found";}
			if (is_null($carquest) || $carquest == ""){$carquest = "Not Found";}
			if (is_null($superchamp) || $superchamp == ""){$superchamp = "Not Found";}
			if (is_null($warner) || $warner == ""){$warner = "Not Found";}
			if (is_null($fram) || $fram == ""){$fram = "Not Found";}
			if (is_null($mobil) || $mobil == ""){$mobil = "Not Found";}
			if (is_null($pennzoil) || $pennzoil == ""){$pennzoil = "Not Found";}
			if (is_null($performax) || $performax == ""){$performax = "Not Found";}
			if (is_null($purolator) || $purolator == ""){$purolator = "Not Found";}
			if (is_null($quakerstate) || $quakerstate == ""){$quakerstate = "Not Found";}
			if (is_null($servicepro) || $servicepro == ""){$servicepro = "Not Found";}


			$ar = array($year,$make,$model,$engine,$viscosity,$capacity,$capdesc,$drainplugtorque,$oilfilterbrandone,$oilfilterpartnumberone,$oilfilterbrandtwo,$oilfilterpartnumbertwo,$coolantcapacity,$coolantcapdesc,$shopid,$vehid,$vin,$pronto,$valvoline,$pg,$fvp,$mighty,$carquest,$napa,$superchamp,$warner,$fram,$mobil,$pennzoil,$performax,$purolator,$quakerstate,$servicepro);


			$json = json_encode($ar);
			echo $json;

		}else{

			echo "no data";

		}

		break;


	case "newquicklubedata":

		$json = json_decode($_POST['json'],true);
		//print_r($json);

		if (strlen($json[0]['vin']) > 10){
			$stmt = "insert into quicklubedata (`year`,`make`,`model`,`engine`,viscosity,capacity,capdesc,drainplugtorque,oilfilterbrandone,oilfilterpartnumberone,oilfilterbrandtwo,oilfilterpartnumbertwo,coolantcapacity,coolantcapdesc,shopid,vehid,vin)"
			. " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
			if ($query = $conn->prepare($stmt)){
				$query->bind_param("sssssssssssssssis",$json[0]['year'],$json[0]['make'],$json[0]['model'],$json[0]['engine'],$json[0]['viscosity'],$json[0]['oilCapacity'],$json[0]['oilCapacityDescription'],$json[0]['oilDrainPlugTorque'],$json[0]['OilFilterBrand_1'],$json[0]['OilFilterPartNumber_1'],$json[0]['OilFilterBrand_2'],$json[0]['OilFilterPartNumber_2'],$json[0]['CoolantCapacity'],$json[0]['CoolantCapacityDescription'],$shopid,$json[0]['vehid'],$json[0]['vin']);
				if ($query->execute()){
					$conn->commit();
				}else{
					echo $conn->error;
				}
				$query->close();
				echo "success";
			}else{
				echo $conn->error;
			}
		}else{
			echo "Unable to save info without VIN";
		}
		break;

	case "quicklubedata":
		$yr = trim(urlencode($_POST['yr']));
		$mk = trim(urlencode(str_ireplace(' ','-',$_POST['mk'])));
		$md = trim(urlencode(str_ireplace(' ','-',$_POST['md'])));
		if (strpos($md," ")){
			$mar = explode(" ",$md);
			$md = $mar[0];
		}

		//echo "posteng:".isset($_POST['eng'])."\r\n";

		if (isset($_POST['eng'])){

			$eng = str_ireplace(' ','-',$_POST['eng']);
			$url = "https://api.vin10.net/oil/?key=SB001G9Jl4H3pq901Hb&year=$yr&make=$mk&model=$md&engine=$eng";
			//echo $url;
			$qld = file_get_contents_curl($url);
			echo str_replace('"""','""',$qld);

		}else{

			//echo "mklen:".strlen($mk)."\r\n";
			if (strlen($mk) > 0 && strlen($md) > 0){
				$mdurl = "https://api.vin10.net/oil/?key=SB001G9Jl4H3pq901Hb&year=$yr&make=$mk&model=$md";
				//echo $mdurl."\r\n";
				$mdresult = file_get_contents_curl($mdurl);
				if ($mdresult == ""){

					// try again without model
					$mkurl = "https://api.vin10.net/oil/?key=SB001G9Jl4H3pq901Hb&year=$yr&make=$mk";
					//echo "mkurl:".$mkurl."\r\n";
					$mkresult = file_get_contents_curl($mkurl);

					if ($mkresult == ""){

						// last time with year only
						$yrurl = "https://api.vin10.net/oil/?key=SB001G9Jl4H3pq901Hb&year=$yr";
						//echo "yrurl:".$yrurl."\r\n";
						$yrresult = file_get_contents_curl($yrurl);

						if ($yrresult == ""){

							echo "No Results.  Please check year, make and model";

						}else{

							echo "ym|".$yrresult;

						}

					}else{

						echo "ymm|".$mkresult;

					}

				}else{

					echo "ymme|".$mdresult;

				}
			}elseif (strlen($md) == 0 && strlen($mk) > 0){
				$mkurl = "https://api.vin10.net/oil/?key=SB001G9Jl4H3pq901Hb&year=$yr&make=$mk";
				$mkresult = file_get_contents_curl($mkurl);

				if ($mkresult == ""){

					// last time with year only
					$yrurl = "https://api.vin10.net/oil/?key=SB001G9Jl4H3pq901Hb&year=$yr";
					//echo "yrurl:".$yrurl."\r\n";
					$yrresult = file_get_contents_curl($yrurl);

					if ($yrresult == ""){

						echo "No Results.  Please check year, make and model";

					}else{

						echo "ym|".$yrresult;

					}

				}else{

					echo "ymm|".$mkresult;

				}
			}elseif (strlen($mk) == 0 && strlen($md) == 0){
				$yrurl = "https://api.vin10.net/oil/?key=SB001G9Jl4H3pq901Hb&year=$yr";
				$yrresult = file_get_contents_curl($yrurl);

				if ($yrresult == ""){

					echo "No Results.  Please check year, make and model";

				}else{

					echo "ym|".$yrresult;

				}
			}
		}
		//echo "mdurl:".$mdurl."\r\n";
		// get this url data and test for content



		break;

	case "dupconcern":

        include_once "rostatus_check.php";

		$stmt = "select coalesce(complaintid,0) from complaints where shopid = ? order by complaintid desc limit 1";
		if ($query = $conn->prepare($stmt)){

			$query->bind_param("s",$shopid);
		    $query->execute();
		    $query->store_result();
		    $num_roid_rows = $query->num_rows;
		    //echo "roid rows:".$num_roid_rows."<BR>";
		    if ($num_roid_rows > 0){
			   	$query->bind_result($complaintid);
			    $query->fetch();
			}else{
				$complaintid = 1000;
			}
		    $query->close();

		}else{
			echo "RO Number failed: (" . $conn->errno . ") " . $conn->error;
		}

		// now get existing complaints to get the correct display order
		$stmt = "select displayorder from complaints where shopid = ? and roid = ? order by displayorder desc limit 1";
		if ($query = $conn->prepare($stmt)){

			$query->bind_param("si",$shopid,$roid);
		    $query->execute();
		    $query->store_result();
		    $num_roid_rows = $query->num_rows;
		    //echo "roid rows:".$num_roid_rows."<BR>";
		    if ($num_roid_rows > 0){
			   	$query->bind_result($lastdisplayorder);
			    $query->fetch();
			}else{
				$lastdisplayorder = 0;
			}
		    $query->close();

		}else{
			echo "RO Number failed: (" . $conn->errno . ") " . $conn->error;
		}

		$newcomid = $complaintid + 1;
		$lastdisplayorder += 1;

		$comid = $_POST['comid'];

		// now duplicate the concern, then dup the parts and labor and sublet
		$stmt = "insert into complaints (shopid,complaintid,roid,complaint,techreport,tech,"
		. "acceptdecline,issue,displayorder,advisorcomments,cstatus) select ?"
		. " ,?,?,complaint,techreport,tech,acceptdecline,issue,"
		. "?,advisorcomments,cstatus from complaints where shopid = ? and "
		. "roid = ? and complaintid = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("siiisii",$shopid,$newcomid,$roid,$lastdisplayorder,$shopid,$roid,$comid);
			$query->execute();
			$conn->commit();
			$query->close();
		}else{
			echo $conn->error;
		}

		// now get the parts
		$stmt = "insert into parts (shopid,PartNumber,PartDesc,PartPrice,Quantity,"
		. "ROID,Supplier,Cost,PartInvoiceNumber,PartCode,LineTTLPrice,LineTTLCost,Date,"
		. "PartCategory,complaintid,discount,net,bin,tax,overridematrix,posted,"
		. "scheduledreceivedate,ponumber,allocated,received,updated,displayorder,"
		. "pstatus,deleted,datedone,salesperson) select ?,PartNumber,PartDesc"
		. ",PartPrice,Quantity,?,Supplier,Cost,PartInvoiceNumber,PartCode,LineTTLPrice"
		. ",LineTTLCost,Date,PartCategory,?,discount,net,bin,tax,overridematrix"
		. ",posted,scheduledreceivedate,ponumber,allocated,received,updated,displayorder,"
		. "pstatus,deleted,datedone,salesperson from parts where shopid = ? and roid = ? and complaintid = ?";

		if ($query = $conn->prepare($stmt)){
			$query->bind_param("siisii",$shopid,$roid,$newcomid,$shopid,$roid,$comid);
			$query->execute();
			$conn->commit();
			$query->close();
		}else{
			echo $conn->error;
		}

		$stmt = "insert into labor (shopid,ROID,HourlyRate,LaborHours,Labor,Tech"
		. ",LineTotal,LaborOp,complaintid,techrate,discount,discountpercent,lstatus,"
		. "scheduleddatetime,schedulelength,scheduletext,schedulecat,deleted,memorate,"
		. "datedone) select ?,?,HourlyRate,LaborHours,Labor,Tech,LineTotal"
		. ",LaborOp,?,techrate,discount,discountpercent,lstatus,scheduleddatetime"
		. ",schedulelength,scheduletext,schedulecat,deleted,memorate,datedone"
		. " from labor where shopid = ? and roid = ? and complaintid = ?";

		if ($query = $conn->prepare($stmt)){
			$query->bind_param("siisii",$shopid,$roid,$newcomid,$shopid,$roid,$comid);
			$query->execute();
			$conn->commit();
			$query->close();
		}else{
			echo $conn->error;
		}

		/*
		// get the next sublet id
		$newsubletid = 1;
		$stmt = "select subletid from sublet where shopid = '$shopid' order by subletid desc limit 1";
		if($query = $conn->prepare($stmt)){

			//$query->bind_param("s",$shopid);
		    $query->execute();
		    $query->bind_result($subletid);
		    if ($query->fetch()) {
		    	if (is_numeric($subletid)){
			    	$newsubletid = $subletid + 1;
			    }
		    }

		    $query->close();


		}else{
			echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}


		$stmt = "select roid,SubLetID,SubletDesc,SubletPrice,SubletCost,SubletInvoiceNo"
		. ",SubletSupplier,complaintid,ponumber,deleted,invdate,done from sublet"
		. " where shopid = ? and roid = ? and comid = ?";
		if ($query = $conn->prepare($stmt)){

			$query->bind_param("sii",$shopid,$roid,$comid);
			$query->execute();
			$r = $query->get_result();
			while ($rs = $r->fetch_assoc()){

				$istmt = "insert into sublet (shopid,roid,SubLetID,SubletDesc,SubletPrice,"
				. "SubletCost,SubletInvoiceNo,SubletSupplier,complaintid,ponumber,"
				. "deleted,invdate,done) values (?,?,?,?,?,?,?,?,?,?,?,?,?)";
				if ($iquery = $conn->prepare($istmt)){

					$iquery->bind_param("siisddssissss",$shopid,$roid,$newsubletid,$rs['SubletDesc'],$rs['SubletPrice'],$rs['SubletCost'],$rs['SubletInvoiceNo'],$rs['SubletSupplier'],$newcomid,$rs['ponumber'],$rs['deleted'],$rs['invdate'],$rs['done']);
					$iquery->execute();
					$conn->commit();
					$iquery->close();

				}else{
					$conn->error;
				}

			}

		}*/
        echo "success";
		break;

	case "lockconcern":

		$stmt = "update complaints set locked='yes' where shopid = ? and roid = ? and complaintid = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sss",$shopid,$roid,$_POST['comid']);
			if ($query->execute()){
				$conn->commit();
				echo "success";
			}else{
				echo $conn->errno;
			}

		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}
	break;

	case "unlockconcern":

		$stmt = "update complaints set locked='no' where shopid = ? and roid = ? and complaintid = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sss",$shopid,$roid,$_POST['comid']);
			if ($query->execute()){
				$conn->commit();
				echo "success";
			}else{
				echo $conn->errno;
			}

		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}
	break;

	case "getdiscountinfo":
		$dname = $_POST['dname'];
		$stmt = "select parts,labor,type,partsmax,labormax from discountreasons where shopid = ? and discountreason = ?";
		if ($query = $conn->prepare($stmt)){

			$query->bind_param("ss",$shopid,$dname);
			$query->execute();
			$query->bind_result($parts,$labor,$type,$partsmax,$labormax);
			$query->fetch();
			$query->close();

			echo $parts."|".$labor."|".$type."|".$partsmax."|".$labormax;

		}

		break;

	case "updateschedule":

		$schid = $_POST['schid'];
		$stmt = "update schedule set roid = ? where shopid = ? and id = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("isi",$roid,$shopid,$schid);
			$query->execute();
			$conn->commit();
			$query->close();

		$stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='110'";
        $query = $conn->prepare($stmt);
        $query->execute();
        $query->store_result();
        $numrows = $query->num_rows();
        if ($numrows > 0)
        {
         $query->bind_result($textcontent,$emailcontent,$popupcontent);
	     $query->fetch();
         $stmt = "select concat(firstname,' ',lastname) from schedule where shopid = ? and id = ?";
	     if ($query = $conn->prepare($stmt))
	     {
	     $query->bind_param("si",$shopid,$schid);
		 $query->execute();
		 $query->bind_result($customer);
		 $query->fetch();
		 $query->close();
	     }
	     if(!empty($roid))
	     {
	     	$rolink="for <a href='".COMPONENTS_PRIVATE."/ro/ro.php?roid=$roid'>RO# $roid</a>";
	     	$textrolink="for RO# $roid";
	     }
	     else
	     {
	     	$rolink=$textrolink='';
	     }
	     $emailcontent=str_replace("*|CUSTOMER|*",$customer,str_replace("*|ROLINK|*",$rolink,$emailcontent));
	     $popupcontent=str_replace("*|CUSTOMER|*",$customer,str_replace("*|ROLINK|*",$rolink,$popupcontent));
	     $textcontent=str_replace("*|CUSTOMER|*",$customer,str_replace("*|TEXTROLINK|*",$textrolink,$textcontent));
	     $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'110',?,?,?)";
	     if ($query = $conn->prepare($stmt))
	     {
	     $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
	     $query->execute();
	     $conn->commit();
	     $query->close();
         }
        }
			echo "success";
		}



		break;


	case "createcannedjob":
		$comid = $_POST['comid'];
		$jobname = strtoupper($_POST['jobname']);
		$taxable = $_POST['taxable'];

		// use the comid to create a canned job
		$stmt = "insert into cannedjobs (shopid,jobname,taxable) values ('$shopid',?,'$taxable')";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("s",$jobname);
			$query->execute();
			$id=$conn->insert_id;
			$conn->commit();
			$query->close();
		}

		$stmt = "select labor, laborhours, hourlyrate from labor where shopid = '$shopid' and roid = $roid and complaintid = $comid and deleted = 'no'";
		//echo $stmt."\r\n";
		if ($query = $conn->prepare($stmt)){
			$query->execute();
			$result = $query->get_result();
			while ($row = $result->fetch_array()){
				$lstmt = "insert into cannedlabor (cannedjobsid,shopid,labor,laborhours,rateforcalc) values ($id,'$shopid',?,?,?)";
				if ($lquery = $conn->prepare($lstmt)){
					$lquery->bind_param("sdd",$row['labor'],$row['laborhours'],$row['hourlyrate']);
					$lquery->execute();
					$conn->commit();
					$lquery->close();
					//echo "added labor";
				}else{
					echo "labor error: ".$conn->error."\r\n";
				}
			}
			$query->close();
		}

		$stmt = "select partnumber,partdesc,supplier,cost,partprice,quantity,tax,partcategory,partcode,bin,overridematrix from parts where shopid = '$shopid' and roid = $roid and complaintid = $comid and deleted = 'no'";
		//echo $stmt."\r\n";
		if ($query = $conn->prepare($stmt)){
			$query->execute();
			$result = $query->get_result();
			while ($row = $result->fetch_array()){
				$lstmt = "insert into cannedparts (cannedjobsid,shopid,partnumber,partdescription,supplier,partcost,partprice,qty,tax,partcategory,partcode,bin,overridematrix) values "
				. "($id,'$shopid',?,?,?,?,?,?,?,?,?,?,?)";
				if ($lquery = $conn->prepare($lstmt)){
					$lquery->bind_param("sssdddsssss",$row['partnumber'],$row['partdesc'],$row['supplier'],$row['cost'],$row['partprice'],$row['quantity'],$row['tax'],$row['partcategory'],$row['partcode'],$row['bin'],$row['overridematrix']);
					$lquery->execute();
					$conn->commit();
					$lquery->close();
					//echo "added part";
				}else{
					echo "part error: ".$conn->error."\r\n";
				}
			}
			$query->close();
		}

		$sstmt = "insert into cannedsublet (cannedjobsid,shopid,subletdesc,subletprice,subletcost,subletsupplier,taxable) select '$id','$shopid',subletdesc,subletprice,subletcost,subletsupplier,taxable from sublet where shopid = '$shopid' and roid = $roid and complaintid = $comid and deleted = 'no'";
			if ($squery = $conn->prepare($sstmt)){
				$squery->execute();
				$conn->commit();
				$squery->close();
			}


		echo "success";

		break;

	case "addcorecharge":

		$pn = $_POST['pn'];
		$pd = $_POST['pd'];
		$supp = $_POST['supp'];
		$cc = $_POST['cc'];
		$od = date("Y-m-d H:i:s", strtotime($_POST['od']));

		$stmt = "insert into cores (shopid,partnumber,partdesc,corecharge,supplier,partorderdate,roid) values (?,?,?,?,?,?,?)";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sssdssi",$shopid,$pn,$pd,$cc,$supp,$od,$roid);
			$query->execute();
			$conn->commit();
			$query->close();
		}

		echo "success";
		break;

	case "origro":

		$amt = $_POST['amt'];
		$stmt = "update repairorders set origro = ? where shopid = '$shopid' and roid = $roid";
		echo $stmt;
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("d",$amt);
			$query->execute();
			$conn->commit();
			$query->close();
		}

		echo "success";


		break;

	case "refname":

		$origtech = $_POST['origtech'];
		$source = $_POST['source'];
		//echo "New Orig tech in save data is" . $origtech;
		$stmt = "update repairorders set origtech = ?, source = ? where shopid = '$shopid' and roid = $roid";

		//echo $stmt;
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("ss",$origtech,$source);
			$query->execute();
			$conn->commit();
			$query->close();
		}

		echo "success";


		break;

	case "settag":

		$tag = $_POST['tag'];
		$stmt = "update repairorders set tagnumber = ? where shopid = '$shopid' and roid = $roid";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("s",$tag);
			$query->execute();
			$conn->commit();
			$query->close();
		}

		echo "success";
		break;


	case "addflycannedjob":

		// addcomid = original com id to select all the data
		// addroid = original roid to select all the data
		// selcomid = current ro comid or new concern
		// roid = current roid to add data to
		// addcomid="+addcomid+"&selcomid="+selcomid+"&addroid="+addroid
        include_once "rostatus_check.php";
		$selcomid = $_POST['selcomid'];
		$addcomid = $_POST['addcomid'];
		$addroid = $_POST['addroid'];
		$catval = $_POST['vicat'];

		// insert into roid and selcomid select * from parts and labor where comid = addcomid and roid = addroid
		if ($selcomid == "new"){

			// use the original concern text
			$stmt = "select complaint from complaints where shopid = '$shopid' and roid = $addroid and complaintid = $addcomid";
			if ($query = $conn->prepare($stmt)){
				$query->execute();
				$query->bind_result($complaint);
				$query->fetch();
				$query->close();
			}

			// get the next complaintid
			$stmt = "select coalesce(complaintid,0) from complaints where shopid = ? order by roid desc limit 1";
			if ($query = $conn->prepare($stmt)){

				$query->bind_param("s",$shopid);
			    $query->execute();
			    $query->store_result();
			    $num_roid_rows = $query->num_rows;
			    //echo "roid rows:".$num_roid_rows."<BR>";
			    if ($num_roid_rows > 0){
				   	$query->bind_result($complaintid);
				    $query->fetch();
				}else{
					$complaintid = 1000;
				}
			    $query->close();

			}else{
				echo "RO Number failed: (" . $conn->errno . ") " . $conn->error;
			}

			$newcomid = $complaintid + 1;

			// now get existing complaints to get the correct display order
			$stmt = "select displayorder from complaints where shopid = ? and roid = ? order by displayorder desc limit 1";
			if ($query = $conn->prepare($stmt)){

				$query->bind_param("si",$shopid,$roid);
			    $query->execute();
			    $query->store_result();
			    $num_roid_rows = $query->num_rows;
			    //echo "roid rows:".$num_roid_rows."<BR>";
			    if ($num_roid_rows > 0){
				   	$query->bind_result($lastdisplayorder);
				    $query->fetch();
				}else{
					$lastdisplayorder = 0;
				}
			    $query->close();

			}else{
				echo "RO Number failed: (" . $conn->errno . ") " . $conn->error;
			}

			$lastdisplayorder += 1;

			// create a new complaint
			$stmt = "insert into complaints (shopid,roid,complaint,acceptdecline,complaintid,displayorder,issue) values (?,?,?,'Pending',?,?,?)";
			if ($query = $conn->prepare($stmt)){
				$query->bind_param('sisiis',$shopid,$roid,$complaint,$newcomid,$lastdisplayorder,$catval);
				if ($query->execute()){
					$conn->commit();
					$query->close();
				}else{
					echo $conn->errno;
				}
			}

			$stmt = "insert into labor (shopid,roid,hourlyrate,laborhours,labor,tech,linetotal,complaintid) select shopid,$roid,hourlyrate,laborhours,labor,tech,linetotal,$newcomid"
			. " from labor where roid = $addroid and shopid = '$shopid' and complaintid = $addcomid";
			if ($query = $conn->prepare($stmt)){
				$query->execute();
				$conn->commit();
				$query->close();
			}

			$pdate = date("Y-m-d");
			$stmt = "insert into parts (shopid,partnumber,partdesc,partprice,quantity,roid,supplier,cost,partcode,linettlprice,linettlcost,`date`,partcategory,complaintid,discount,net,tax,overridematrix) "
			. " select shopid,partnumber,partdesc,partprice,quantity,$roid,supplier,cost,partcode,linettlprice,linettlcost,'$pdate',partcategory,$newcomid,discount,net,tax,overridematrix "
			. " from parts where roid = $addroid and shopid = '$shopid' and complaintid = $addcomid";
			if ($query = $conn->prepare($stmt)){
				$query->execute();
				$conn->commit();
				$query->close();
			}

		}else{

			$stmt = "insert into labor (shopid,roid,hourlyrate,laborhours,labor,tech,linetotal,complaintid) select shopid,$roid,hourlyrate,laborhours,labor,tech,linetotal,$selcomid"
			. " from labor where roid = $addroid and shopid = '$shopid' and complaintid = $addcomid";
			if ($query = $conn->prepare($stmt)){
				$query->execute();
				$conn->commit();
				$query->close();
			}

			$pdate = date("Y-m-d");
			$stmt = "insert into parts (shopid,partnumber,partdesc,partprice,quantity,roid,supplier,cost,partcode,linettlprice,linettlcost,`date`,partcategory,complaintid,discount,net,tax,overridematrix) "
			. " select shopid,partnumber,partdesc,partprice,quantity,$roid,supplier,cost,partcode,linettlprice,linettlcost,'$pdate',partcategory,$selcomid,discount,net,tax,overridematrix "
			. " from parts where roid = $addroid and shopid = '$shopid' and complaintid = $addcomid";
			if ($query = $conn->prepare($stmt)){
				$query->execute();
				$conn->commit();
				$query->close();
			}


		}

		echo "success";
		break;

	case "searchhistorydetail":

		$returnval = "";
		$comid = $_POST['comid'];
		$stmt = "select partnumber,partdesc,quantity,cost,partprice from parts where shopid = '$shopid' and roid = $roid and complaintid = $comid";
		//echo $stmt;
		if ($query = $conn->prepare($stmt)){
			$returnval .= "<div class='col-sm-12'>";
			$returnval .= "<table class='table 2-100'>";
			$returnval .= "<tr><th colspan='5'><h6 class='text-primary text-bold'>PARTS</h6></th></tr>";
			$returnval .= "<tr>";
			$returnval .= "<th>PART NUMBER</th>";
			$returnval .= "<th>DESCRIPTION</th>";
			$returnval .= "<th>QTY</th>";
			$returnval .= "<th>COST</th>";
			$returnval .= "<th>LIST</th>";
			$returnval .= "</tr>";
			$query->execute();
			$result = $query->get_result();
			while ($row = $result->fetch_array()){
				$returnval .= "<tr>";
				$returnval .= "<td>".strtoupper($row['partnumber'])."</td>";
				$returnval .= "<td>".strtoupper($row['partdesc'])."</td>";
				$returnval .= "<td>".number_format($row['quantity'],2)."</td>";
				$returnval .= "<td>".asDollars($row['cost'])."</td>";
				$returnval .= "<td>".asDollars($row['partprice'])."</td>";
				$returnval .= "</tr>";
			}
		}

		// get the labor
		$stmt = "select labor,laborhours,tech from labor where shopid = '$shopid' and roid = $roid and complaintid = $comid";
		//echo $stmt;
		if ($query = $conn->prepare($stmt)){
			$returnval .= "<tr><th colspan='5'><h6 class='text-primary text-bold'>LABOR</h6></th></tr>";
			$returnval .= "<tr>";
			$returnval .= "<th>TECH</th>";
			$returnval .= "<th>DESCRIPTION</th>";
			$returnval .= "<th>HOURS</th>";
			$returnval .= "<th>&nbsp;</th>";
			$returnval .= "<th>&nbsp;</th>";
			$returnval .= "</tr>";
			$query->execute();
			$result = $query->get_result();
			while ($row = $result->fetch_array()){
				$returnval .= "<tr>";
				$returnval .= "<td>".strtoupper($row['tech'])."</td>";
				$returnval .= "<td>".strtoupper($row['labor'])."</td>";
				$returnval .= "<td>".strtoupper($row['laborhours'])."</td>";
				$returnval .= "<td>&nbsp;</td>";
				$returnval .= "<td>&nbsp;</td>";
				$returnval .= "</tr>";
			}
		}

		// get the sublet
		$stmt = "select subletdesc,subletprice,subletcost,subletsupplier,subletinvoicenumber from sublet where shopid = '$shopid' and roid = $roid and complaintid = $comid";
		//echo $stmt;
		if ($query = $conn->prepare($stmt)){
			$returnval .= "<tr><th colspan='5'><h6 class='text-primary text-bold'>SUBLET</h6></th></tr>";
			$returnval .= "<tr>";
			$returnval .= "<th>SUPPLIER</th>";
			$returnval .= "<th>DESCRIPTION</th>";
			$returnval .= "<th>INV #</th>";
			$returnval .= "<th>COST</th>";
			$returnval .= "<th>LIST</th>";
			$returnval .= "</tr>";
			$query->execute();
			$result = $query->get_result();
			while ($row = $result->fetch_array()){
				$returnval .= "<tr>";
				$returnval .= "<td>".strtoupper($row['subletsupplier'])."</td>";
				$returnval .= "<td>".strtoupper($row['subletdesc'])."</td>";
				$returnval .= "<td>".strtoupper($row['subletinvoicenumber'])."</td>";
				$returnval .= "<td>".number_format($row['subletcost'],2)."</td>";
				$returnval .= "<td>".number_format($row['subletprice'],2)."</td>";
				$returnval .= "</tr>";
			}
		}
		$returnval .= "</table></div>";
		$returnval .= "<div class='d-flex justify-content-center'><button onclick='addJob(".$comid.",".$roid.")' class='btn btn-secondary float-end'>Add to RO</button></div>";
		echo $returnval;
		break;

	case "searchhistory":

		$vehyear = str_replace("'","''",$_POST['vehyear']);
		$vehmake = str_replace("'","''",$_POST['vehmake']);
		$vehmodel = str_replace("'","''",$_POST['vehmodel']);
		$vehengine = str_replace("'","''",$_POST['vehengine']);

		$stmt = "select complaint,complaintid,r.roid,c.issue from repairorders r inner join complaints c on r.shopid = c.shopid and r.roid = c.roid"
		. " where r.shopid = '$shopid' and cstatus = 'no' and r.roid != $roid";
		if ($vehyear != "DONOTUSE"){
			$stmt .= " and vehyear = '$vehyear' ";
		}
		if ($vehmake != "DONOTUSE"){
			$stmt .= " and vehmake = '$vehmake' ";
		}
		if ($vehmodel != "DONOTUSE"){
			$stmt .= " and vehmodel = '$vehmodel' ";
		}
		if ($vehengine != "DONOTUSE"){
			$stmt .= " and vehengine = '$vehengine' ";
		}
		$stmt .= " order by complaint asc limit 1000";
		//echo $stmt;
		if ($query = $conn->prepare($stmt)){
		?>
		<?php
			$query->execute();
			$result = $query->get_result();
			while ($row = $result->fetch_array()){
		?>
						<div class="accordion mt-2 resultclick">
							<div class="accordion-item">
								<div class="accordion-header" id="flush-cantax">
									<button class="accordion-button collapsed pb-1" type="button"
											data-mdb-toggle="collapse" id="display<?php echo $row['complaintid']; ?>" onclick="showDetail(<?php echo $row['complaintid']; ?>,<?php echo $row['roid']; ?>)"
											data-mdb-target="#vi_<?php echo $row['complaintid']; ?>" aria-expanded="false" aria-controls="advisors">
										<div class="w-100"><span class="vehicle_issue"><?= strtoupper(str_replace("\r\n","",$row['complaint'])); ?></span><?php
											if (strlen($row['issue']) > 0){
												?>
												<span class="float-end me-3 pe-3">Category: <?php echo strtoupper($row['issue']); ?></span>
												<?php
											}
											?></div>
									</button>
								</div>
								<div id="vi_<?php echo $row['complaintid']; ?>" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
									 data-mdb-parent="#cantax">
									<div class="accordion-body">
										<div class="row" id="result<?php echo $row['complaintid']; ?>">

										</div>
									</div>
						   </div>
					   </div>
			</div>
		<?php
			}
		}else{
			echo "No results";
		}


		break;

	case "checkinv":

		$pn = $_POST['pn'];
		//echo $pn;
		$pnar = array();
		if (strpos($pn,",") > 0){
			$pnar = explode(",",$pn);
		}
		$resultar = array();

		foreach ($pnar as $v){

			if (strlen($v) > 0){
				$netonhand = 0;
				//$v = str_replace("-","",$v);
				//$v = str_replace(" ","",$v);
				$stmt = "select coalesce(netonhand,0) netonhand, count(*) c from partsinventory where shopid = '$oshopid' and partnumber = '$v'";
				//echo $stmt;
				if ($query = $conn->prepare($stmt)){
					//$query->bind_param("s",$v);
					$query->execute();
					$query->bind_result($netonhand,$count);
					$query->fetch();
					// count > 0 means in stock item
					// netonhand =  how many in stock
						// if 0 instock means IN-STOCK-LAST
						// if 1 or mor means IN-STOCK
						// if < 0 in stock means OUT_OF_STOCK
					if ($count >= 1){
						// in stock
						if ($netonhand >= 1){
							$resultar[$v] = "yes";
						}
						if ($netonhand == 0){
							$resultar[$v] = "last";
						}
						if ($netonhand < 0){
							$resultar[$v] = "oos";
						}
					}
					$query->close();
				}
			}
		}

		$json = json_encode($resultar);
		echo $json;

		break;

	case "setpromisedate":

		$d = $_POST['d'];
		$stmt = "update repairorders set datetimepromised = '$d' where shopid = '$shopid' and roid = $roid";
		if ($query = $conn->prepare($stmt)){
			if ($query->execute()){
				$conn->commit();
			}else{
				echo $conn->errno;
			}
			$query->close();
		}else{
			echo "Update DateTimePromised failed: (" . $conn->errno . ") " . $conn->error;
		}


		break;

	case "savenewquoteitem":

		$dstr=explode('*||*',$_POST['dstr']);

		if(!empty($dstr))
		{
		 foreach($dstr as $arr)
		 {

		 	$exparr = explode('*|*',$arr);
		 	$laborid = $exparr[0];

			$stmt = "update labor set tech = ? where shopid = '$shopid' and roid = $roid and laborid = $laborid";

			if ($query = $conn->prepare($stmt)){
				$query->bind_param("s",$exparr[1]);
				if ($query->execute()){
					$conn->commit();
				}else{
					echo $conn->errno;
				}
				$query->close();
			}else{
				echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error; die();
			}

		}
	 }


    echo("success");


		break;

			case "savequoteitem":

		$quoteid = $_POST['quoteid'];
		$tempdate = date("Y-m-d");

		$dstr=explode('*||*',$_POST['dstr']);

		if(!empty($dstr))
		{
		 foreach($dstr as $arr)
		 {

		 	$exparr = explode('*|*',$arr);
		 	$quoteitemtype = $exparr[0];
		 	$quoteitemid = $exparr[1];
		 	$comid = $exparr[2];

     		if ($quoteitemtype == "p"){
			$stmt = "insert into parts (shopid,PartNumber,PartDesc,PartPrice,Quantity,ROID,Supplier,Cost,PartInvoiceNumber,PartCode,LineTTLPrice,LineTTLCost,Date,PartCategory,complaintid,discount"
			. ",net,bin,tax,overridematrix,ponumber,pstatus,deleted,cannedjobsid) select shopid,partnumber,part,price,qty,$roid,supplier,partcost,'',partcode,extprice,partcost*qty,'$tempdate',matrixcat,$comid,discount,net,bin,taxable,'no'"
		. ",'','','no',cannedjobsid from quoteparts where shopid = '$shopid' and quoteid = $quoteid and id = $quoteitemid";
		//echo $stmt;
			if ($query = $conn->prepare($stmt)){
				if ($query->execute()){
					$conn->commit();
				}else{
					echo $conn->errno;
				}
				$query->close();
			}else{
				echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error; die();
			}

		if(strtoupper($_POST['updateinv'])=='YES')
		{

		$stmt = "select qty,partnumber from quoteparts where shopid = ? and quoteid = ? and id = ?";
        if ($query = $conn->prepare($stmt)){
	    $query->bind_param("sss",$shopid,$quoteid,$quoteitemid);
	    $query->execute();
	    $r = $query->get_result();
	    while ($rs = $r->fetch_array()){
	     $pq = $rs['qty'];
		 $stmt = "update partsinventory set onhand = onhand - $pq, netonhand = netonhand - $pq where shopid = ? and partnumber = ?";
	     if ($query = $conn->prepare($stmt)){
	     $query->bind_param("ss",$oshopid,$rs['partnumber']);
		 $query->execute();
		 $conn->commit();
		 $query->close();
	     }

	     $stmt = "select NetOnHand,ReOrderLevel from partsinventory where shopid = ? and partnumber = ?";
         if ($query = $conn->prepare($stmt))
         {
          $query->bind_param("ss",$oshopid,$rs['partnumber']);
          $query->execute();
          $query->bind_result($netonhand,$reorderlevel);
          $query->fetch();
          $query->close();
         }

         if ($netonhand<=$reorderlevel && $netonhand!='' && $reorderlevel!='')
         {
          $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid = ? AND s.notification_type='166'";
          if ($query = $conn->prepare($stmt))
          {
           $query->bind_param("s", $shopid);
           $query->execute();
           $query->store_result();
           $num_rows = $query->num_rows;
           if($num_rows>0)
           {
           $query->bind_result($textcontent, $emailcontent, $popupcontent);
           $query->fetch();

           $popupcontent = str_replace("*|PARTNUMBER|*", $rs['partnumber'], $popupcontent);
           $emailcontent = str_replace("*|PARTNUMBER|*", $rs['partnumber'], $emailcontent);
           $textcontent = str_replace("*|PARTNUMBER|*", $rs['partnumber'], $textcontent);
           $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'166',?,?,?)";
           if ($squery = $conn->prepare($stmt))
           {
            $squery->bind_param("ssss", $shopid, $popupcontent, $textcontent, $emailcontent);
            $squery->execute();
            $conn->commit();
            $squery->close();
           }
           }
          }
         }

	    }
         }
        }

			$stmt = "delete from quoteparts where shopid = '$shopid' and quoteid = $quoteid and id = $quoteitemid";
			if ($query = $conn->prepare($stmt)){
				if ($query->execute()){
					$conn->commit();
				}else{
					echo $conn->errno;
				}
				$query->close();
			}else{
				echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}
		}


		elseif ($quoteitemtype == "s"){

			$stmt = "select subletid from sublet where shopid = '$shopid' order by subletid desc limit 1";
		if ($query = $conn->prepare($stmt)){
		    $query->execute();
		    $query->store_result();
		    $query->bind_result($newsubletid);
		    $query->fetch();
		    $query->close();
		}else{
			echo "3Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

			$stmt = " SELECT shopid,$roid,`description`,price,cost,invnum,`supplier` from quotesublet where shopid = '$shopid' and quoteid = $quoteid and id = $quoteitemid";

		if ($query = $conn->prepare($stmt)){
			$query->execute();
			$result = $query->get_result();
			while ($row = $result->fetch_array()){
				$newsubletid = $newsubletid + 1;
				$desc = $row["description"];
				$subprice = $row["price"];
				$subcost = $row["cost"];
				$subinvnum = $row["invnum"];
				$subsupplier = $row["supplier"];

				$stmt = "insert into sublet (shopid,SubletID,ROID,SubletDesc,SubletPrice,SubletCost,SubletInvoiceNo,SubletSupplier,complaintid)"
				. "values ('$shopid',$newsubletid,$roid,'$desc',$subprice,$subcost,'$subinvnum','$subsupplier',$comid)";


				if ($query = $conn->prepare($stmt)){
					if ($query->execute()){
						$conn->commit();
					}else{
						echo $conn->error;
					}
				}

			}

		}

			$stmt = "delete from quotesublet where shopid = '$shopid' and quoteid = $quoteid and id = $quoteitemid";
			if ($query = $conn->prepare($stmt)){
				if ($query->execute()){
					$conn->commit();
				}else{
					echo $conn->errno;
				}
				$query->close();
			}else{
				echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}
		}

		elseif ($quoteitemtype == "l"){

			$stmt = "insert into labor (shopid,roid,hourlyrate,laborhours,labor,tech,linetotal,complaintid,schedulelength,cannedjobsid) select shopid,$roid,rate,hours,labor,?,extlabor,$comid,lower(taxable),cannedjobsid from quotelabor"
			. " where shopid = '$shopid' and quoteid = $quoteid and id = $quoteitemid";
			//echo $stmt;
			if ($query = $conn->prepare($stmt)){
				$query->bind_param("s",$exparr[3]);
				if ($query->execute()){
					$conn->commit();
				}else{
					echo $conn->errno;
				}
				$query->close();
			}else{
				echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error; die();
			}

			$stmt = "delete from quotelabor where shopid = '$shopid' and quoteid = $quoteid and id = $quoteitemid";
			if ($query = $conn->prepare($stmt)){
				if ($query->execute()){
					$conn->commit();
				}else{
					echo $conn->errno;
				}
				$query->close();
			}else{
				echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

		}
	}
    }

    $stmt = "delete from quotes where shopid = '$shopid' and id = $quoteid";
	if ($query = $conn->prepare($stmt)){
	 if ($query->execute()){
	$conn->commit();
	}else{
	echo $conn->errno;
	}
	$query->close();
	}else{
	echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

    echo("success");


		break;

	case "getsublet":

		$subletid = $_POST['subletid'];
		$stmt = "select subletdesc,subletprice,subletcost,subletinvoiceno,subletsupplier from sublet where subletid = $subletid and shopid = '$shopid' and roid = $roid";
		if ($query = $conn->prepare($stmt)){

			$query->execute();
			$query->bind_result($subletdesc,$subletprice,$subletcost,$subletinvoiceno,$subletsupplier);
			$query->fetch();
			$query->close();
			echo strtoupper($subletdesc)."|".number_format($subletprice,2)."|".number_format($subletcost,2)."|".strtoupper($subletinvoiceno)."|".strtoupper($subletsupplier);

		}

		break;

	case "editpayment":

		$id = $_POST['id'];
		$pdate = date("Y-m-d",strtotime($_POST['pdate']));
		$ptype = $_POST['ptype'];
		$pnumber = $_POST['pnumber'];
		$amt = filter_var($_POST['amt'],FILTER_SANITIZE_NUMBER_FLOAT,FILTER_FLAG_ALLOW_FRACTION);

		$stmt = "update accountpayments set pdate = ?, ptype = ?, pnumber = ?, amt = ? where id = ? and shopid = ?";
		if ($query = $conn->prepare($stmt)){

			$query->bind_param("sssdis",$pdate,$ptype,$pnumber,$amt,$id,$shopid);
			$query->execute();
			$conn->commit();
			$query->close();
			echo "success";

		}else{

			echo $conn->error;

		}

		break;

	case "getpayment":

		$id = $_POST['id'];
		$stmt = "select pdate,ptype,pnumber,amt+surcharge from accountpayments where id = $id and shopid = '$shopid'";
		if ($query = $conn->prepare($stmt)){

			$query->execute();
			$query->bind_result($pdate,$ptype,$pnumber,$amt);
			$query->fetch();
			$query->close();
			echo date("m/d/Y",strtotime($pdate))."|".$ptype."|".$pnumber."|".number_format($amt,2,'.','');

		}else{

			echo $conn->error;

		}

		break;

	case "applydiscount":

		$partsdiscount = $_POST['partsdiscount'];
		$labordiscount = $_POST['labordiscount'];

		// if partsdiscount > 0 then delete all parts discount lines and add the new one

		if ($partsdiscount < 0){

			$stmt = "delete from parts where shopid = '$shopid' and roid = $roid and partnumber = 'DISCOUNT' and partdesc = 'DISCOUNT' and supplier = 'DISCOUNT'";
			echo $stmt."\r\n";
			if ($query = $conn->prepare($stmt)){
				if ($query->execute()){
					$conn->commit();
				}else{
					echo $conn->errno;
				}
			}else{
				echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

			$stmt = "insert into parts (shopid,roid,partnumber,partdesc,quantity,supplier,partcategory,tax,partprice,linettlprice) values ('$shopid',$roid,'DISCOUNT','DISCOUNT',1,'DISCOUNT','DISCOUNT','yes',$partsdiscount,$partsdiscount)";
			echo $stmt."\r\n";
			if ($query = $conn->prepare($stmt)){
				if ($query->execute()){
					$conn->commit();
				}else{
					echo $conn->errno;
				}
			}else{
				echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}
		}

		// if labordiscount > 0 then delete all labor discount lines and add the new one

		if ($labordiscount < 0){

			$stmt = "delete from labor where shopid = '$shopid' and roid = $roid and tech = 'DISCOUNT, DISCOUNT' and labor = 'DISCOUNT'";
			echo $stmt."\r\n";
			if ($query = $conn->prepare($stmt)){
				if ($query->execute()){
					$conn->commit();
				}else{
					echo $conn->errno;
				}
			}else{
				echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

			$stmt = "insert into labor (shopid,roid,labor,tech,linetotal) values ('$shopid',$roid,'DISCOUNT', 'DISCOUNT, DISCOUNT',$labordiscount)";
			echo $stmt."\r\n";
			if ($query = $conn->prepare($stmt)){
				if ($query->execute()){
					$conn->commit();
				}else{
					echo $conn->errno;
				}
			}else{
				echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}
		}
		echo "success";

		break;

	case "showpartinfo":

		$partid = $_POST['partid'];
		$stmt = "select partnumber,partdesc,partprice,cost,partcode,supplier,partinvoicenumber from parts where shopid = '$shopid' and partid = $partid";
		if ($query = $conn->prepare($stmt)){
			$query->execute();
			$query->bind_result($partnumber,$partdesc,$partprice,$partcost,$partcode,$partsupplier,$partinvoicenumber);
			$query->fetch();
			$query->close();
			echo "<table class='table table-condensed table-striped table-sm'><tr><td>Part Number</td><td>".strtoupper($partnumber)."</td></tr>";
			echo "<tr><td>Part Description</td><td>".strtoupper($partdesc)."</td></tr>";
			echo "<tr><td>Part Price</td><td>".number_format($partprice,2)."</td></tr>";
			echo "<tr><td>Part Cost</td><td>".number_format($partcost,2)."</td></tr>";
			echo "<tr><td>Part Supplier</td><td>".strtoupper($partsupplier)."</td></tr>";
			echo "<tr><td>Part Code</td><td>".strtoupper($partcode)."</td></tr>";
			echo "<tr><td>Part Invoice #</td><td>".strtoupper($partinvoicenumber)."</td></tr></table>";
		}

		break;

	case "mycarfax":

		$user = $_POST['user'];

		// check for an existing record
		$stmt = "select count(*) c from carfaxfeatures where shopid = '$shopid'";
		if ($query = $conn->prepare($stmt)){
			$query->execute();
			$query->bind_result($c);
			$query->fetch();
			$query->close();
		}

		$mycarfaxdate = localTimeStamp($shopid);
		if ($c > 0){
			$stmt = "update carfaxfeatures set mycarfax = 'yes', mycarfaxacceptdate = '$mycarfaxdate', mycarfaxuser = ? where shopid = '$shopid'";
		}else{
			$stmt = "insert into carfaxfeatures (shopid,mycarfax,mycarfaxacceptdate,mycarfaxuser) values ('$shopid','yes','$mycarfaxdate',?)";
		}

		if ($query = $conn->prepare($stmt)){
			$query->bind_param("s",$user);
			$query->execute();
			$conn->commit();
			$query->close();
			echo "success";
		}else{
			echo $conn->error;
		}

		break;

	case "changerodate":

		// check the password
		$empid = $_POST['empid'];
		$pwd = $_POST['pwd'];
		$dbempid = "";

		if (strtolower($empid) !== "demo" && strtolower($empid) !== "admin"){
			$stmt = "select id from employees where id = $empid and shopid = '$shopid' and password = '$pwd'";
			if ($query = $conn->prepare($stmt)){
				$query->execute();
				$query->bind_result($dbempid);
				$query->fetch();
				$query->close();
			}else{
				echo "emp pwd Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

			if ($empid == $dbempid){
				$nd = date("Y-m-d",strtotime($_POST['nd']));
				$stmt = "update repairorders set statusdate = '$nd', finaldate = '$nd' where shopid = '$shopid' and roid = $roid";
				if ($query = $conn->prepare($stmt)){
					$query->execute();
					$conn->commit();
					echo "success";
				}else{
					echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
				}
			}else{
				echo "pwd";
			}
		}
		break;


	case "alertinspection":

		$msgdate = localTimeStamp($shopid);
		$tstmt = "insert into alerts (shopid,startdate,message) values ('$shopid','$msgdate','Inspection updated on RO $roid')";
		if ($tquery = $conn->prepare($tstmt)){
			$tquery->execute();
			$conn->commit();
		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		break;

	case "cardknoxsavesig":
		$imgname = $shopid."_".$roid."_".grs().".png";
		//echo $imgname."\r\n";
		$sigstring = str_replace(" ","+",$_POST['sigstring']);
		//echo $sigstring;

		// save the signature to the sbp/signatures
		if (strlen($sigstring) > 10){
			//echo $sigstring;
			// convert it to an image and trim the white space
			file_put_contents($_SERVER['DOCUMENT_ROOT'].'/sbp/signatures/'.$imgname, base64_decode($sigstring));
			$imgpath = 'convert '.realpath($_SERVER['DOCUMENT_ROOT'].'/sbp/signatures/'.$imgname).' -resize 200% -trim '.realpath($_SERVER['DOCUMENT_ROOT'].'/sbp/signatures/'.$imgname);

			exec($imgpath);

            thicken_image(realpath($_SERVER['DOCUMENT_ROOT'].'/sbp/signatures/'.$imgname), realpath($_SERVER['DOCUMENT_ROOT'].'/sbp/signatures/'.$imgname));
		}
		echo $imgname;
		break;

	case "t60savesig":
		$imgname = $shopid."_".$roid."_".grs().".png";
		//echo $imgname."\r\n";
		$sigstring = str_replace(" ","+",$_POST['sigstring']);
		//echo $sigstring;

		// save the signature to the sbp/signatures
		if (strlen($sigstring) > 10){
			//echo $sigstring;
			// convert it to an image and trim the white space
			file_put_contents($_SERVER['DOCUMENT_ROOT'].'/sbp/signatures/'.$imgname, base64_decode($sigstring));
			$imgpath = 'convert '.realpath($_SERVER['DOCUMENT_ROOT'].'/sbp/signatures/'.$imgname).' -resize 200% -trim '.realpath($_SERVER['DOCUMENT_ROOT'].'/sbp/signatures/'.$imgname);

			exec($imgpath);

            thicken_image(realpath($_SERVER['DOCUMENT_ROOT'].'/sbp/signatures/'.$imgname), realpath($_SERVER['DOCUMENT_ROOT'].'/sbp/signatures/'.$imgname));
		}
		echo $imgname;
		break;


	case "postcc":

		$amt = $_POST['amt'];
		$cardtype = $_POST['cardtype'];
		$refnum = $_POST['refnum'];
		$sigstring = str_replace(" ","+",$_POST['sigstring']);
		$pdate = date("Y-m-d");
		$cid = $_POST['cid'];

		$stmt = "insert into accountpayments (shopid,roid,amt,pdate,ptype,pnumber,cid,api) values ('$shopid',$roid,$amt,'$pdate','$cardtype','$refnum',$cid,'yes')";
		//echo $stmt;
		if ($query = $conn->prepare($stmt)){
			if ($query->execute()){
				$conn->commit();
			}else{
				echo $conn->errno;
			}
		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		//postPaymentToQB("ro",$roid,$amt,$pdate,$cardtype,$refnum,$cid,"","");

		    //Notification

		    $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='74'";
			$query = $conn->prepare($stmt);
			$query->execute();
			$query->store_result();
			$numrows = $query->num_rows();
			if ($numrows > 0)
			{
			$query->bind_result($textcontent,$emailcontent,$popupcontent);
	        $query->fetch();
	        $emailcontent=str_replace("*|AMOUNT|*",$amt,str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent)));
	        $popupcontent=str_replace("*|AMOUNT|*",$amt,str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent)));
			$textcontent=str_replace("*|AMOUNT|*",$amt,str_replace("*|RO|*",$roid,$textcontent));
			$stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'74',?,?,?)";
     		if ($query = $conn->prepare($stmt))
     		{
	 		   $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
			   $query->execute();
			   $conn->commit();
			   $query->close();
			}

		 }

		$ref = "Payment against RO# $roid";
		$stmt  = "INSERT INTO undepositedfunds (";
		$stmt .= "shopid,amount,category,memo,udate,roid";
		$stmt .= ") VALUES (";
		$stmt .= "? ,?, 'Income from work performed',?, ?, ?";
		$stmt .= ")";
		//echo $stmt;

		if ($query = $conn->prepare($stmt)){
			$query->bind_param("ssssi",$shopid,$amt,$ref,$pdate,$roid);
			if ($query->execute()){
				$conn->commit();
				//echo "success";
			}else{
				echo $conn->errno;
			}
		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		$imgname = $shopid."_".$roid."_".grs().".png";

		// save the signature to the sbp/signatures
		if (strlen($sigstring) > 10){
			//echo $sigstring;
			// convert it to an image and trim the white space
			file_put_contents($_SERVER['DOCUMENT_ROOT'].'/sbp/signatures/'.$imgname, base64_decode($sigstring));
			$imgpath = 'convert '.realpath($_SERVER['DOCUMENT_ROOT'].'/sbp/signatures/'.$imgname).' -trim '.realpath($_SERVER['DOCUMENT_ROOT'].'/sbp/signatures/'.$imgname);

			exec($imgpath);
		}
		echo "success";
		break;

	case "delpayment":
		$id = $_POST['id'];

		$stmt = "select amt from accountpayments where id = $id";
		if ($query = $conn->prepare($stmt)){
			$query->execute();
			$query->bind_result($amt);
			$query->fetch();
			$query->close();
		}

		$stmt = "delete from undepositedfunds where shopid = '$shopid' and roid = $roid and amount = $amt";
		if ($query = $conn->prepare($stmt)){
			$query->execute();
			$conn->commit();
		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		$stmt = "delete from accountpayments where shopid = '$shopid' and roid = $roid and id = $id";
		if ($query = $conn->prepare($stmt)){
			$query->execute();
			$conn->commit();
			recordAudit("Delete Payment on RO", "Pymt of ".asDollars($amt)." Deleted on RO#$roid and PymtId$id");

			echo "success";
		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		break;

	case "tp":

		$id = $_POST['id'];
		$val = $_POST['val'];
		$stmt = "update repairorders set $id = ? where shopid = '$shopid' and roid = $roid";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("s",$val);
			$query->execute();
			$conn->commit();
			echo "success";
		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		break;

	case "comments":
	    $ts = localTimeStamp($shopid);
		$comments = filter_var($_POST['c'], FILTER_SANITIZE_STRING).'*||*'.$_COOKIE['username'].' ('.$ts.')';
		$stmt = "update repairorders set comments = ? where shopid = '$shopid' and roid = $roid";
		//echo $stmt;
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("s",$comments);
			$query->execute();
			$conn->commit();
		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}
		echo "success";


		break;

    case "recall":
        $recall_text = filter_var($_POST['c'], FILTER_SANITIZE_STRING);
        $stmt = "update repairorders set recall = ? where shopid = '$shopid' and roid = $roid";
        //echo $stmt;
        if ($query = $conn->prepare($stmt)){
            $query->bind_param("s",$recall_text);
            $query->execute();
            $conn->commit();
        }else{
            echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }
        echo "success";


        break;

	case "sendemail":

	    require(COMPONENTS_PRIVATE_PATH."/shared/securitycheck.php");

		// get the shops email and name
		$stmt = "select companyname,companyemail from company where shopid = '$shopid'";
		if ($query = $conn->prepare($stmt)){
		    $query->execute();
		    $query->store_result();
	    	$query->bind_result($shopname,$shopemail);
	    	$query->fetch();
		    $query->close();
		}else{
			echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		$stmt = "select ccshop from settings where shopid = ?";
		if ($query = $conn->prepare($stmt)){
		    $query->bind_param("s", $shopid);
		    $query->execute();
		    $query->bind_result($ccshop);
		    $query->fetch();
		    $query->close();
		}

		$subj = ucwords($_POST['subj']);
		$email = $sendto = strtolower($_POST['email']);
		$msg = ucfirst($_POST['msg']);
		$subject = $subj;
		$message = $msg;

		if($ccshop=='yes')$email .= ';'.$shopemail;

		$res = sendEmailMandrill($email,$subject,$message,$shopname,$shopemail);

		if(empty($res)) {
		    echo 'Message could not be sent.';
		    echo 'Mailer Error: ' . $mail->ErrorInfo;
		}else{
		    echo 'success';
		    recordAudit("EMail Sent", "Direct Email: An Email Update was sent to $sendto for RO#$roid");
		}

		//Notification
	        $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='41'";
			$query = $conn->prepare($stmt);
			$query->execute();
			$query->store_result();
			$numrows = $query->num_rows();
			if ($numrows > 0)
			{
			$query->bind_result($textcontent,$emailcontent,$popupcontent);
	        $query->fetch();
	        $emailcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent));
	        $popupcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent));
			$textcontent=str_replace("*|RO|*",$roid,$textcontent);
			$stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'41',?,?,?)";
     		if ($query = $conn->prepare($stmt))
     		{
	 		   $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
			   $query->execute();
			   $conn->commit();
			   $query->close();
			}

		 }

		break;

	case "sendsms":

		// get the sending shops sms number
		$sendsmsnum = "";
		$stmt = "select smsnum from smsnumbers where shopid = '$shopid'";
		if ($query = $conn->prepare($stmt)){
			$query->execute();
			$query->bind_result($sendsmsnum);
			$query->fetch();
			$query->close();
		}

		if ($sendsmsnum != ""){

			$token = "t-ewgnokfkia545y4zn4xzxdi";
		    $secret = "zb2lcrqeouxma52dhgjfpqak6w5dg4tfnwvytxy";

		    $from = $sendsmsnum;
			$to = $_POST['cell'];
			//$roid = $_POST['roid'];
			$msg = $mb = "From ".$_POST['shopname']." at $from: ".$_POST['sms'];
			$usr = $_COOKIE['usr'];

		    require(INTEGRATIONS_PATH."/bandwidth/sendsmsv2.php");
	        SendSmsV2($from,$to,$msg);


			$shopname = $_COOKIE['shopname'];
			$ts = localTimeStamp($shopid);

			// add to the sms table
			$stmt = "insert into sms (`from`,name,msg,ts,shopid,markread,roid) values (?,?,?,?,?,'yes',?)";
			if ($query = $conn->prepare($stmt)){
				$query->bind_param("sssssi",$to,$usr,$msg,$ts,$shopid,$roid);
			    $query->execute();
			    $conn->commit();
			    $query->close();
			}else{
				echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

			$commmb = "Sent via Text Message: ".$mb;
			$stmt = "insert into repairordercommhistory (shopid,roid,`datetime`,comm,`by`) values (?,?,?,?,?)";
			if ($query = $conn->prepare($stmt)){
				$query->bind_param("sisss",$shopid,$roid,$ts,$commmb,$usr);
			    $query->execute();
			    $conn->commit();
			    $query->close();
			}else{
				echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

		}else{

			$stmt = "select companyphone from company where shopid = '$shopid'";
			if ($query = $conn->prepare($stmt)){
				$query->execute();
				$query->bind_result($shopphone);
				$query->fetch();
				$query->close();
			}

			$cell = $_POST['cell'];
			$mb = "From ".$_POST['shopname']." at $shopphone: ".$_POST['sms'];
			// Your Account SID and Auth Token from twilio.com/console
			$sid = '**********************************';
			$token = 'f519be94a23d1968e0a1c89ea7030589';
			$client = new Client($sid, $token);

			// Use the client to do fun stuff like send text messages!
			$client->messages->create(
			    // the number you'd like to send the message to
			    '+1'.$cell,
			    array(
			        // A Twilio phone number you purchased at twilio.com/console
			        'from' => '+***********',
			        // the body of the text message you'd like to send
			        'body' => $mb
			    )
			);
			recordAudit("Text Message Sent", "A Text Message ($mb) was sent to $cell for RO#$roid");

			$shopname = $_COOKIE['shopname'];
			$ts = localTimeStamp($shopid);
			$usr = $_COOKIE['usr'];

			// add to the sms table
			$stmt = "insert into sms (`from`,name,msg,ts,shopid,markread,roid) values (?,?,?,?,?,'yes',?)";
			if ($query = $conn->prepare($stmt)){
				$query->bind_param("sssssi",$cell,$usr,$mb,$ts,$shopid,$roid);
			    $query->execute();
			    $conn->commit();
			    $query->close();
			}else{
				echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

			$commmb = "Sent via Text Message: ".$mb;
			$stmt = "insert into repairordercommhistory (shopid,roid,`datetime`,comm,`by`) values (?,?,?,?,?)";
			if ($query = $conn->prepare($stmt)){
				$query->bind_param("sisss",$shopid,$roid,$ts,$commmb,$usr);
			    $query->execute();
			    $conn->commit();
			    $query->close();
			}else{
				echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

		}

		//Notification
	        $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='38'";
			$query = $conn->prepare($stmt);
			$query->execute();
			$query->store_result();
			$numrows = $query->num_rows();
			if ($numrows > 0)
			{
			$query->bind_result($textcontent,$emailcontent,$popupcontent);
	        $query->fetch();
	        $emailcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent));
	        $popupcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent));
			$textcontent=str_replace("*|RO|*",$roid,$textcontent);
			$stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'38',?,?,?)";
     		if ($query = $conn->prepare($stmt))
     		{
	 		   $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
			   $query->execute();
			   $conn->commit();
			   $query->close();
			}

		 }

		echo "success";

		break;

	case "gettime":
		echo localTimeStamp($shopid);
		break;

	case "getdisc":
		$stmt = "select rodisc, warrdisc from repairorders where shopid = '$shopid' and roid = $roid";
		if ($query = $conn->prepare($stmt)){
		    $query->execute();
		    $query->store_result();
	    	$query->bind_result($rodisc,$warrdisc);
	    	$query->fetch();
	    	echo $rodisc."|".$warrdisc;
		    $query->close();
		}else{
			echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		break;

	case "itemstatus":
		$type = $_POST['type'];
		$id = $_POST['id'];
		$status = trim($_POST['status']);
		$dd = date("Y-m-d");

		if ($type == "part"){
			if ($status == ""){
				$stmt = "update parts set pstatus = 'other' where partid = $id and shopid = '$shopid' and roid = $roid";
			}elseif ($status == "other"){
				$stmt = "update parts set pstatus = 'done' where partid = $id and shopid = '$shopid' and roid = $roid";
			}elseif ($status == "done"){
				$stmt = "update parts set pstatus = '' where partid = $id and shopid = '$shopid' and roid = $roid";
			}
		}elseif ($type == "labor"){
			if ($status == ""){
				$stmt = "update labor set lstatus = 'done', datedone = '$dd' where laborid = $id and shopid = '$shopid' and roid = $roid";
			}else{
				$stmt = "update labor set lstatus = '', datedone = '0000-00-00' where laborid = $id and shopid = '$shopid' and roid = $roid";
			}
		}elseif ($type == "sublet"){
			if ($status == ""){
				$stmt = "update sublet set done = 'yes' where subletid = $id and shopid = '$shopid' and roid = $roid";
			}elseif ($status == "yes"){
				$stmt = "update sublet set done = 'no' where subletid = $id and shopid = '$shopid' and roid = $roid";
			}elseif ($status == "no"){
				$stmt = "update sublet set done = '' where subletid = $id and shopid = '$shopid' and roid = $roid";
			}
		}
		//echo $stmt;
		if ($query = $conn->prepare($stmt)){
			if ($query->execute()){
				$conn->commit();
			}else{
				echo $conn->errno;
			}
		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}
		echo "success";
		break;

	case "clearfee":
		$feenum = $_POST['feenumber'];
		$stmt = "update repairorders set userfee".$feenum." = 0, userfee".$feenum."percent = 0 where shopid = '$shopid' and roid = $roid";
		//echo $stmt;
		if ($query = $conn->prepare($stmt)){
			if ($query->execute()){
				$conn->commit();
			}else{
				echo $conn->errno;
			}
		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		$stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='62'";
			$query = $conn->prepare($stmt);
			$query->execute();
			$query->store_result();
			$numrows = $query->num_rows();
			if ($numrows > 0)
			{
			$query->bind_result($textcontent,$emailcontent,$popupcontent);
	        $query->fetch();
	        $emailcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent));
	        $popupcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent));
			$textcontent=str_replace("*|RO|*",$roid,$textcontent);
			$stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'62',?,?,?)";
     		if ($query = $conn->prepare($stmt))
     		{
	 		   $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
			   $query->execute();
			   $conn->commit();
			   $query->close();
			}

		  }

		echo "success";
		break;

	case "closero":

	  include_once "rostatus_check.php";

		$currtimestamp = localTimeStamp($shopid);
		$overridestatusdate = $_POST['overridestatusdate'];
		$statusdate = date("Y-m-d");
		if (strtolower($overridestatusdate) == "yes"){
			$stmt = "update repairorders set status = 'CLOSED' where shopid = '$shopid' and roid = $roid";
		}else{
			$stmt = "update repairorders set updatesent = '$currtimestamp', status = 'CLOSED', statusdate = '$statusdate', finaldate = '$statusdate', dateclosed = '$statusdate' where shopid = '$shopid' and roid = $roid";
		}
		if ($query = $conn->prepare($stmt)){
			if ($query->execute()){
				$conn->commit();
				recordAudit("Closed RO", "Closed RO#$roid");
			}else{
				echo $conn->errno;
			}
		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		$useaccounting = $_COOKIE['useaccounting'];
		if ($useaccounting == "yes"){
			// remove all previous parts expenses, then post new amounts
			$stmt = "delete from unpostedexpenses where shopid = '$shopid' and roid = $roid";
			if ($query = $conn->prepare($stmt)){
				if ($query->execute()){
					$conn->commit();
				}else{
					echo $conn->errno;
				}
			}else{
				echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}


		}

		$stmt = "select `shopid`,`PartID`,`PartNumber`,`PartDesc`,`PartPrice`,`Quantity`,`ROID`,`Supplier`,`Cost`,`PartInvoiceNumber`,`PartCode`,`LineTTLPrice`,`LineTTLCost`,`Date`,`PartCategory`,`complaintid`,`discount`,`net`,`bin`,`tax`,`overridematrix`,`posted`,`scheduledreceivedate`,`ponumber`,`allocated`,`received`,`updated`,`displayorder`,`pstatus`,`deleted`,`datedone`,`salesperson` from parts where deleted = 'no' and shopid = '$shopid' and ROID = '$roid'";
		if ($query = $conn->prepare($stmt)){
		    $query->execute();
		    $result = $query->get_result();
			while ($row = $result->fetch_assoc()){

				// post the parts cost to the unpostedexpenses table
				if ($useaccounting = "yes" && strtolower($row['posted']) <> "yes"){
					$supplier = strtoupper(str_replace("'","''",$row['Supplier']));
					$linettlcost = $row['LineTTLCost'];
					$solddate = date("Y-m-d");
					$partid = $row['PartID'];
					$istmt = "insert into unpostedexpenses (shopid,paidto,amount,category,memo,udate,roid,partid) values ('$shopid','$supplier',$linettlcost,'Cost of Goods Sold',"
					. "'Parts cost for RO# $roid', '$solddate',$roid,$partid)";
					if ($iquery = $conn->prepare($istmt)){
						if ($iquery->execute()){
							$conn->commit();
						}else{
							echo $conn->errno;
						}
					}else{
						echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
					}
				}

				// update inventory counts
				/*$soldqty = $row['Quantity'];
				$partnumber = $row['PartNumber'];
				$ustmt = "update partsinventory set onhand = onhand - $soldqty, allocatted = allocatted - $soldqty where shopid = '$shopid' and PartNumber = '$partnumber'";
				if ($uquery = $conn->prepare($ustmt)){
					if ($uquery->execute()){
						$conn->commit();
					}else{
						echo $conn->errno;
					}
				}else{
					echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
				}*/

			}
		}

		// check for podium
		$podiumcount = 0;
		$stmt = "select count(*) c from apilogin where companyname = 'podium' and shopid = '$shopid'";
		if ($query = $conn->prepare($stmt)){

			$query->execute();
			$query->bind_result($podiumcount);
			$query->fetch();
			$query->close();


		}

		$stmt = "select apikey from apilogin where shopid = '$shopid' and companyname = 'broadly'";
		if ($query = $conn->prepare($stmt)){
				$query->execute();
				$query->bind_result($broadlyapikey);
				$query->fetch();
				$query->close();
		}
		else
		$broadlyapikey='';

	  $stmt = "select apikey from apilogin where shopid = '$shopid' and companyname = 'repairshopmarketing'";
		if ($query = $conn->prepare($stmt)){
				$query->execute();
				$query->bind_result($repairshopmarketing);
				$query->fetch();
				$query->close();
		}
		else
		$repairshopmarketing='';

	  if(!empty($repairshopmarketing))
	  {
	  	$url = "https://carglyplatform.appspot.com/partners/api/shopboss-callback?shopid=".$shopid."&roid=".$roid;
      $ch = curl_init();
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
      curl_setopt($ch, CURLOPT_URL,$url);
      $result = curl_exec( $ch );
      curl_close( $ch );
	  }

	  $birdapikey = $birdbusid = '';

	  $stmt = "select apikey,username from apilogin where shopid = '$shopid' and companyname = 'birdeye'";
		if ($query = $conn->prepare($stmt)){
				$query->execute();
				$query->bind_result($birdapikey,$birdbusid);
				$query->fetch();
				$query->close();
		}


		$stmt = "select customerid,customerlast,customerfirst,customeraddress,customercity,customerstate,customerzip,customerphone,customerwork,cellphone,email,vehid,vehyear,vehmake,vehmodel,vin,vehlicense,vehengine,milesout,customvehicle1,customvehicle1label,customvehicle2,customvehicle2label,customvehicle3,customvehicle3label,customvehicle4,customvehicle4label,customvehicle5,customvehicle5label,customvehicle6,customvehicle6label,customvehicle7,customvehicle7label,customvehicle8,customvehicle8label,totalro from repairorders where shopid = '$shopid' and roid = $roid";
 		if ($query = $conn->prepare($stmt))
 		{
 			$query->execute();
			$query->bind_result($customerid,$customerlast,$customerfirst,$customeraddress,$customercity,$customerstate,$customerzip,$homephone,$workphone,$cellphone,$email,$vehid,$vehyear,$vehmake,$vehmodel,$vin,$vehlicense,$vehengine,$milesout,$customvehicle1,$customvehicle1label,$customvehicle2,$customvehicle2label,$customvehicle3,$customvehicle3label,$customvehicle4,$customvehicle4label,$customvehicle5,$customvehicle5label,$customvehicle6,$customvehicle6label,$customvehicle7,$customvehicle7label,$customvehicle8,$customvehicle8label,$totalro);
 			$query->fetch();
 			$query->close();
 		}

		$stmt = "select follow from customer where shopid = '$shopid' and customerid = $customerid";
		if ($query = $conn->prepare($stmt)){

			$query->execute();
			$query->bind_result($follow);
			$query->fetch();
			$query->close();

		}



		if ($broadlyapikey!='' && strtolower($follow) == "yes")
		{
			$json = array();
			$phonetouse = "";
			if (strlen($cellphone) > 0){
				$phonetouse = $cellphone;
			}else{
				$phonetouse = "";
				if (strlen($homephone) > 0){
					$phonetouse = $homephone;
				}else{
					$phonetouse = "";
					if (strlen($workphone) > 0){
						$phonetouse = $workphone;
					}else{
						$phonetouse = "";
					}
				}
 			}

 			$pro = array();
 			$pcount=1;

 			$stmt = "select distinct tech from labor where shopid = '$shopid' and ROID = '$roid'";
		    if ($query = $conn->prepare($stmt))
		   {
		    $query->execute();
		    $result = $query->get_result();
			while ($row = $result->fetch_assoc())
			{
			$tarr=explode(',',$row['tech']);
			$pro[]=array('id'=>strval($pcount++),'name'=>trim($tarr[1]).' '.trim($tarr[0]));
		    }
	       }

 			$customer=array('id'=>strval($customerid),"name"=>$customerfirst.' '.$customerlast);
            if(!empty($email))$customer["email"]=$email;
            if(!empty($phonetouse))$customer["phone"]=$phonetouse;
            if(!empty($pro))$json['providers']=$pro;

			$json['locationID'] = strval($shopid);
			$json['transactions']=array(array('id'=>strval($roid),"timestamp"=>date("c", time()),'amount'=>strval($totalro),"customer"=>$customer));

			$json = json_encode($json);

			$curl = curl_init();
			$base = "https://connect-api.broadly.com/partner";
			curl_setopt_array($curl, array(
			    CURLOPT_URL            => $base,
			    CURLOPT_POST           => 1,
			    CURLOPT_CUSTOMREQUEST  => "POST",
			    CURLOPT_POSTFIELDS     => $json,
			    CURLOPT_RETURNTRANSFER => 1,
			    CURLOPT_SSL_VERIFYHOST => 0, // to avoid SSL issues if you need to fetch from https
			    CURLOPT_SSL_VERIFYPEER => 0, // same ^
			));
			curl_setopt($curl, CURLOPT_HTTPHEADER, array(
			    'Content-Type: application/json',
			    'Accept: application/json',
			    'x-api-key: '.$broadlyapikey
			));

			$jsonr = curl_exec($curl);


		}


		if ($birdapikey!='' && $email!='' && strtolower($follow) == "yes")
		{
			$json = array();
			$phonetouse = "";
			if (strlen($cellphone) > 0){
				$phonetouse = $cellphone;
			}else{
				$phonetouse = "";
				if (strlen($homephone) > 0){
					$phonetouse = $homephone;
				}else{
					$phonetouse = "";
					if (strlen($workphone) > 0){
						$phonetouse = $workphone;
					}else{
						$phonetouse = "";
					}
				}
 			}

 			$json['name'] = $customerfirst.' '.$customerlast;
 			$json['phone'] = $phonetouse;
 			$json['emailId'] = $email;
 			$json['smsEnabled'] = 1;

 			$stmt = "select distinct tech from labor where shopid = '$shopid' and ROID = '$roid'";
		    if ($query = $conn->prepare($stmt))
		   {
		    $query->execute();
		    $result = $query->get_result();
		    $inarr = array();
			while ($row = $result->fetch_assoc())
			{
			$tarr=explode(',',$row['tech']);
			$inarr[]=array('firstName'=>trim($tarr[1]),'lastName'=>trim($tarr[0]));
		    }
		    if(!empty($inarr))$json['employees']=$inarr;
	       }

 			$json = json_encode($json);

			$curl = curl_init();
			$base = "https://api.birdeye.com/resources/v1/customer/checkin?bid=$birdbusid&api_key=".$birdapikey;
			curl_setopt_array($curl, array(
			    CURLOPT_URL            => $base,
			    CURLOPT_POST           => 1,
			    CURLOPT_CUSTOMREQUEST  => "POST",
			    CURLOPT_POSTFIELDS     => $json,
			    CURLOPT_RETURNTRANSFER => 1,
			    CURLOPT_SSL_VERIFYHOST => 0, // to avoid SSL issues if you need to fetch from https
			    CURLOPT_SSL_VERIFYPEER => 0, // same ^
			));
			curl_setopt($curl, CURLOPT_HTTPHEADER, array(
			    'Content-Type: application/json',
			    'Accept: application/json'
			));

			$jsonr = curl_exec($curl);


		}


		if ($podiumcount > 0 && strtolower($follow) == "yes"){

			$stmt = "select apikey,username from apilogin where shopid = '$shopid' and companyname = 'podium'";
			if ($query = $conn->prepare($stmt)){
				$query->execute();
				$query->bind_result($locationid,$token);
				$query->fetch();
				$query->close();
			}

			$token = '"'.$token.'"';

			$stmt = "select companyemail from company where shopid = '$shopid'";
			if ($query = $conn->prepare($stmt)){
				$query->execute();
				$query->bind_result($shopemail);
				$query->fetch();
				$query->close();
			}

			$phonetouse = "";
			if (strlen($cellphone) > 0){
				$phonetouse = $cellphone;
			}else{
				$phonetouse = "";
				if (strlen($homephone) > 0){
					$phonetouse = $homephone;
				}else{
					$phonetouse = "";
					if (strlen($workphone) > 0){
						$phonetouse = $workphone;
					}else{
						$phonetouse = "";
					}
				}
			}


			$json = array();
			$json['locationId'] = $locationid;
			$json['phoneNumber'] = $phonetouse;
			$json['email'] = $email;
			$json['firstName'] = $customerfirst;
			$json['lastName'] = $customerlast;
			$json['senderEmail'] = $shopemail;
			$json['integrationName'] = "Shop Boss";
			//$json['test'] = "true";

			$json = json_encode($json);

			$curl = curl_init();
			$base = "https://platform.podium.com/api/v2/review_invitations";
			curl_setopt_array($curl, array(
			    CURLOPT_URL            => $base,
			    CURLOPT_POST           => 1,
			    CURLOPT_CUSTOMREQUEST  => "POST",
			    CURLOPT_POSTFIELDS     => $json,
			    CURLOPT_RETURNTRANSFER => 1,
			    CURLOPT_SSL_VERIFYHOST => 0, // to avoid SSL issues if you need to fetch from https
			    CURLOPT_SSL_VERIFYPEER => 0, // same ^
			));
			curl_setopt($curl, CURLOPT_HTTPHEADER, array(
			    'Content-Type: application/json',
			    'Accept: application/json',
			    'Authorization: '.$token
			));

			$jsonr = json_decode(curl_exec($curl));

		}

		// test for AutoPilothq.com integration
		$stmt = "select apikey from apilogin where shopid = '$shopid' and companyname = 'autopilot'";
		if ($query = $conn->prepare($stmt)){

			$query->execute();
			$query->bind_result($apikey);
			$query->fetch();
			$query->close();

		}else{
			$apikey = "";
		}

		if ($apikey != ""){


			$stmt = "select userdefined1,userdefined2,userdefined3 from customer where customerid = $customerid and shopid = '$shopid'";
			if ($query = $conn->prepare($stmt)){

				$query->execute();
				$query->bind_result($userdefined1,$userdefined2,$userdefined3);
				$query->fetch();
				$query->close();

			}

			$stmt = "select customuserfield1,customuserfield2,customuserfield3 from company where shopid = '$shopid'";
			//echo $stmt;
			if ($query = $conn->prepare($stmt)){

				$query->execute();
				$query->bind_result($customuserfield1,$customuserfield2,$customuserfield3);
				$query->fetch();
				$query->close();

			}
            $headers = array();
			$endpoint = "https://api2.autopilothq.com/v1/trigger/0002/contact";
			$headers[] = "autopilotapikey: ".$apikey;
			$headers[] = "Content-Type: application/json";

			$vehstr = "";

			if (strlen($customvehicle1label) > 0){
				$vehstr .= '"string--' . str_replace(" ","--",str_replace("\t","",$customvehicle1label)) . '": "' . str_replace("\t","",$customvehicle1) . '",';
			}
			if (strlen($customvehicle2label) > 0){
				$vehstr .= '"string--' . str_replace(" ","--",str_replace("\t","",$customvehicle2label)) . '": "' . str_replace("\t","",$customvehicle2) . '",';
			}
			if (strlen($customvehicle3label) > 0){
				$vehstr .= '"string--' . str_replace(" ","--",str_replace("\t","",$customvehicle3label)) . '": "' . str_replace("\t","",$customvehicle3) . '",';
			}
			if (strlen($customvehicle4label) > 0){
				$vehstr .= '"string--' . str_replace(" ","--",str_replace("\t","",$customvehicle4label)) . '": "' . str_replace("\t","",$customvehicle4) . '",';
			}
			if (strlen($customvehicle5label) > 0){
				$vehstr .= '"string--' . str_replace(" ","--",str_replace("\t","",$customvehicle5label)) . '": "' . str_replace("\t","",$customvehicle5) . '",';
			}
			if (strlen($customvehicle6label) > 0){
				$vehstr .= '"string--' . str_replace(" ","--",str_replace("\t","",$customvehicle6label)) . '": "' . str_replace("\t","",$customvehicle6) . '",';
			}
			if (strlen($customvehicle7label) > 0){
				$vehstr .= '"string--' . str_replace(" ","--",str_replace("\t","",$customvehicle7label)) . '": "' . str_replace("\t","",$customvehicle7) . '",';
			}
			if (strlen($customvehicle8label) > 0){
				$vehstr .= '"string--' . str_replace(" ","--",str_replace("\t","",$customvehicle8label)) . '": "' . str_replace("\t","",$customvehicle8) . '",';
			}
			if (strlen($customuserfield1) > 0){
				$vehstr .= '"string--' . str_replace(" ","--",str_replace("\t","",$customuserfield1)) . '": "' . str_replace("\t","",$userdefined1) . '",';
			}
			if (strlen($customuserfield2) > 0){
				$vehstr .= '"string--' . str_replace(" ","--",str_replace("\t","",$customuserfield2)) . '": "' . str_replace("\t","",$userdefined2) . '",';
			}
			if (strlen($customuserfield3) > 0){
				$vehstr .= '"string--' . str_replace(" ","--",str_replace("\t","",$customuserfield3)) . '": "' . str_replace("\t","",$userdefined3) . '",';
			}
			//echo $vehstr."\r\n";
			if (substr($vehstr,-1) == ","){
				$vehstr = ",".substr($vehstr,0,strlen($vehstr)-1);
			}

			$params = '{
				"contact": {
					"FirstName": "'.str_replace("\t","",$customerfirst).'",
					"LastName": "'.str_replace("\t","",$customerlast).'",
					"Email": "'.str_replace("\t","",$email).'",
					"MailingStreet": "'.str_replace("\t","",$customeraddress).'",
					"MailingCity": "'.str_replace("\t","",$customercity).'",
					"MailingState": "'.str_replace("\t","",$customerstate).'",
					"MailingPostalCode": "'.str_replace("\t","",$customerzip).'",
					"Phone": "'.str_replace("\t","",$homephone).'",
					"MobilePhone": "'.str_replace("\t","",$cellphone).'",
				    "custom": {
				    	"string--Year": "'.str_replace("\t","",$vehyear).'",
				    	"string--Make": "'.str_replace("\t","",$vehmake).'",
				    	"string--Model": "'.str_replace("\t","",$vehmodel).'",
				    	"string--VIN": "'.str_replace("\t","",$vin).'",
				    	"string--License": "'.str_replace("\t","",$vehlicense).'",
				    	"string--Engine": "'.str_replace("\t","",$vehengine).'",
				    	"string--Miles--Out": "'.str_replace("\t","",$milesout).'"
				    	'.$vehstr.'
				    }
    			}
			}';


			//echo $params."\r\n";
			$curl = curl_init();
			curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
			curl_setopt_array($curl, array(
			    CURLOPT_URL            => $endpoint,
			    CURLOPT_POST           => 1,
			    CURLOPT_POSTFIELDS     => $params,
			    CURLOPT_RETURNTRANSFER => 1,
			    CURLOPT_SSL_VERIFYHOST => 0,
			    CURLOPT_SSL_VERIFYPEER => 0
			));

			$result = curl_exec($curl);
			//echo $result."\r\n";
		}

		// test for referralrock.com integration
		$apikey = "";
		$stmt = "select apikey from apilogin where shopid = '$shopid' and companyname = 'referralrock'";
		if ($query = $conn->prepare($stmt)){

			$query->execute();
			$query->bind_result($apikey);
			$query->fetch();
			$query->close();

		}else{
			$apikey = "";
		}

		if ($apikey != ""){

			$totalro = number_format($totalro,2,".","");
			$headers = array();
			$endpoint = "https://api.referralrock.com/api/referral/update";
			$headers[] = "Authorization: Basic ".$apikey;
			$headers[] = "Content-Type: application/json";

			//print_r($headers);

			$json = '[{
			    "query": {
			      "fuzzyInfo": {
			        "Identifier": "'.$email.'"
			      }
			    },
			    "referral": {
			      "firstName": "'.$customerfirst.'",
			      "lastName": "'.$customerlast.'",
			      "email": "'.$email.'",
			      "phoneNumber": "'.$cellphone.'",
			      "externalIdentifier": "'.$customerid.'",
			      "amount": '.$totalro.',
			      "status": "approved"
			    }
			  }]';

			//echo $json."\r\n";
			$curl = curl_init();
			curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
			curl_setopt_array($curl, array(
			    CURLOPT_URL            => $endpoint,
			    CURLOPT_POST           => 1,
			    CURLOPT_POSTFIELDS     => $json,
			    CURLOPT_RETURNTRANSFER => 1,
			    CURLOPT_SSL_VERIFYHOST => 0,
			    CURLOPT_SSL_VERIFYPEER => 0
			));

			$result = curl_exec($curl);
			//echo $result;
		}


		// apination
		$apikey = "";
		$stmt = "select `key` from apinationkeys where shopid = '$shopid'";
		if ($query = $conn->prepare($stmt)){
			$query->execute();
			$query->bind_result($apikey);
			$query->fetch();
			$query->close();
		}

		if ($apikey != ""){
				require(INTEGRATIONS_PATH."/apination/closerowebhook.php");
				APINationRO($roid);
		}

		// backoffice
		$apikey = "";
		$stmt = "select `locid` from backofficekeys where shopid = '$shopid' and active='yes'";
		if ($query = $conn->prepare($stmt)){
			$query->execute();
			$query->bind_result($apikey);
			$query->fetch();
			$query->close();
		}

		if ($apikey != ""){
				require(INTEGRATIONS_PATH."/backoffice/closerowebhook.php");
				BackofficeRO($roid);
		}


		$stmt = "select asid from autoserveshop where shopid = ?";
	    if ($query = $conn->prepare($stmt))
	    {
		$query->bind_param("s",$shopid);
		$query->execute();
		$query->bind_result($asid);
		$query->fetch();
		$query->close();
	    }

	    if(!empty($asid))
	    {
	    	$stmt = "select asid,ordernumber from autoserveinspections where shopid = ? and roid = ?";
			if ($query = $conn->prepare($stmt))
			{
			  $query->bind_param("si",$shopid,$roid);
			  $query->execute();
			  $r = $query->get_result();
			  while ($rs = $r->fetch_assoc())
			  {
			  	$inspOrderArray = array();
                $inspOrderArray['inspectionOrder']['_id'] = $rs['asid'];
                $inspOrderArray['inspectionOrder']['orderNumber'] = $rs['ordernumber'];
	            $inspOrderArray['inspectionOrder']['storeId'] = $asid;					// from autoserveshop table
	            $inspOrderArray['inspectionOrder']['customer']['uid'] = $customerid;
	            $inspOrderArray['inspectionOrder']['customer']['email'] = $email;
	            $inspOrderArray['inspectionOrder']['customer']['firstName'] = $customerfirst;
	            $inspOrderArray['inspectionOrder']['customer']['lastName'] = $customerlast;
	            $inspOrderArray['inspectionOrder']['customer']['homePhone'] = $homephone;
	            $inspOrderArray['inspectionOrder']['customer']['workPhone'] = $workphone;
	            $inspOrderArray['inspectionOrder']['customer']['mobilePhone'] = $cellphone;
	            $inspOrderArray['inspectionOrder']['vehicle']['uid'] = $vehid;
	            $inspOrderArray['inspectionOrder']['vehicle']['licensePlate'] = $vehlicense;
	            $inspOrderArray['inspectionOrder']['vehicle']['make'] = $vehmake;
	            $inspOrderArray['inspectionOrder']['vehicle']['year'] = $vehyear;
	            $inspOrderArray['inspectionOrder']['vehicle']['model'] = $vehmodel;
	            $inspOrderArray['inspectionOrder']['vehicle']['vinNumber'] = $vin;
	            $inspOrderArray['inspectionOrder']['closed'] = true;

	            $json = json_encode($inspOrderArray);
            	$url = "https://app.autoserve1.com//v1/store/$asid/inspectionOrder/";

	            $ch = curl_init();
	            curl_setopt($ch, CURLOPT_URL, $url);
	            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	            curl_setopt($ch, CURLOPT_POST, true);
	            curl_setopt($ch,CURLOPT_POSTFIELDS, $json);
	            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
	            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);

	            $headers = array();
	            $headers[] = 'Content-Type: application/json';
	            $headers[] = 'Authorization: J6G3egdSGXfRSeLAD';
	            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

	            $result = curl_exec($ch);
              	curl_close($ch);

			  }
			}
	    }

		 //notification

		 if($_POST['balance']>0)
		 {
		    $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='32'";
			$query = $conn->prepare($stmt);
			$query->execute();
			$query->store_result();
			$numrows = $query->num_rows();
			if ($numrows > 0)
			{
			$query->bind_result($textcontent,$emailcontent,$popupcontent);
	        $query->fetch();
	        $emailcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent));
	        $popupcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent));
			$textcontent=str_replace("*|RO|*",$roid,$textcontent);
			$stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'32',?,?,?)";
     		if ($query = $conn->prepare($stmt))
     		{
	 		   $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
			   $query->execute();
			   $conn->commit();
			   $query->close();
			}

		    }
		 }

		 if($totalro==0)
		 {
		    $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='169'";
			$query = $conn->prepare($stmt);
			$query->execute();
			$query->store_result();
			$numrows = $query->num_rows();
			if ($numrows > 0)
			{
			$query->bind_result($textcontent,$emailcontent,$popupcontent);
	        $query->fetch();
	        $emailcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent));
	        $popupcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent));
			$textcontent=str_replace("*|RO|*",$roid,$textcontent);
			$stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'169',?,?,?)";
     		if ($query = $conn->prepare($stmt))
     		{
	 		   $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
			   $query->execute();
			   $conn->commit();
			   $query->close();
			  }
		  }
		 }

		$stmt = "delete from shop_data where shopid='$shopid' and roid='$roid'";
		if ($query = $conn->prepare($stmt))
    {
        $query->execute();
        $conn->commit();
        $query->close();
    }

		$stmt = "Insert into shop_data
    select
    null as 'id',
    r.shopid,
    r.roid,
    r.statusdate,
    status,
    rotype,
    totalRO total_ro,
    Subtotal subtotal,
    salestax tax,
    totalfees fees,
    totallbrhrs labor_hours,
    PartsCost parts_cost,
    (SELECT SUM(l.LaborHours*e.hourlyrate)
    FROM labor l,employees e
    WHERE l.shopid=e.shopid
    and l.tech=CONCAT(e.employeelast,', ',employeefirst)
    AND l.deleted = 'no'
    AND l.tech != 'DISCOUNT, DISCOUNT'
    AND l.shopid=r.shopid
    AND l.roid=r.roid
    AND e.active='yes') labor_cost,
    sublet_cost_ro(shopid,roid) sublet_cost,
    totalprts parts_price,
    totallbr labor_price,
    totalsublet sublet_price,
    (SELECT coalesce(SUM(if(tech = 'DISCOUNT, DISCOUNT',abs(linetotal),discount)),0) as tlabordiscount FROM labor WHERE shopid = r.shopid AND roid in (r.roid)) labor_discount,
    (SELECT coalesce(SUM(if(partnumber = 'DISCOUNT',abs(linettlprice),((partprice*quantity)-linettlprice))),0) as tpartsdiscount FROM parts WHERE shopid = r.shopid AND roid in (r.roid) and (partnumber='Discount' || discount!=0)) parts_discount
    from
    repairorders r
    where r.shopid = '$shopid'
    and r.roid = '$roid'";

    if ($query = $conn->prepare($stmt))
    {
        $query->execute();
        $conn->commit();
        $query->close();
    }

		echo "success";
		break;

	case "ponumber":
		$ponumber = $_POST['ponumber'];
		$stmt = "update repairorders set ponumber = ? where shopid = ? and roid = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("ssi",$ponumber,$shopid,$roid);
			if ($query->execute()){
				$conn->commit();
				echo "success";
			}else{
				echo $conn->errno;
			}

		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}
		break;

	case "addpmt":
		//t=addpmt&shopid= echo $shopid; &roid= echo $roid; &amt="+amt+"&ptype="+ptype+"&ref="+ref+"&pdate="+pdate
		$amt = filter_var($_POST['amt'],FILTER_SANITIZE_NUMBER_FLOAT,FILTER_FLAG_ALLOW_FRACTION);
		$ptype = $_POST['ptype'];
		$refnum = $_POST['ref'];
		$pdate = $_POST['pdate'];
		$pdate = new DateTime($pdate);
		$pdate = date_format($pdate,"Y-m-d");
		$cid = $_POST['cid'];

		$stmt = "insert into accountpayments (shopid,roid,amt,pdate,pnumber,cid,ptype,api) values (?,?,?,?,?,?,?,'yes')";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sidssis",$shopid,$roid,$amt,$pdate,$refnum,$cid,$ptype);
			if ($query->execute()){
				$conn->commit();
				echo "success";
				recordAudit("Payment Posted", "Payment of $amt posted to RO #$roid");
			}else{
				echo $conn->errno;
			}

		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		//postPaymentToQB("ro",$roid,$amt,$pdate,$ptype,$refnum,$cid,"","");

		$ref = "Payment against RO# $roid";
		$stmt  = "INSERT INTO undepositedfunds (";
		$stmt .= "shopid,amount,category,memo,udate,roid";
		$stmt .= ") VALUES (";
		$stmt .= "? ,?, 'Income from work performed',?, ?, ?";
		$stmt .= ")";
		//echo $stmt;

		if ($query = $conn->prepare($stmt)){
			$query->bind_param("ssssi",$shopid,$amt,$ref,$pdate,$roid);
			if ($query->execute()){
				$conn->commit();
				//echo "success";
			}else{
				echo $conn->errno;
			}
		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

         //Notification

		    $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='74'";
			$query = $conn->prepare($stmt);
			$query->execute();
			$query->store_result();
			$numrows = $query->num_rows();
			if ($numrows > 0)
			{
			$query->bind_result($textcontent,$emailcontent,$popupcontent);
	        $query->fetch();
	        $emailcontent=str_replace("*|AMOUNT|*",$amt,str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent)));
	        $popupcontent=str_replace("*|AMOUNT|*",$amt,str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent)));
			$textcontent=str_replace("*|AMOUNT|*",$amt,str_replace("*|RO|*",$roid,$textcontent));
			$stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'74',?,?,?)";
     		if ($query = $conn->prepare($stmt))
     		{
	 		   $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
			   $query->execute();
			   $conn->commit();
			   $query->close();
			}

		 }

		 if($ptype=='Cash')
         {
		    $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='35'";
			$query = $conn->prepare($stmt);
			$query->execute();
			$query->store_result();
			$numrows = $query->num_rows();
			if ($numrows > 0)
			{
			$query->bind_result($textcontent,$emailcontent,$popupcontent);
	        $query->fetch();
	        $emailcontent=str_replace("*|AMOUNT|*",$amt,str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent)));
	        $popupcontent=str_replace("*|AMOUNT|*",$amt,str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent)));
			$textcontent=str_replace("*|AMOUNT|*",$amt,str_replace("*|RO|*",$roid,$textcontent));
			$stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'35',?,?,?)";
     		if ($query = $conn->prepare($stmt))
     		{
	 		   $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
			   $query->execute();
			   $conn->commit();
			   $query->close();
			}

		 }

		 }

		break;

	case "status":

	  include_once "rostatus_check.php";

		$status = $_POST['s'];
		if (strtolower($status) == "final"){
			$sdate = date("Y-m-d");
			$stmt = "update repairorders set status = ?, finaldate = '$sdate', datefinal = '$sdate', statusdate = '$sdate' where shopid = ? and roid = ?";
		}else{
			$stmt = "update repairorders set status = ? where shopid = ? and roid = ?";
		}
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("ssi",$status,$shopid,$roid);
			if ($query->execute()){
				$conn->commit();
				echo "success";
				recordAudit("ROStatusChange", "Status changed to $status on RO# $roid");
			}else{
				echo $conn->errno;
			}

		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		// ********** test for autotext.me ********************
		$apikey = "";
		$stmt = "select apikey from apilogin where shopid = ? and companyname = 'autotext'";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("s",$shopid);
			$query->execute();
			$query->bind_result($apikey);
			$query->fetch();
			$query->close();

		}else{
			$apikey = "";
		}

		if ($apikey != ""){

			// get the data from the RO
			$stmt = "select customerid,customerlast,customerfirst,customeraddress,customercity,"
			. "customerstate,customerzip,customerphone,customerwork,cellphone,vehid,vehyear,vehmake,"
			. "vehmodel,vin,vehlicense,status,totalro,taxrate,labortaxrate,sublettaxrate,"
			. "writer,vehiclemiles,vehengine,email from repairorders where shopid = ? and roid = ?";
			if ($query = $conn->prepare($stmt)){
				$query->bind_param("si",$shopid,$roid);
				$query->execute();
				$query->bind_result($cid,$customerlast,$customerfirst,$CustomerAddress,$customercity,$customerstate,$customerzip,$CustomerPhone,$CustomerWork,$CellPhone,$vid,$vehyear,$vehmake,$vehmodel,$Vin,$vehlicense,$status,$totalro,$taxrate,$labortaxrate,$sublettaxrate,$defaultwriter,$currmileage,$VehEngine,$email);
				$query->fetch();
				$query->close();
			}

			if (is_numeric(substr($status,0,1))){
				$status = substr($status,1,strlen($status)-1);
			}

			// now get the concerns
			$runconcerns = "";
			$cstmt = "select complaint from complaints where shopid = ? and roid = ? order by displayorder";
			if ($cquery = $conn->prepare($cstmt)){
				$cquery->bind_param("si",$shopid,$roid);
				$cquery->execute();
				$cr = $cquery->get_result();
				while ($crs = $cr->fetch_assoc()){
					$runconcerns .= $crs['complaint'].", ";
				}
			}

			// post to the endpoint
			$ar = array();
			$ar['shopid'] = $shopid;
			$ar['key'] = $apikey;
			$ar['ronumber'] = $roid;
			$ar['customerid'] = $cid;
			$ar['lastname'] = strtoupper($customerlast);
			$ar['firstname'] = strtoupper($customerfirst);
			$ar['address'] = strtoupper($CustomerAddress);
			$ar['city'] = strtoupper($customercity);
			$ar['state'] = strtoupper($customerstate);
			$ar['zip'] = strtoupper($customerzip);
			$ar['homephone'] = $CustomerPhone;
			$ar['workphone'] = $CustomerWork;
			$ar['cellphone'] = $CellPhone;
			$ar['vehid'] = $vid;
			$ar['year'] = strtoupper($vehyear);
			$ar['make'] = strtoupper($vehmake);
			$ar['model'] = strtoupper($vehmodel);
			$ar['vin'] = strtoupper($Vin);
			$ar['license'] = strtoupper($vehlicense);
			$ar['status'] = strtoupper($status);
			$ar['totalro'] = $totalro;
			$ar['partstaxrate'] = $taxrate;
			$ar['labortaxrate'] = $labortaxrate;
			$ar['sublettaxrate'] = $sublettaxrate;
			$ar['writer'] = strtoupper($defaultwriter);
			$ar['milesin'] = $currmileage;
			$ar['vehengine'] = strtoupper($VehEngine);
			$ar['email'] = strtoupper($email);
			$ar['customerConcerns'] = strtoupper($runconcerns);

			$json = json_encode($ar);
			$json = str_replace("\n","",str_replace("\r","",$json));

			$json = json_encode($ar);
			$json = str_replace("\n","",str_replace("\r","",$json));
			//echo $json;

			$ch = curl_init();

			curl_setopt($ch, CURLOPT_URL, "https://shopboss.autotext.me/Admin/Shopboss/webhook.php");
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
			curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
			curl_setopt($ch, CURLOPT_POST, 1);

			$headers = array("Content-Type: application/json");
			curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

			$json_result = curl_exec($ch);
			//echo $json_result;
			curl_close ($ch);
		}

		//Notification
	        $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='44'";
			$query = $conn->prepare($stmt);
			$query->execute();
			$query->store_result();
			$numrows = $query->num_rows();
			if ($numrows > 0)
			{
			$query->bind_result($textcontent,$emailcontent,$popupcontent);
	        $query->fetch();
	        if (is_numeric(substr($status,0,1)))
			$status = substr($status,1,strlen($status)-1);
	        $emailcontent=str_replace("*|STATUS|*",$status,str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent)));
	        $popupcontent=str_replace("*|STATUS|*",$status,str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent)));
			$textcontent=str_replace("*|STATUS|*",$status,str_replace("*|RO|*",$roid,$textcontent));
			$stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'44',?,?,?)";
     		if ($query = $conn->prepare($stmt))
     		{
	 		   $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
			   $query->execute();
			   $conn->commit();
			   $query->close();
			}

		 }


		break;

	case "rotype":

		$rotype = $_POST['r'];

		$stmt = "select rotype from repairorders where shopid = ? and roid= ?";
		if ($query = $conn->prepare($stmt))
		{
			$query->bind_param("ss",$shopid,$roid);
		    $query->execute();
		    $query->store_result();
	    	$query->bind_result($oldrotype);
		    $query->fetch();
		    $query->close();
		}

		$stmt = "update repairorders set rotype = ? where shopid = ? and roid = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("ssi",$rotype,$shopid,$roid);
			if ($query->execute()){
				$conn->commit();
				echo "success";
			}else{
				echo $conn->errno;
			}

		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		$stmt = "select upper(updateinvonadd) from company where shopid = ?";
        if ($query = $conn->prepare($stmt)){
	    $query->bind_param("s",$shopid);
        $query->execute();
        $query->bind_result($updateinvonadd);
        $query->fetch();
        $query->close();
        }else{
	    echo "RO Type failed: (" . $conn->errno . ") " . $conn->error;
        }

		if(strtolower($oldrotype) == "no approval" && strtolower($rotype) != "no approval" && $updateinvonadd=='YES')
		{
			$stmt = "select partnumber,quantity from parts where shopid = ? and roid = ?";
			if ($query = $conn->prepare($stmt)){

				$query->bind_param("si",$shopid,$roid);
				$query->execute();
				$r = $query->get_result();
				while ($rs = $r->fetch_assoc()){

					$qty = $rs['quantity'];
					$pn = $rs['partnumber'];
					$upstmt = "update partsinventory set onhand = onhand - $qty, "
					. "netonhand = netonhand - $qty where shopid = ? and partnumber = ?";
					if ($upquery = $conn->prepare($upstmt)){

						$upquery->bind_param("ss",$oshopid,$pn);
						$upquery->execute();
						$conn->commit();
						$upquery->close();

					}

				}
				$query->close();
			}

		}

		// if it is a "No Approval" type, update all inventory parts
		if (strtolower($rotype) == "no approval"){

            recordAudit("ROTypeNoApproval", "RO Type set to No Approval to 'No Approval' on RO# $roid");

			$stmt = "delete from kanbandata where shopid = ? and roid = ?";
			if ($query = $conn->prepare($stmt)){
				$query->bind_param("si",$shopid,$roid);
				$query->execute();
				$conn->commit();
				$query->close();
			}

			if($updateinvonadd=='YES')
            {
			$stmt = "select partnumber,quantity from parts where shopid = ? and roid = ?";
			if ($query = $conn->prepare($stmt)){

				$query->bind_param("si",$shopid,$roid);
				$query->execute();
				$r = $query->get_result();
				while ($rs = $r->fetch_assoc()){

					$qty = $rs['quantity'];
					$pn = $rs['partnumber'];
					$upstmt = "update partsinventory set onhand = onhand + $qty, "
					. "netonhand = netonhand + $qty where shopid = ? and partnumber = ?";
					if ($upquery = $conn->prepare($upstmt)){

						$upquery->bind_param("ss",$oshopid,$pn);
						$upquery->execute();
						$conn->commit();
						$upquery->close();

					}

				}
				$query->close();
			}
		    }

			/*$stmt = "update parts set quantity = 0 where roid = ? and shopid = ?";
			if ($query = $conn->prepare($stmt)){
				$query->bind_param("is",$roid,$shopid);
				$query->execute();
				$conn->commit();
				$query->close();
			}*/
		}

		//Notification
	        $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='50'";
			$query = $conn->prepare($stmt);
			$query->execute();
			$query->store_result();
			$numrows = $query->num_rows();
			if ($numrows > 0)
			{
			$query->bind_result($textcontent,$emailcontent,$popupcontent);
	        $query->fetch();
	        $emailcontent=str_replace("*|ROTYPE|*",$rotype,str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent)));
	        $popupcontent=str_replace("*|ROTYPE|*",$rotype,str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent)));
			$textcontent=str_replace("*|ROTYPE|*",$rotype,str_replace("*|RO|*",$roid,$textcontent));
			$stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'50',?,?,?)";
     		if ($query = $conn->prepare($stmt))
     		{
	 		   $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
			   $query->execute();
			   $conn->commit();
			   $query->close();
			}

		 }

		break;

	case "writer":

	  include_once "rostatus_check.php";

		$writer = $_POST['w'];
		$stmt = "update repairorders set writer = ? where shopid = ? and roid = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("ssi",$writer,$shopid,$roid);
			if ($query->execute()){
				$conn->commit();
				echo "success";
			}else{
				echo $conn->errno;
			}

		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		$empid = '';

		$stmt = "select id from employees where shopid = ? and concat(employeefirst,' ',employeelast) = ?";
	    if ($query = $conn->prepare($stmt))
	    {
		  $query->bind_param("ss",$shopid,$writer);
	      $query->execute();
	      $query->bind_result($empid);
	      $query->fetch();
	      $query->close();
	    }

	    if(!empty($empid))
	    {
	    	$stmt = "update schedule set empid = ? where shopid = ? and roid = ?";
		    if ($query = $conn->prepare($stmt))
		    {
			$query->bind_param("ssi",$empid,$shopid,$roid);
			$query->execute();
		    $conn->commit();
		    }
		}


		//Notification
	        $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='53'";
			$query = $conn->prepare($stmt);
			$query->execute();
			$query->store_result();
			$numrows = $query->num_rows();
			if ($numrows > 0)
			{
			$query->bind_result($textcontent,$emailcontent,$popupcontent);
	        $query->fetch();
	        $emailcontent=str_replace("*|WRITER|*",$writer,str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent)));
	        $popupcontent=str_replace("*|WRITER|*",$writer,str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent)));
			$textcontent=str_replace("*|WRITER|*",$writer,str_replace("*|RO|*",$roid,$textcontent));
			$stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'53',?,?,?)";
     		if ($query = $conn->prepare($stmt))
     		{
	 		   $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
			   $query->execute();
			   $conn->commit();
			   $query->close();
			}

		 }

		break;

	case "source":

		$source = $_POST['s'];
		$stmt = "update repairorders set source = ? where shopid = ? and roid = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("ssi",$source,$shopid,$roid);
			if ($query->execute()){
				$conn->commit();
				echo "success";
			}else{
				echo $conn->errno;
			}

		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}
		break;

	case "miles":

		$milesin = $_POST['milesin'];
		$milesout = $_POST['milesout'];
		$stmt = "update repairorders set vehiclemiles = ?, milesout = ? where shopid = ? and roid = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sssi",$milesin,$milesout,$shopid,$roid);
			if ($query->execute()){
				$conn->commit();
				echo "success";
			}else{
				echo $conn->errno;
			}

		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}
		break;

	case "customer":

		// POST all the customer data from customer table and update the ro with it.
		// fields to update: customer (F L),customeraddress,customercsz,customerphone,customercity,customerstate,customerzip,customerwork,cellphone,lastfirst,customerfirst,customerlast
		$shopid = $_POST['shopid'];
		$cid = $_POST['cid'];

		$stmt = "select lastname,firstname,address,city,state,zip,homephone,workphone,cellphone from customer where customerid = ? and shopid = ?";
		if ($query = $conn->prepare($stmt)){

			$query->bind_param("si",$cid,$shopid);
		    $query->execute();
		    $query->store_result();
		    $num_roid_rows = $query->num_rows;
		    if ($num_roid_rows > 0){
		    	$query->bind_result($lastname,$firstname,$address,$city,$state,$zip,$homephone,$workphone,$cellphone);
		    	$query->fetch();
		    }else{
		    	$rotype = "Unknown";
		    }
		    $query->close();


		}else{
			echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		break;


	case "comstatus":
		$shopid = $_POST['shopid'];
		$roid = $_POST['roid'];
		$comid = $_POST['comid'];
		$rawstatus = $_POST['status'];
		$status = strtoupper($_POST['status']);

		if($status=='JOB COMPLETE')$datedone = date('Y-m-d');else $datedone='0000-00-00';

		$stmt = "update complaints set acceptdecline = ?, datedone = ? where shopid = ? and roid = ? and complaintid = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("ssssi",$rawstatus,$datedone,$shopid,$roid,$comid);
			if ($query->execute()){
				$conn->commit();

     	   //notification
			$stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='8'";
			$query = $conn->prepare($stmt);
			$query->execute();
			$query->store_result();
			$numrows = $query->num_rows();
			if ($numrows > 0)
			{
			$query->bind_result($textcontent,$emailcontent,$popupcontent);
	        $query->fetch();
	        $emailcontent=str_replace("*|STATUS|*",$status,str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent)));
	        $popupcontent=str_replace("*|STATUS|*",$status,str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent)));
			$textcontent=str_replace("*|STATUS|*",$status,str_replace("*|RO|*",$roid,$textcontent));
			$stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'8',?,?,?)";
     		if ($query = $conn->prepare($stmt))
     		{
	 		   $query->bind_param('isss',$shopid,$popupcontent,$textcontent,$emailcontent);
			   $query->execute();
			   $conn->commit();
			   $query->close();
			}

		    }

				echo "success";
			}else{
				echo $conn->errno;
			}

		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}
		break;

	case "get_declined_recid":
		//"t=POST_declined_recid&roid="+roid+"&shopid="+shopid+"&comid="+comid
		$roid = $_POST['roid'];
		$shopid = $_POST['shopid'];
		$comid = $_POST['comid'];

		$stmt = "select id from recommend where shopid = '$shopid' and roid = $roid and comid = $comid";
		if ($query = $conn->prepare($stmt)){
		    $query->execute();
	    	$query->bind_result($recid);
	    	$query->fetch();
		    $query->close();
		}else{
			echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}
		echo $recid;

		break;

	case "declined":

	  include_once "rostatus_check.php";

		$shopid = $_POST['shopid'];
		$roid = $_POST['roid'];
		$comid = $_POST['comid'];
		$rawstatus = $_POST['status'];
		$status = strtoupper($_POST['status']);

		$stmt = "select count(*) c from labortimeclock where shopid = ? and roid = ? and complaintid = ? and isnull(enddatetime)";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sii",$shopid,$roid,$comid);
			$query->execute();
			$query->bind_result($countltc);
			$query->fetch();
			$query->close();
		}

		if ($countltc == 0){

			$stmt = "update complaints set acceptdecline = ? where shopid = ? and roid = ? and complaintid = ?";
			if ($query = $conn->prepare($stmt)){
				$query->bind_param("sssi",$rawstatus,$shopid,$roid,$comid);
				if ($query->execute()){
					$conn->commit();

			//notification
			$stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='11'";
			$query = $conn->prepare($stmt);
			$query->execute();
			$query->store_result();
			$numrows = $query->num_rows();
			if ($numrows > 0)
			{
			$query->bind_result($textcontent,$emailcontent,$popupcontent);
	        $query->fetch();
	        $emailcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent));
	        $popupcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent));
			$textcontent=str_replace("*|RO|*",$roid,$textcontent);
			$stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'11',?,?,?)";
     		if ($query = $conn->prepare($stmt))
     		{
	 		   $query->bind_param('isss',$shopid,$popupcontent,$textcontent,$emailcontent);
			   $query->execute();
			   $conn->commit();
			   $query->close();
			}

		    }

					//echo "success";
				}else{
					echo $conn->errno;
				}

			}else{
				echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

			// now move all parts and labor to recommended
			$stmt = "select complaint,coalesce(techreport,'') techr from complaints where roid = $roid and shopid = '$shopid' and complaintid = $comid";
			if ($query = $conn->prepare($stmt)){
			    $query->execute();
		    	$query->bind_result($complaint,$techreport);
		    	$query->fetch();
			    $query->close();
			}else{
				echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

			$complaint = "Customer Declined: ".$complaint;
			$stmt = "insert into recommend (shopid,roid,comid,`desc`,technotes) values (?,?,?,?,?)";
			if ($query = $conn->prepare($stmt)){
				$query->bind_param("siiss",$shopid,$roid,$comid,$complaint,$techreport);
				if ($query->execute()){
					$conn->commit();
					//echo "success";
				}else{
					echo $conn->errno;
				}
			}else{
				echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

			// now POST the id we just added
			$stmt = "select id from recommend where shopid = '$shopid' and roid = $roid and comid = $comid";
			if ($query = $conn->prepare($stmt)){
			    $query->execute();
		    	$query->bind_result($id);
		    	$query->fetch();
			    $query->close();
			}else{
				echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

			$stmt = "select SubLetID,SubletDesc,SubletPrice,SubletCost,SubletInvoiceNo,SubletSupplier from sublet where shopid = '$shopid' and roid = $roid and complaintid = $comid";
			//echo $stmt."\r\n";
			$stmt = "select SubLetID,SubletDesc,SubletPrice,SubletCost,SubletInvoiceNo,SubletSupplier from sublet where shopid = ? and roid = ? and complaintid = ?";
			$totalrec = 0;
			if ($query = $conn->prepare($stmt)){
				$query->bind_param("sii",$shopid,$roid,$comid);
			    $query->execute();
			    $result = $query->get_result();
				while ($row = $result->fetch_assoc()){
					$subdesc = $row['SubletDesc'];
					$subcost = $row['SubletCost'];
					$subprice = $row['SubletPrice'];
					$subsupp = $row['SubletSupplier'];
					$totalrec += $row["SubletPrice"];

					$sstmt = "insert into recommendsublet (recid,shopid,roid,comid,subletdesc,subletcost,subletprice,supplier) values (?,?,?,?,?,?,?,?)";
					if ($squery = $conn->prepare($sstmt)){
						$squery->bind_param("isiisdds",$id,$shopid,$roid,$comid,$subdesc,$subcost,$subprice,$subsupp);
						if ($squery->execute()){
							$conn->commit();
						}else{
							echo $conn->error;
						}
					}else{
						echo $conn->error;
					}
				}
			}

			$stmt = "select `shopid`,`LaborID`,`ROID`,`HourlyRate`,`ratelabel`,`LaborHours`,`Labor`,`Tech`,`LineTotal`,`LaborOp`,`complaintid`,`techrate` from labor where shopid = ? and roid = ? and complaintid = ?";

			if ($query = $conn->prepare($stmt)){
				$query->bind_param("sii",$shopid,$roid,$comid);
			    $query->execute();
			    $result = $query->get_result();
				while ($row = $result->fetch_assoc()){
					$totalrec += $row["LineTotal"];
					$labordesc = $row['Labor'];
					$rate = $row['HourlyRate'];
					$hrs = $row['LaborHours'];
					$linetotal = $row['LineTotal'];
					$techname = $row['Tech'];
					$ratelabel = $row['ratelabel'];
					$istmt = "insert into recommendlabor (recid,shopid,roid,comid,`desc`,rate,ratelabel,hours,`total`,tech) values (?,?,?,?,?,?,?,?,?,?)";
					if ($query = $conn->prepare($istmt)){
						$query->bind_param("isiisdsdds",$id,$shopid,$roid,$comid,$labordesc,$rate,$ratelabel,$hrs,$linetotal,$techname);
						if ($query->execute()){
							$conn->commit();
						}
					}
				}
			}

			$stmt = "select upper(updateinvonadd) from company where shopid = ?";
            if ($query = $conn->prepare($stmt)){
	        $query->bind_param("s",$shopid);
            $query->execute();
            $query->bind_result($updateinvonadd);
            $query->fetch();
            $query->close();
            }else{
	         echo "RO Type failed: (" . $conn->errno . ") " . $conn->error;
            }

			$stmt = "select allocated,`shopid`,`PartID`,`PartNumber`,`PartDesc`,`PartPrice`,`Quantity`,`ROID`,`Supplier`,`Cost`,`PartInvoiceNumber`,`PartCode`,`LineTTLPrice`,`LineTTLCost`,`Date`,`PartCategory`,`complaintid`,`discount`,`net`,`tax`,`bin`,`overridematrix`,`POSTed` from parts where deleted = 'no' and shopid = ? and roid = ? and complaintid = ?";
			if ($query = $conn->prepare($stmt)){
				$query->bind_param("sii",$shopid,$roid,$comid);
			    $query->execute();
			    $result = $query->get_result();
				while ($row = $result->fetch_assoc()){
					$totalrec += $row["LineTTLPrice"];
					$pn = $row['PartNumber'];
					$pd = $row['PartDesc'];
					$q = $row['Quantity'];
					$supp = $row['Supplier'];
					$cost = $row['Cost'];
					$pin = $row['PartInvoiceNumber'];
					$pc = $row['PartCode'];
					$pp = $row['PartPrice'];
					$extp = $row['LineTTLPrice'];
					$extc = $row['LineTTLCost'];
					$cost = $row['Cost'];
					$pdate = $row['Date'];
					$cat = $row['PartCategory'];
					$disc = $row['discount'];
					$net = $row['net'];
					$tax = $row['tax'];
					$allocated = $row['allocated'];
					$bin = $row['bin'];
					$overridematrix = $row['overridematrix'];
					$tstmt = "insert into recommendparts (allocated,shopid,recid,partnumber,partdesc,partprice,quantity,roid,supplier,cost,partinvoicenumber,partcode,"
					. "linettlprice,linettlcost,`date`,partcategory,complaintid,discount,`net`,tax,bin,overridematrix) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
					if ($query = $conn->prepare($tstmt)){
						$query->bind_param("ssissddisdssddssiddsss",$allocated,$shopid,$id,$pn,$pd,$pp,$q,$roid,$supp,$cost,$pin,$pc,$extp,$extc,$pdate,$cat,$comid,$disc,$net,$tax,$bin,$overridematrix);
						if ($query->execute()){
							$conn->commit();
						}
					}

					// update the partinventory quantities
					if (strtoupper($allocated) != 'NON' && $updateinvonadd=='YES'){
						$ustmt = "update partsinventory set netonhand = netonhand + $q, onhand = onhand + $q where shopid = '$oshopid' and partnumber = ?";
						if ($uquery = $conn->prepare($ustmt)){
							$uquery->bind_param("s",$pn);
							if ($uquery->execute()){
								$conn->commit();
							}
						}
					}

				}
			}

			$stmt = "update recommend set totalrec = $totalrec where id = $id";
			if ($query = $conn->prepare($stmt)){
				if ($query->execute()){
					$conn->commit();
					//echo "success";
				}else{
					echo $conn->errno;
				}
			}else{
				echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

			$ustmt = "delete f from rofees f,parts p where f.shopid=p.shopid and f.itemid=p.partid and p.shopid = ? and p.roid = ? and p.complaintid=?";
			if ($uquery = $conn->prepare($ustmt)){
				$uquery->bind_param("sii",$shopid,$roid,$comid);
			    $uquery->execute();
			    $conn->commit();
			    $uquery->close();
			}

			$stmt = "delete from parts where shopid = '$shopid' and complaintid = $comid and roid = $roid";
			//echo $stmt."\r\n";
			if ($query = $conn->prepare($stmt)){
				if ($query->execute()){
					$conn->commit();
					//echo "success";
				}else{
					echo $conn->errno;
				}
			}else{
				echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

			$stmt = "delete from labor where shopid = '$shopid' and complaintid = $comid and roid = $roid";
			//echo $stmt."\r\n";
			if ($query = $conn->prepare($stmt)){
				if ($query->execute()){
					$conn->commit();
					//echo "success";
				}else{
					echo $conn->errno;
				}
			}else{
				echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

			$stmt = "delete from sublet where shopid = '$shopid' and complaintid = $comid and roid = $roid";
			//echo $stmt."\r\n";
			if ($query = $conn->prepare($stmt)){
				if ($query->execute()){
					$conn->commit();
					//echo "success";
				}else{
					echo $conn->errno;
				}
			}else{
				echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

			echo "success";

		}else{

			echo "There is a technician clocked in on this Customer Concern. You cannot decline it until they clock out";

		}
		break;


	case "disc":
		$rodisc = $_POST['rodisc'];
		$warrdisc = $_POST['warrdisc'];

		$stmt = "update repairorders set warrdisc = ?, rodisc = ? where shopid = ? and roid = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sssi",$warrdisc,$rodisc,$shopid,$roid);
			if ($query->execute()){
				$conn->commit();
				echo "success";
			}else{
				echo $conn->errno;
			}

		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}
		break;

	case "comm":
		$comm = $_POST['comm'];
		$by = $_POST['by'];
		$localtime = localTimeStamp($shopid);

		$stmt = "insert into repairordercommhistory (shopid,roid,comm,`by`,`datetime`) values (?,?,?,?,?)";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sisss",$shopid,$roid,$comm,$by,$localtime);
			if ($query->execute()){
				$conn->commit();
				//echo "success";
			}else{
				echo $conn->errno;
			}

		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		// output the commlog to put on the RO
		$stmt = "select `datetime`,`by`,comm from repairordercommhistory where shopid = ? and roid = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("si",$shopid,$roid);
			$query->execute();
		    $result = $query->get_result();
		    $query->store_result();
		    while($row = $result->fetch_assoc()) {
		?>
		<div class="row">
			<div style="font-size:8pt" class="col-md-4"><?php echo $row['datetime'].' - '.$row['by'];?></div><div style="font-size:8pt" class="col-md-8"><?php echo strtoupper($row['comm']); ?></div>
		</div>
		<?php
			}
		}

		 $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='77'";
			$query = $conn->prepare($stmt);
			$query->execute();
			$query->store_result();
			$numrows = $query->num_rows();
			if ($numrows > 0)
			{
			$query->bind_result($textcontent,$emailcontent,$popupcontent);
	        $query->fetch();
	        $emailcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent));
	        $popupcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent));
			$textcontent=str_replace("*|RO|*",$roid,$textcontent);
			$stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'77',?,?,?)";
     		if ($query = $conn->prepare($stmt))
     		{
	 		   $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
			   $query->execute();
			   $conn->commit();
			   $query->close();
			}

		 }
		break;

	case "rev":
		$revamt = $_POST['revamt'];
		$revdt = str_replace(',','',$_POST['revdt']);
		$revdate = date('Y-m-d',strtotime($revdt));
		$revtime = date('H:i:s',strtotime($revdt));
		$revphone = $_POST['revphone'];
		$revby = $_POST['revby'];
		$revappmethod = $_POST['revappmethod'];
		$revappby = $_POST['revappby'];


		$stmt = "insert into revisions (shopid,roid,revdate,revtime,revamt,revphone,revby,revappmethod,revappby) values (?,?,?,?,?,?,?,?,?)";
		//printf(str_replace("?","'"."%s"."'",$stmt),$shopid,$roid,$revdate,$revtime,$revamt,$revphone,$revby);
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sissdssss",$shopid,$roid,$revdate,$revtime,$revamt,$revphone,$revby,$revappmethod,$revappby);
			if ($query->execute()){
				$conn->commit();
				//echo "success";
			}else{
				echo $conn->errno;
			}

		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		// output the revision to put on the RO
		$stmt = "select revdate,revtime,revamt,revphone,revby from revisions where shopid = ? and roid = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("si",$shopid,$roid);
			$query->execute();
		    $result = $query->get_result();
		    $query->store_result();
		    while($row = $result->fetch_assoc()) {
		?>
			<div style="font-size:10pt;border-bottom:1px silver inset;" class="col-md-10"><?php echo date('m/d/Y',strtotime($row['revdate'])).' '.date('h:i',strtotime($row['revtime'])).'<br>'.strtoupper($row['revby']).' @ '.strtoupper($row['revphone']);?></div><div style="border-bottom:1px silver inset;font-size:10pt;text-align:right" class="col-md-2"><?php echo $row['revamt']; ?><br>&nbsp;</div>
		<?php
			}
		}
		break;

	case "waiting":
		$w = filter_var($_POST['w'], FILTER_SANITIZE_STRING);
		$stmt = "update repairorders set waiter = ? where shopid = '$shopid' and roid = $roid";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("s",$w);
			$query->execute();
			$conn->commit();
		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}
		echo "success";

		break;

	case "warr":
		$warrmos = $_POST['warrmos'];
		$warrmiles = $_POST['warrmiles'];

		$stmt = "update repairorders set warrmos = ?, warrmiles = ? where shopid = ? and roid = ?";
		//printf(str_replace("?","'"."%s"."'",$stmt),$shopid,$roid,$revdate,$revtime,$revamt,$revphone,$revby);
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sssi",$warrmos,$warrmiles,$shopid,$roid);
			if ($query->execute()){
				$conn->commit();
				echo "success";
			}else{
				echo $conn->errno;
			}

		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		break;

	case "fees":
        include_once "rostatus_check.php";
		$edithazardouswaste = $_POST['edithazardouswaste'];
		$editstoragefee = $_POST['editstoragefee'];
		if (isset($_POST['editcustomfee1'])){
			$editcustomfee1 = $_POST['editcustomfee1'];
		}else{
			$editcustomfee1 = 0;
		}
		if (isset($_POST['editcustomfee2'])){
			$editcustomfee2 = $_POST['editcustomfee2'];
		}else{
			$editcustomfee2 = 0;
		}
		if (isset($_POST['editcustomfee3'])){
			$editcustomfee3 = $_POST['editcustomfee3'];
		}else{
			$editcustomfee3 = 0;
		}
		$editpartstaxrate = $_POST['editpartstaxrate'];
		$editlabortaxrate = $_POST['editlabortaxrate'];
		$editsublettaxrate = $_POST['editsublettaxrate'];
		$partsdiscount = $_POST['partsdiscount'];
		$labordiscount = $_POST['labordiscount'];
		$discountreason = $_POST['discountreason'];
		$discounttaxable = $_POST['editdiscounttaxable'];
		$taxpartsprice = $_POST['edittaxpartsprice'];
		$taxchanged=$feechanged=false;

		 $stmt = "select hazardouswaste,storagefee,userfee1,userfee1type, userfee2, userfee2type ,userfee3, userfee3type, taxrate,labortaxrate,sublettaxrate,canadiantax from repairorders where shopid='$shopid' and roid='$roid'";
		 $query = $conn->prepare($stmt);
		 $query->execute();
		 $query->store_result();
		 $query->bind_result($oldhazardouswaste,$oldstoragefee,$oldcustomfee1, $oldcustomfee1type, $oldcustomfee2,$oldcustomfee2type, $oldcustomfee3, $oldcustomfee3type,$oldpartstaxrate,$oldlabortaxrate,$oldsublettaxrate,$canadiantax);
		 $query->fetch();

		 if($editpartstaxrate!=$oldpartstaxrate || $editlabortaxrate!=$oldlabortaxrate || $editsublettaxrate!=$oldsublettaxrate)
		 $taxchanged=true;

		 if($edithazardouswaste!=round($oldhazardouswaste,2) || $editstoragefee!=round($oldstoragefee,2) || $editcustomfee1!=round($oldcustomfee1,2) || $editcustomfee2!=round($oldcustomfee2,2) || $editcustomfee3!=round($oldcustomfee3,2))
		 $feechanged=true;

		 if(isset($_POST['hst']) || isset($_POST['pst']) || isset($_POST['gst']) || isset($_POST['qst']))
		 {
		 	$gst = (isset($_POST['gst']) && !empty($_POST['gst']) ? $_POST['gst'] : 0);
		 	$hst = (isset($_POST['hst']) && !empty($_POST['hst']) ? $_POST['hst'] : 0);
		 	$pst = (isset($_POST['pst']) && !empty($_POST['pst']) ? $_POST['pst'] : 0);
		 	$qst = (isset($_POST['qst']) && !empty($_POST['qst']) ? $_POST['qst'] : 0);
		 	$canadiantax = $hst.','.$pst.','.$gst.','.$qst;
		 }


         $edited_fee_arr = array();
        if (($editcustomfee1 != round($oldcustomfee1, 2)) && $oldcustomfee1type == '%'){
            $edited_fee_arr[] = "1";
        }
        if(($editcustomfee2 != round($oldcustomfee2, 2)) && $oldcustomfee2type == '%'){
           $edited_fee_arr[] = "2";
        }
        if (($editcustomfee3 != round($oldcustomfee3, 2)) && $oldcustomfee3type == '%'){
            $edited_fee_arr[] = "3";
        }
        if (!empty($edited_fee_arr)){
            $clfee_stmt = "update repairorders set";
            foreach ($edited_fee_arr as $edited_fee_num){
                $clfee_stmt .= " userfee".$edited_fee_num." = 0, userfee".$edited_fee_num."percent = 0,";
            }
            $clfee_stmt = rtrim($clfee_stmt, ",");
            $clfee_stmt .= " WHERE shopid = ? AND roid = ?";
            if ($clfee_query = $conn->prepare($clfee_stmt)){
                $clfee_query->bind_param("si", $shopid, $roid);
                if ($clfee_query->execute()){
                    $conn->commit();
                }else{
                    echo $clfee_query->errno;
                }
            }else{
                echo "CLFEE 4034 Prepare failed: (" . $conn->errno . ") " . $conn->error." In Query : ".$clfee_stmt;
            }
        }

		$stmt = "update repairorders set cb = ?, discountamt = 0,discountpercent = 0,hazardouswaste = ?,storagefee = ?,userfee1 = ?,userfee2 = ?,userfee3 = ?,userfee1amount = ?,userfee2amount = ?,userfee3amount = ?,taxrate = ?,labortaxrate = ?,sublettaxrate = ?,discounttaxable = ?,taxpartsprice = ?, canadiantax = ? where shopid = ? and roid = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sdddddddddddssssi",$discountreason,$edithazardouswaste,$editstoragefee,$editcustomfee1,$editcustomfee2,$editcustomfee3,$editcustomfee1,$editcustomfee2,$editcustomfee3,$editpartstaxrate,$editlabortaxrate,$editsublettaxrate,$discounttaxable,$taxpartsprice,$canadiantax,$shopid,$roid);
			if ($query->execute()){
				$conn->commit();
			}else{
				echo $conn->errno;
			}

		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		// if partsdiscount > 0 then delete all parts discount lines and add the new one
		$stmt = "delete from parts where shopid = '$shopid' and roid = $roid and partnumber = 'DISCOUNT' and partdesc = 'DISCOUNT' and supplier = 'DISCOUNT'";
		if ($query = $conn->prepare($stmt)){
			if ($query->execute()){
				$conn->commit();
			}else{
				echo $conn->errno;
			}
		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		$stmt = "delete from labor where shopid = '$shopid' and roid = $roid and tech = 'DISCOUNT, DISCOUNT' and labor = 'DISCOUNT'";
		if ($query = $conn->prepare($stmt)){
			if ($query->execute()){
				$conn->commit();
			}else{
				echo $conn->errno;
			}
		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		if ($partsdiscount != 0){
			$stmt = "insert into parts (shopid,roid,partnumber,partdesc,quantity,supplier,partcategory,tax,partprice,linettlprice) values ('$shopid',$roid,'DISCOUNT','DISCOUNT',1,'DISCOUNT','DISCOUNT','yes',$partsdiscount,$partsdiscount)";
			if ($query = $conn->prepare($stmt)){
				if ($query->execute()){
					$conn->commit();
				}else{
					echo $conn->errno;
				}
			}else{
				echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}
		}

		if ($labordiscount != 0){
			$stmt = "insert into labor (shopid,roid,labor,tech,linetotal) values ('$shopid',$roid,'DISCOUNT', 'DISCOUNT, DISCOUNT',$labordiscount)";
			if ($query = $conn->prepare($stmt)){
				if ($query->execute()){
					$conn->commit();
				}else{
					echo $conn->errno;
				}
			}else{
				echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}
		}

		//Notification
		if($taxchanged)
		{
	        $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='56'";
			$query = $conn->prepare($stmt);
			$query->execute();
			$query->store_result();
			$numrows = $query->num_rows();
			if ($numrows > 0)
			{
			$query->bind_result($textcontent,$emailcontent,$popupcontent);
	        $query->fetch();
	        $emailcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent));
	        $popupcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent));
			$textcontent=str_replace("*|RO|*",$roid,$textcontent);
			$stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'56',?,?,?)";
     		if ($query = $conn->prepare($stmt))
     		{
	 		   $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
			   $query->execute();
			   $conn->commit();
			   $query->close();
			}

		  }
		 }

		 if($feechanged)
		{
	        $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='62'";
			$query = $conn->prepare($stmt);
			$query->execute();
			$query->store_result();
			$numrows = $query->num_rows();
			if ($numrows > 0)
			{
			$query->bind_result($textcontent,$emailcontent,$popupcontent);
	        $query->fetch();
	        $emailcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent));
	        $popupcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent));
			$textcontent=str_replace("*|RO|*",$roid,$textcontent);
			$stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'62',?,?,?)";
     		if ($query = $conn->prepare($stmt))
     		{
	 		   $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
			   $query->execute();
			   $conn->commit();
			   $query->close();
			}

		  }
		 }

		 echo("success");

	break;

	case "addtechpaidhrs":

	$tech = $_POST['tech'];
	$hours = $_POST['hours'];
	$laborid = $_POST['laborid'];
	$dt = localTimeStamp($shopid);

	$stmt = "insert into techpaidlog (shopid,roid,laborid,tech,hours,timestamp) values (?,?,?,?,?,?)";
    if ($query = $conn->prepare($stmt))
    {
	   $query->bind_param('ssisss',$shopid,$roid,$laborid,$tech,$hours,$dt);
	   $query->execute();
	   $conn->commit();
	   $query->close();
	}

	break;

	case "editpaidhrs":

	$id = $_POST['id'];
	$hours = $_POST['hours'];
	$dt = date('Y-m-d H:i:s',strtotime(str_replace(',','',$_POST['dt'])));

	if($hours==0)
	{
	   $stmt = "delete from techpaidlog where shopid=? and id=?";
       if ($query = $conn->prepare($stmt))
      {
	   $query->bind_param('si',$shopid,$id);
	   $query->execute();
	   $conn->commit();
	   $query->close();
	  }
	}

	else
	{
	   $stmt = "update techpaidlog set hours=?,`timestamp` = ? where shopid=? and id=?";
       if ($query = $conn->prepare($stmt))
      {
	   $query->bind_param('sssi',$hours,$dt,$shopid,$id);
	   $query->execute();
	   $conn->commit();
	   $query->close();
	  }
	}


	break;

	case "cfpcheck":

	$data = array("merchantId"=>$_POST['merchantId'],"phone"=>$_POST['phone'],"altphone"=>$_POST['altphone'],"zip"=>$_POST['zip'],"firstName"=>$_POST['firstName'],"lastName"=>$_POST['lastName'],"street"=>$_POST['street'],"state"=>$_POST['state'],"city"=>$_POST['city'],"email"=>$_POST['email'],"middle"=>'',"amount"=>$_POST['Amount']);

	if(in_array($_COOKIE['shopid'],array('13846','6062','matco','Matco')))
	{
	 $data["partnerKey"] = "SBPS9teaU";
   $url = "https://us-central1-paymentsdev.cloudfunctions.net/velox/acctsearch2?".http_build_query($data); //sandbox
  }
  else
  {
   $data["partnerKey"] = "Sb24F110";
   $url = "https://us-central1-payments360-214018.cloudfunctions.net/velox/acctsearch2?".http_build_query($data);
  }

  $result = file_get_contents_curl($url);

  $cfpresult = json_decode($result);

  // Check if status property exists to prevent undefined property notice
  if(isset($cfpresult->status) && $cfpresult->status=='Error')
  {
  	echo(json_encode(array('status'=>'error','response'=>$cfpresult->errorMsg)));
  }
  elseif($cfpresult->lender[0]->status=='Found')
  {
  	$lender = strtolower($cfpresult->lender[0]->lenderName);

  	echo(json_encode(array('status'=>'found','type'=>$lender,'response'=>$cfpresult->lender[0]->result,'phone'=>$cfpresult->lender[0]->phone,'appid'=>($cfpresult->lender[0]->accountId??''),'applyurl'=>($cfpresult->lender[0]->applyUrl??''),'credits'=>(isset($cfpresult->lender[0]->availableCredit)?asDollars($cfpresult->lender[0]->availableCredit):''))));
  }
  else
  {
  	echo(json_encode(array('status'=>'notfound','response'=>$cfpresult->lender[0]->result,'applyurl'=>$cfpresult->lender[0]->applyUrl)));
  }

	break;

	case "emailmatcoreport":

	$sendfrom = $_POST['sendfrom']; //shopname
	$sendto = $_POST['email'];
	$subject = $_POST['subj'];
	$message = str_replace(chr(10),"<BR>",$_POST['msg']);
	$shopemail = $_POST['shopemail'];
	$id = $_POST['id'];

	$url = COMPONENTS_PRIVATE."/scan_tool_dashboard/generatepdf.php?save=yes&shopid=".$shopid."&id=".$id;

  $ch = curl_init();
  curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
  curl_setopt($ch, CURLOPT_URL,$url);
  $pdf = curl_exec( $ch );
  curl_close( $ch );



  //$pdf = file_get_contents_curl(COMPONENTS_PRIVATE."/scan_tool_dashboard/generatepdf.php?save=yes&shopid=".$shopid."&id=".$id);

	$res = sendEmailMandrill($sendto,$subject,$message,$sendfrom,$shopemail,array('string'=>$pdf,'name'=>'Report.pdf'));

	if(empty($res)) {
	echo 'Message could not be sent.';
	}
	else {
        recordAudit("EMail Sent", "ScanTool Report: An Email Update was sent to $sendto from ROID $roid");
        echo "success";
    }

	break;

	case "editpartfees":

	$id = $_POST['id'];
	$amount = filter_var($_POST['amount'],FILTER_SANITIZE_NUMBER_FLOAT,FILTER_FLAG_ALLOW_FRACTION);

	if(empty($amount))
	{
	   $stmt = "delete from rofees where shopid=? and roid=? and id=?";
     if ($query = $conn->prepare($stmt))
     {
	   $query->bind_param('sii',$shopid,$roid,$id);
	   $query->execute();
	   $conn->commit();
	   $query->close();
	  }
	}

	else
	{
	   $stmt = "update rofees set feeamount = ? where shopid=? and roid=? and id=?";
     if ($query = $conn->prepare($stmt))
     {
	   $query->bind_param('dsii',$amount,$shopid,$roid,$id);
	   $query->execute();
	   $conn->commit();
	   $query->close();
	  }
	}


	break;
}



mysqli_close($conn);

?>
