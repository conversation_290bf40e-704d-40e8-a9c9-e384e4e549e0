<?php


require CONN;

$shopid = $oshopid = $_GET['shopid'];
$roid = $_GET['roid'];
$comid = $_GET['comid'];
$jobid = $_GET['id'];
$tech = $_GET['tech'];
$rate = (isset($_GET['rate'])?$_GET['rate']:'');
$ratelabel = (isset($_GET['ratelabel'])?$_GET['ratelabel']:'');
//echo $shopid."<BR>".$roid."<BR>".$comid."<BR>".$jobid."<BR>".$tech."<BR>".$rate."<BR>";
include_once "rostatus_check.php";

if(in_array($shopid, array('13445','22957'))) $oshopid = '22865';

$stmt = "select id,flatprice,jobname,taxable,techstory from cannedjobs where id = ? and shopid = ?";
if ($query = $conn->prepare($stmt)){

	$query->bind_param("is",$jobid,$shopid);
    $query->execute();
    $query->bind_result($cannedjobid,$flatprice,$jobname,$taxable,$techstory);
    $query->fetch();
    $query->close();

}else{
	echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$stmt = "select lower(c.taxexempt) from repairorders r,customer c where r.origshopid=c.shopid and r.customerid=c.customerid and r.shopid = ? and r.roid = ?";
if ($query = $conn->prepare($stmt)){

	$query->bind_param("si",$shopid,$roid);
    $query->execute();
    $query->bind_result($taxexempt);
    $query->fetch();
    $query->close();
}

if ($taxexempt == "yes")
$taxable = 'no';

$stmt = "select showpartfeeaspart from settings where shopid = '$shopid'";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $query->bind_result($showpartfeeaspart);
    $query->fetch();
    $query->close();
}

$stmt = "select upper(updateinvonadd) from company where shopid = ?";
if ($query = $conn->prepare($stmt)){

	$query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($updateinvonadd);
    $query->fetch();
    $query->close();

}else{
	echo "RO Type failed: (" . $conn->errno . ") " . $conn->error;
}

//echo $taxable;

recordAudit("Add Canned Job", "Added Canned Job $jobname to RO#$roid");
// get all the labor

if(!empty($techstory))
{
	$techstory = ' '.$techstory;
	$stmt = "update complaints set techreport = concat(COALESCE(`techreport`,''),?) where shopid = ? and roid = ? and complaintid = ?";
    if ($query = $conn->prepare($stmt))
    {
        $query->bind_param('ssii',$techstory,$shopid,$roid,$comid);
        $query->execute();
        $conn->commit();
        $query->close();
    }
}

if ($flatprice == 0){
	// add the labor
	$stmt = "select laborhours,labor,flatprice,nocalc from cannedlabor where shopid = '$shopid' and cannedjobsid = $jobid";
	if($query = $conn->prepare($stmt)){
	    $query->execute();
	    $result = $query->get_result();
	    $query->store_result();
		while($row = $result->fetch_assoc()) {
			$laborhours = $row['laborhours'];
			$labor = $row['labor'];
			if ($row['flatprice'] > 0 || $row['nocalc']=='1'){
				$linetotal = $row['flatprice'];
				$scheduletext = "none";
			}else{
				$linetotal = round($laborhours * $rate,2);
				$scheduletext = "";
			}

			$lstmt = "insert into labor (shopid,roid,hourlyrate,ratelabel,laborhours,labor,tech,linetotal,complaintid,schedulelength,scheduletext,cannedjobsid) values (?,?,?,?,?,?,?,?,?,?,?,?)";
			if($lquery = $conn->prepare($lstmt)){
				$lquery->bind_param("sidsdssdissi",$shopid,$roid,$rate,$ratelabel,$laborhours,$labor,$tech,$linetotal,$comid,$taxable,$scheduletext,$jobid);
			    if ($lquery->execute()){
			    	$conn->commit();
			    	recordAudit("Add Labor", "Added Labor $labor ($laborhours hours) to RO#$roid");
			    }else{
			    	echo "labor commit failed";
			    }
			}else{
				echo "insert labor failed:".$conn->error;
			}

		}
	}else{
		echo "labor stmt error".$conn->errno;
	}

	// add the parts
	$stmt = "select partnumber,partdescription,partprice,qty,partcost,partcode,tax,supplier,partcategory,overridematrix,bin,allocated from cannedparts where shopid = '$shopid' and cannedjobsid = $jobid";
	//echo $stmt."\r\n";
	if($query = $conn->prepare($stmt)){
		//$query->bind_param("si",$shopid,$jobid);
	    $query->execute();
	    $result = $query->get_result();
	    $query->store_result();
	    while ($row = $result->fetch_array()){
	    	$partnumber = $row['partnumber'];
	    	$partdescription = $row['partdescription'];
	    	$partprice = $row['partprice'];
	    	$qty = $row['qty'];
	    	$partcost = $row['partcost'];
	    	$partcode = $row['partcode'];
	    	$tax = ($taxexempt == 'no' ? $row['tax'] : 'no');
	    	$supplier = $row['supplier'];
	    	$partcategory = $row['partcategory'];
	    	$allocated = $row['allocated'];
	    	$linettlprice = round($qty*$partprice,2);
	    	$linettlcost = round($qty*$partcost,2);
	    	$net = $partprice;
	    	$pdate = date('Y-m-d');
	    	if ($row['overridematrix'] == ""){
	    		$ovrm = "no";
	    	}else{
	    		$ovrm = $row['overridematrix'];
	    	}
	    	$bin=$row['bin'];
	    	//if (strlen($supplier) > 0){	$supplier = '';}
	    	//if (strlen($partcategory) > 0){	$partcategory = '';}
	    	$pstmt = "insert into parts (overridematrix,shopid,partnumber,partdesc,partprice,quantity,roid,cost,partcode,linettlprice,linettlcost,`date`,complaintid,net,tax,supplier,partcategory,bin,allocated,cannedjobsid";
	    	$pstmt .= ") values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
	    	//printf(str_replace("?","%s",$pstmt),$shopid,$partnumber,$partdescription,$partprice,$qty,$roid,$partcost,$partcode,$linettlprice,$linettlcost,$pdate,$comid,$net,$tax,$supplier,$partcategory);
			//echo "<BR>";
			if($pquery = $conn->prepare($pstmt)){
				$pquery->bind_param("ssssddidsddsidsssssi",$ovrm,$shopid,$partnumber,$partdescription,$partprice,$qty,$roid,$partcost,$partcode,$linettlprice,$linettlcost,$pdate,$comid,$net,$tax,$supplier,$partcategory,$bin,$allocated,$jobid);
			    if ($pquery->execute()){
			    	$partid = $conn->insert_id;
			    	$conn->commit();
			    	recordAudit("Add Part", "Added Part Number $partnumber to RO#$roid");
			    }else{
			    	echo "part commit failed";
			    }
			}else{
				echo "insert part failed:".$conn->error;
			}

			$stmt = "select additionalfeename,addfeetaxable,addfeeamt,addfeepercentordollar,qtyflag from partsinventoryfees where shopid = ? and partnumber = ?";
			if ($query = $conn->prepare($stmt)){

				$query->bind_param("ss",$oshopid,$partnumber);
				$query->execute();
				$r = $query->get_result();
				while ($rs = $r->fetch_assoc()){

					$feepartnumber = "PARTFEE";
					$qtyflag = $rs['qtyflag'];


					$feedesc = strtoupper($rs['additionalfeename']);
					$feetype = $rs['addfeepercentordollar'];

					if ($qtyflag == 'yes') {
						$feeqty = $qty;
					}else{
						$feeqty = 1;
					}

					if (strtolower($feetype) == "dollar"){
						if ($qtyflag == 'yes') {
							$feeamt = $rs['addfeeamt'] ;
							$linettlprice = ($rs['addfeeamt'] * $qty) ;
							$linettlcost = $linettlprice;
						}else{
							$feeamt = $rs['addfeeamt'] ;
							$linettlprice = $rs['addfeeamt'] ;
							$linettlcost = $linettlprice;
						}

					}elseif (strtolower($feetype) == "percent"){
						if ($qtyflag == 'yes') {
							$feeamt = sbpround(($partprice * ($rs['addfeeamt'] / 100)),2);
							$linettlprice = $feeamt * $qty;
							$linettlcost = $feeamt * $qty;
						}else{
							$feeamt = sbpround(($partprice * ($rs['addfeeamt'] / 100)),2);
							$linettlprice = $feeamt ;
							$linettlcost = $feeamt;

						}
					}
					$feesupp = "NONE";


					$feecat = "PARTFEE";
					$feedisc = 0;
					$feetax = ($taxexempt == 'no' ? $rs['addfeetaxable'] : 'no');
					$feeor = "yes";
					$pstatus = " ";
                    $deleted = "no";

                    if($showpartfeeaspart == 'yes')
                    {
						// add the fees to the parts table
						$stmt = "insert into parts (`shopid`,`PartNumber`,`PartDesc`,`PartPrice`,`Quantity`,`ROID`,`Supplier`,`Cost`,`LineTTLPrice`,`LineTTLCost`,`Date`,`PartCategory`,`complaintid`,`discount`,`net`,`tax`,`overridematrix`,`pstatus`,`deleted`,cannedjobsid) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
						if ($query = $conn->prepare($stmt)){
							$query->bind_param("sssddisdddssiddssssi",$shopid,$feepartnumber,$feedesc,$feeamt,$feeqty,$roid,$feesupp,$feeamt,$linettlprice,$linettlcost,$pdate,$feecat,$comid,$feedisc,$feeamt,$feetax,$feeor,$pstatus,$deleted,$jobid);
						    if ($query->execute()){
							    $conn->commit();
							    $query->close();
							}
						}
					}
					else
					{
						$stmt = "insert into rofees (`shopid`,`roid`,`itemid`,`feename`,`feeamount`,`taxable`) values (?,?,?,?,?,?)";

						if ($query = $conn->prepare($stmt)){
							$query->bind_param("sddsds",$shopid,$roid,$partid,$feedesc,$linettlprice,$feetax);
						    $query->execute();
							$conn->commit();
							$query->close();
						}
					}

				}
			}

			if($updateinvonadd=='YES')
            {
			// now adjust the inventory allocated
			$stmt = "update partsinventory set onhand = onhand - ?, allocatted = allocatted + ?, netonhand = netonhand - ? where shopid = ? and partnumber = ?";
			if ($query = $conn->prepare($stmt)){
				$query->bind_param("dddss",$qty,$qty,$qty,$oshopid,$partnumber);
			    if ($query->execute()){
				    $conn->commit();
				    $query->close();
				}else{
					echo "Execution Error|Inserting into Part from RecommendParts";
					exit;
				}
			}else{
				echo "Connection Error|Inserting into Parts from RecommendParts";
				exit;
			}

		    }


	    }
	}else{
		echo "parts stmt error".$conn->errno;
	}

    // add sublets
    $newsubletid = 1;
	$stmt = "select subletid from sublet where shopid = '$shopid' order by subletid desc limit 1";
	if($query = $conn->prepare($stmt)){
	    $query->execute();
	    $query->bind_result($subletid);
	    if ($query->fetch()) {
	    	if (is_numeric($subletid)){
		    	$newsubletid = $subletid + 1;
		    }
	    }
	    $query->close();
	}

	$stmt = "select * from cannedsublet where shopid = '$shopid' and cannedjobsid = $jobid";
	if($query = $conn->prepare($stmt)){
	    $query->execute();
	    $result = $query->get_result();
	    $query->store_result();
		while($row = $result->fetch_assoc()) {

			$tax = ($taxexempt == 'no' ? $row['taxable'] : 'no');
			
			$lstmt = "insert into sublet (subletid,shopid,roid,complaintid,subletdesc,subletcost,subletprice,subletsupplier,taxable) values (?,?,?,?,?,?,?,?,?)";
			if($lquery = $conn->prepare($lstmt)){
				$lquery->bind_param("isiisddss",$newsubletid,$shopid,$roid,$comid,$row['subletdesc'],$row['subletcost'],$row['subletprice'],$row['subletsupplier'],$tax);
			    if ($lquery->execute()){
			    	$conn->commit();
			    	recordAudit("Add Sublet", "Added Sublet ".$row['subletdesc']." to RO#$roid");
			    }else{
			    	echo "sublet commit failed";
			    }
			}else{
				echo "insert sublet failed:".$conn->error;
			}

			$newsubletid++;

		}
	}else{
		echo "labor stmt error".$conn->errno;
	}


	echo "success";
}else{

	// '****  add the parts and labor without totals and modify to show as part of the
	$stmt = "select laborhours,labor from cannedlabor where shopid = '$shopid' and cannedjobsid = $jobid order by id asc";
	$result = $conn->query($stmt);
	while($row = $result->fetch_array()) {
		$lbrhours = $row['laborhours'];
		$lbr = $row['labor']." (Included in $jobname)";
		$linettl = 0;
		$lstmt = "insert into labor (shopid,roid,hourlyrate,ratelabel,laborhours,labor,tech,linetotal,complaintid,memorate,schedulelength,cannedjobsid) values (?,?,?,?,?,?,?,?,?,?,?,?)";
		if ($query = $conn->prepare($lstmt)){
			$query->bind_param("sidsdssdidsi",$shopid,$roid,$rate,$ratelabel,$lbrhours,$lbr,$tech,$linettl,$comid,$rate,$taxable,$jobid);
		    if ($query->execute()){
			    $conn->commit();
			    recordAudit("Add Labor", "Added Labor $lbr ($lbrhours hours) to RO#$roid");
			    $query->close();
			}else{
				echo "Execution Error|Inserting into Part from RecommendParts";
				exit;
			}

		}else{
			echo "Connection Error|Inserting into Parts from RecommendParts";
			exit;
		}

	}

	$newsubletid = 1;
	$stmt = "select subletid from sublet where shopid = '$shopid' order by subletid desc limit 1";
	if($query = $conn->prepare($stmt)){
	    $query->execute();
	    $query->bind_result($subletid);
	    if ($query->fetch()) {
	    	if (is_numeric($subletid)){
		    	$newsubletid = $subletid + 1;
		    }
	    }
	    $query->close();
	}

	$stmt = "select * from cannedsublet where shopid = '$shopid' and cannedjobsid = $jobid";
	if($query = $conn->prepare($stmt)){
	    $query->execute();
	    $result = $query->get_result();
	    $query->store_result();
		while($row = $result->fetch_assoc()) {

			$tax = ($taxexempt == 'no' ? $row['taxable'] : 'no');
			
			$lstmt = "insert into sublet (subletid,shopid,roid,complaintid,subletdesc,subletcost,subletprice,subletsupplier,taxable) values (?,?,?,?,?,?,?,?,?)";
			if($lquery = $conn->prepare($lstmt)){
				$lquery->bind_param("isiisddss",$newsubletid,$shopid,$roid,$comid,$row['subletdesc'],$row['subletcost'],$row['subletprice'],$row['subletsupplier'],$tax);
			    if ($lquery->execute()){
			    	$conn->commit();
			    	recordAudit("Add Sublet", "Added Sublet ".$row['subletdesc']." to RO#$roid");
			    }else{
			    	echo "sublet commit failed";
			    }
			}else{
				echo "insert sublet failed:".$conn->error;
			}

		}
	}else{
		echo "labor stmt error".$conn->errno;
	}

	//'now get the parts
	$c = 1;
	$stmt = "select partnumber,partdescription,qty,partcost,tax,coalesce(supplier,'') supp, coalesce(partcategory,'') pc,bin from cannedparts where shopid = '$shopid' and cannedjobsid = $jobid order by id asc";
	//echo $stmt."\r\n";
	$result = $conn->query($stmt);
	while($row = $result->fetch_array()) {
		$partnumber = $row['partnumber'];
		$partdescription = $row['partdescription']." (Included in $jobname)";
		$qty = $row['qty'];
		$partcost = $row['partcost'];
		$pdate = date("Y-m-d");
		$tax = ($taxexempt == 'no' ? $row['tax'] : 'no');
		$supplier = $row['supp'];
		$partcategory = $row['pc'];
		$partprice = 0;
		$partcode = "New";
		$extp = 0;
		$extc = $partcost * $qty;
		$bin=$row['bin'];
		$nstmt = "insert into parts (shopid,partnumber,partdesc,partprice,quantity,roid,cost,partcode,"
		. "linettlprice,linettlcost,`date`,complaintid,net,tax,supplier,partcategory,displayorder,bin,cannedjobsid) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
		//printf(str_replace("?","%s",$nstmt),$shopid,$partnumber,$partdescription,$partprice,$qty,$roid,$partcost,$partcode,$extp,$extc,$pdate,$comid,$partprice,$tax,$supplier,$partcategory,$c);
		if ($query = $conn->prepare($nstmt)){
			$query->bind_param("sssddidsddsidsssisi",$shopid,$partnumber,$partdescription,$partprice,$qty,$roid,$partcost,$partcode,$extp,$extc,$pdate,$comid,$partprice,$tax,$supplier,$partcategory,$c,$bin,$jobid);
		    if ($query->execute()){
		    	$partid = $conn->insert_id;
			    $conn->commit();
			    recordAudit("Add Part", "Added Part Number $partnumber to RO#$roid");
			    $query->close();
			}else{
				echo "Execution Error|Inserting into Part from RecommendParts";
				exit;
			}
		}else{
			echo "Connection Error|Inserting into Parts from RecommendParts";
			exit;
		}

		$stmt = "select additionalfeename,addfeetaxable,addfeeamt,addfeepercentordollar,qtyflag from partsinventoryfees where shopid = ? and partnumber = ?";
		if ($query = $conn->prepare($stmt)){

			$query->bind_param("ss",$oshopid,$partnumber);
			$query->execute();
			$r = $query->get_result();
			while ($rs = $r->fetch_assoc()){

				$feepartnumber = "PARTFEE";
				$qtyflag = $rs['qtyflag'];


				$feedesc = strtoupper($rs['additionalfeename']);
				$feetype = $rs['addfeepercentordollar'];

				if ($qtyflag == 'yes') {
					$feeqty = $qty;
				}else{
					$feeqty = 1;
				}

				if (strtolower($feetype) == "dollar"){
					if ($qtyflag == 'yes') {
						$feeamt = $rs['addfeeamt'] ;
						$linettlprice = ($rs['addfeeamt'] * $qty) ;
						$linettlcost = $linettlprice;
					}else{
						$feeamt = $rs['addfeeamt'] ;
						$linettlprice = $rs['addfeeamt'] ;
						$linettlcost = $linettlprice;
					}

				}elseif (strtolower($feetype) == "percent"){
					if ($qtyflag == 'yes') {
						$feeamt = sbpround(($partprice * ($rs['addfeeamt'] / 100)),2);
						$linettlprice = $feeamt * $qty;
						$linettlcost = $feeamt * $qty;
					}else{
						$feeamt = sbpround(($partprice * ($rs['addfeeamt'] / 100)),2);
						$linettlprice = $feeamt ;
						$linettlcost = $feeamt;

					}
				}
				$feesupp = "NONE";


				$feecat = "PARTFEE";
				$feedisc = 0;
				$feetax = ($taxexempt == 'no' ? $rs['addfeetaxable'] : 'no');
				$feeor = "yes";
				$pstatus = " ";
                $deleted = "no";

                if($showpartfeeaspart == 'yes')
                {
					 // add the fees to the parts table
					 $stmt = "insert into parts (`shopid`,`PartNumber`,`PartDesc`,`PartPrice`,`Quantity`,`ROID`,`Supplier`,`Cost`,`LineTTLPrice`,`LineTTLCost`,`Date`,`PartCategory`,`complaintid`,`discount`,`net`,`tax`,`overridematrix`,`pstatus`,`deleted`,cannedjobsid) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
					 if ($query = $conn->prepare($stmt)){
						$query->bind_param("sssddisdddssiddssssi",$shopid,$feepartnumber,$feedesc,$feeamt,$feeqty,$roid,$feesupp,$feeamt,$linettlprice,$linettlcost,$pdate,$feecat,$comid,$feedisc,$feeamt,$feetax,$feeor,$pstatus,$deleted,$jobid);
					    if ($query->execute()){
						    $conn->commit();
						    $query->close();
						}
					 } 
				}
				else
				{
					$stmt = "insert into rofees (`shopid`,`roid`,`itemid`,`feename`,`feeamount`,`taxable`) values (?,?,?,?,?,?)";

					if ($query = $conn->prepare($stmt)){
						$query->bind_param("sddsds",$shopid,$roid,$partid,$feedesc,$linettlprice,$feetax);
					    $query->execute();
						$conn->commit();
						$query->close();
					}
				}
			}
		}

		if($updateinvonadd=='YES')
        {
		// now adjust the inventory allocated
		$stmt = "update partsinventory set onhand = onhand - ?, allocatted = allocatted + ?, netonhand = netonhand - ? where shopid = ? and partnumber = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("dddss",$qty,$qty,$qty,$oshopid,$partnumber);
		    if ($query->execute()){
			    $conn->commit();
			    $query->close();
			}else{
				echo "Execution Error|Inserting into Part from RecommendParts";
				exit;
			}
		}else{
			echo "Connection Error|Inserting into Parts from RecommendParts";
			exit;
		}
	    }

		$c++;

	}
	// ***** now add the canned job with a price
	$nstmt = "insert into parts (shopid,partnumber,partdesc,partprice,quantity,roid,cost,partcode,"
	. "linettlprice,linettlcost,`date`,complaintid,net,tax,supplier,partcategory,displayorder,bin,cannedjobsid) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
	if ($query = $conn->prepare($nstmt)){
		$partnum = "JOB";
		$jqty = 1;
		$jcost = 0;
		$jcode = "New";
		$jcat = "GENERAL PART";
		$shopname = $_COOKIE['shopname'];
		$jorder = 0;
		$query->bind_param("sssddidsddsidsssisi",$shopid,$partnum,$jobname,$flatprice,$jqty,$roid,$jcost,$jcode,$flatprice,$jcost,$pdate,$comid,$jcost,$taxable,$shopname,$jcat,$jorder,$bin,$jobid);
	    if ($query->execute()){
		    $conn->commit();
		    recordAudit("Add Canned Job", "Added Canned Job $jobname to RO#$roid");
		    $query->close();
		}else{
			echo "Execution Error|Inserting into Part from RecommendParts";
			exit;
		}

	}else{
		echo "Connection Error|Inserting into Parts from RecommendParts";
		exit;
	}
	echo "success";

}


mysqli_close($conn);
?>
