<script src="<?= SCRIPT ?>/plugins/clipboard/clipboard.min.js"></script>
<script type="text/javascript">
    const fixedMenu = document.getElementById("fixed-ro-menu");
    let fixedMenuHeight = 0;
    let placeholderDiv = null;
    let lastScrollTop = 0;
    let scrollDirection = 'none';

    // Create a placeholder div to prevent layout shifts
    function createPlaceholder() {
        if (!placeholderDiv && fixedMenu) {
            fixedMenuHeight = fixedMenu.offsetHeight;
            placeholderDiv = document.createElement('div');
            placeholderDiv.id = 'fixed-menu-placeholder';
            placeholderDiv.style.height = '0px';
            fixedMenu.parentNode.insertBefore(placeholderDiv, fixedMenu.nextSibling);
        }
    }

    // Function to handle the fixed menu appearance with smoother transitions
    function handleFixedMenu() {
        if (!fixedMenu) return;

        const currentScroll = window.pageYOffset || document.documentElement.scrollTop;

        // Determine scroll direction
        if (currentScroll > lastScrollTop) {
            scrollDirection = 'down';
        } else if (currentScroll < lastScrollTop) {
            scrollDirection = 'up';
        }
        lastScrollTop = currentScroll;

        // Handle fixed menu visibility
        if (currentScroll <= 150) {
            // At the top of the page - hide the fixed menu
            fixedMenu.classList.add('hidden');
            fixedMenu.classList.remove('d-md-flex');
            if (placeholderDiv) placeholderDiv.style.height = '0px';
        } else {
            // Show the fixed menu when scrolling down past threshold
            fixedMenu.classList.remove('hidden');
            fixedMenu.classList.add('d-md-flex');
            if (placeholderDiv) placeholderDiv.style.height = fixedMenuHeight + 'px';
        }
    }

    // Use requestAnimationFrame for smoother scrolling
    let ticking = false;
    window.addEventListener("scroll", () => {
        if (!ticking) {
            window.requestAnimationFrame(() => {
                handleFixedMenu();
                ticking = false;
            });
            ticking = true;
        }
    });

    // Initialize on page load
    window.addEventListener("load", () => {
        createPlaceholder();
        handleFixedMenu();

        // Restore scroll position if available
        const savedScrollPos = sessionStorage.getItem('roScrollPosition');
        if (savedScrollPos) {
            // Use setTimeout to ensure the page is fully loaded before scrolling
            setTimeout(() => {
                window.scrollTo(0, parseInt(savedScrollPos));
                // Clear the saved position after restoring
                sessionStorage.removeItem('roScrollPosition');
            }, 200);
        }
    });



    function newLyft() {
        eModal.iframe({
            title: 'Request a Lyft',
            url: 'lyft.php?shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>',
            size: eModal.size.lg

        });


    }

    function setLyftInterval() {
        setInterval(checkLyft, 15000)
    }

    function checkLyft() {
        $.ajax({
            data: "roid=<?php echo $roid; ?>",
            url: "<?php echo INTEGRATIONS; ?>/lyft/ridestatuscountro.php",
            type: "get",
            success: function (r) {
                r = parseFloat(r)
                if (r > 0) {
                    eModal.iframe({
                        title: "Lyft Activity",
                        url: "<?php echo INTEGRATIONS; ?>/lyft/ridestatusro.php?roid=<?php echo $roid; ?>",
                        size: eModal.size.xl,
                        buttons: [
                            {text: 'Close', style: 'warning', close: true}
                        ]
                    });
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });
    }

    function totalROBreakdown() {
        showLoader()

        ds = "t=robreakdown&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>"
        $.ajax({
            data: ds,
            url: "savedata.php",
            type: "post",
            success: function (r) {
                rar = r.split("|")
                totalpending = rar[0]
                totalothers = rar[1]
                totalfees = "<?php echo $totalfees; ?>"
                totaltax = "<?php echo $totaltax; ?>"
                html = "<div class='row'><div class='col-md-12'><div class='row'><div class='col-md-6'>"
                html += "Total Pending Status:</div><div class='col-md-3'>$" + parseFloat(totalpending).toFixed(2)
                html += "</div></div><div class='row'><div class='col-md-6'>Total All Other Status: </div><div class='col-md-3'>$" + parseFloat(totalothers).toFixed(2)
                html += "</div></div><BR><div class='row'><div class='col-md-6'>Total RO With Tax And Fees:</div><div class='col-md-3'>$" + parseFloat(parseFloat(totalothers) + parseFloat(totalpending) + parseFloat(totaltax) + parseFloat(totalfees)).toFixed(2)
                html += "</div></div></div>"

                $('#totalrobreakdown').html(html)
                hideLoader()
                $('#totalromodal').modal('show')

            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });


    }

    function clearQuickLube() {

        sbconfirm("Lube Info", "Are you sure you want to clear this lube data?", function () {

            $('#quicklubemodal').modal('hide')
            $.ajax({
                data: "t=clearlube&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&vin=<?php echo $Vin; ?>",
                url: "saveData.php",
                type: "post",
                success: function (r) {
                    console.log(r)
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            })
        });

    }

    function saveTech() {

        var complaints = [];
        $.each($("input[name='ta_complaints']:checked"), function () {
            complaints.push($(this).val());
        });
        if (complaints.length <= 0) {
            sbalert("Please select a Vehicle Issue");
            return;
        }

        var complaints_list = complaints.join(",");
        if (complaints_list == '') {
            sbalert("Please select atleast one of the issues");
            return;
        }

        var assigntoRO = $("#ta_checkall").prop("checked");

        tech = $('#assigntech').val()

        sbconfirm('Are you sure?', 'Please confirm if you want to assign selected vehicle issue to selected Tech!', function () {
            ds = "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&tech=" + tech + "&t=updatetechoncomplaint&complaints=" + complaints_list + '&assigntoRO=' + assigntoRO;
            //console.log(ds)
            $.ajax({

                data: ds,
                url: "saveData.php",
                type: "post",
                success: function (r) {
                    location.reload()
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }

            });
        });

    }

    function assignTechVehicleIssue(complaintid, tech) {
        $("#assigntech_complaint_id").val(complaintid);
        $('#assigntech_complaint').removeClass("active")
        $("#assigntech_complaint option").each(function () {
            if ($(this).text().trim() == tech.trim()) {
                $(this).prop('selected', true);
                $('#assigntech_complaint').addClass("active");
            }
        });
        $("#assigntechcomplaintmodal").modal('show');
    }

    function saveTechForVehicleIssue() {

        tech = $('#assigntech_complaint').val()
        complaintid = $("#assigntech_complaint_id").val();

        ds = "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&tech=" + tech + "&complaints=" + complaintid + "&t=updatetechoncomplaint"
        //console.log(ds)
        $.ajax({

            data: ds,
            url: "saveData.php",
            type: "post",
            success: function (r) {
                location.reload()
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }

        });

    }

    function saveRate() {

        if ($('#temprate').val() != "") {
            rate = $('#temprate').val()
            label = "TEMP"
        } else {
            rate = $('#assignrate').val()
            label = encodeURIComponent($('#assignratelabel').val())
        }
        ds = "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&rate=" + rate + "&label=" + label + "&t=updaterate"
        $.ajax({

            data: ds,
            url: "saveData.php",
            type: "post",
            success: function (r) {
                if (r != 'success') {
                    sbalert("Error Updating Labor Rate")
                } else {
                    location.reload()
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }

        });

    }

    function printVehRelease() {

        eModal.iframe({
            title: 'Vehicle Release Form',
            url: "vehiclerelease.php?roid=<?php echo $roid; ?>",
            size: eModal.size.xl,
            buttons: [
                {
                    text: 'Print',
                    style: 'primary',
                    close: true,
                    click: function () {
                        contents = $("#emodal-box").find("iframe");
                        contents[0].contentWindow.focus();
                        contents[0].contentWindow.print();
                    }
                }
            ]

        });
    }

    function showMatcoReport(id) {
        $('#matcoreportid').val(id)

        eModal.iframe({
            title: 'Scan Tool Report',
            url: "<?= COMPONENTS_PRIVATE?>/scan_tool_dashboard/generatepdf.php?id=" + id,
            size: eModal.size.xl,
            buttons: [
                {
                    text: 'Print',
                    style: 'info',
                    close: false,
                    click: function () {
                        contents = $("#emodal-box").find("iframe");
                        contents[0].contentWindow.focus();
                        contents[0].contentWindow.print();
                    }
                },
                {
                    text: 'Email',
                    style: 'primary',
                    close: true,
                    click: emailMatcoReport
                }
            ]

        });

    }

    function emailMatcoReport() {
        $('#matcoemailmodal').modal('show')
    }

    function sendMatcoEmailMessage() {


        $('#btn-matco-email').attr('disabled', 'disabled')
        email = $('#matcoemailmessageaddress').val()
        msg = encodeURIComponent($('#matcoemailmessagemessage').val())
        subj = encodeURIComponent($('#matcoemailmessagesubject').val())

        if (email.length >= 1 && msg.length > 0 && subj.length > 0) {
            $.ajax({
                data: "t=emailmatcoreport&shopid=<?php echo $shopid; ?>&roid=<?= $roid?>&shopemail=<?php echo urlencode($companyemail); ?>&sendfrom=<?php echo urlencode($_COOKIE['shopname']);?>&id=" + $('#matcoreportid').val() + "&email=" + email + "&subj=" + subj + "&msg=" + msg,
                type: "post",
                url: "saveData.php",
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                },
                success: function (r) {

                    if (r == "success") {
                        sbalert("Email Message Sent")
                        $('#matcoemailmodal').modal('hide')
                        $('#btn-matco-email').attr('disabled', false)
                    }
                }
            });
        } else {
            sbalert("You must enter an email, subject and message")

            $('#btn-matco-email').attr('disabled', false)
        }
    }

    function showTotalIssue(comid) {

        ds = "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&comid=" + comid
        $.ajax({

            data: ds,
            url: "totalbyvi.php",
            type: "post",
            success: function (r) {
                //console.log(r)
                rar = r.split("|")
                totalparts = parseFloat(rar[0])
                totallabor = parseFloat(rar[1])
                totalsublet = parseFloat(rar[2])
                totalfees = parseFloat(rar[3])
                totaltax = parseFloat(rar[4])
                if (!$.isNumeric(totalparts)) {
                    totalparts = 0
                }
                if (!$.isNumeric(totallabor)) {
                    totallabor = 0
                }
                if (!$.isNumeric(totalsublet)) {
                    totalsublet = 0
                }
                if (!$.isNumeric(totalfees)) {
                    totalfees = 0
                }
                if (!$.isNumeric(totaltax)) {
                    totaltax = 0
                }

                str = "<div class='row'><div class='col-md-4'>Total Parts: </div><div style='text-align:right' class='col-md-3'>" + totalparts + "</div></div>"
                str += "<div class='row'><div class='col-md-4'>Total Labor: </div><div style='text-align:right' class='col-md-3'>" + totallabor + "</div></div>"
                str += "<div class='row'><div class='col-md-4'>Total Sublet: </div><div style='text-align:right' class='col-md-3'>" + totalsublet + "</div></div>"
                str += "<div class='row'><div class='col-md-4'>Total Tax: </div><div style='border-bottom:2px black solid;text-align:right' class='col-md-3'>" + totaltax + "</div></div>"
                str += "<div class='row'><div class='col-md-4'>Total Concern (without fees): </div><div style='text-align:right' class='col-md-3'>" + (totalparts + totallabor + totalsublet + totaltax).toFixed(2) + "</div></div>"
                str += "<div class='row'><div class='col-md-4'>Approximate Fees for this Concern: </div><div style='text-align:right' class='col-md-3'>" + totalfees + "</div></div>"
                $('#comidtotalbox').html(str)
                $('#comidmodal').modal('show')
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);

            }
        })
    }

    function pullSheet() {

        eModal.iframe({
            title: 'Parts Pull Sheet',
            url: "partspullsheet.php?roid=<?php echo $roid; ?>",
            size: eModal.size.xl,
            buttons: [
                {
                    text: 'Print',
                    style: 'primary',
                    close: true,
                    click: function () {
                        contents = $("#emodal-box").find("iframe");
                        contents[0].contentWindow.focus();
                        contents[0].contentWindow.print();
                    }
                }
            ]

        });
    }


    function getQuickLubeData(y, m, md, e) {

        showLoader()

        ds = "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&t=quicklubedata&yr=" + y + "&md=" + md + "&mk=" + m + "&eng=" + e
        $.ajax({

            data: ds,
            url: "saveData.php",
            type: "post",
            success: function (r) {
                json = JSON.parse(r)
                if (typeof json == 'object') {
                    jsonarray = []
                    item = {}
                    item['year'] = json[0].year
                    item['make'] = json[0].make
                    item['model'] = json[0].model
                    item['engine'] = json[0].engine
                    item['viscosity'] = json[0].viscosity_1
                    item['oilCapacity'] = json[0].oilCapacity
                    item['oilCapacityDescription'] = json[0].oilCapacityDescription
                    item['oilDrainPlugTorque'] = json[0].oilDrainPlugTorque
                    item['OilFilterBrand_1'] = json[0].OilFilterBrand_1 ?? ''
                    item['OilFilterPartNumber_1'] = json[0].OilFilterPartNumber_1 ?? ''
                    item['OilFilterBrand_2'] = json[0].OilFilterBrand_2 ?? ''
                    item['OilFilterPartNumber_2'] = json[0].OilFilterPartNumber_2 ?? ''
                    item['CoolantCapacity'] = json[0].CoolantCapacity ?? ''
                    item['CoolantCapacityDescription'] = json[0].CoolantCapacityDescription ?? ''
                    item['shopid'] = "<?php echo $shopid; ?>";
                    item['vehid'] = "<?php echo $VehID; ?>";
                    item['vin'] = "<?php echo $Vin; ?>";

                    jsonarray.push(item)
                    jsonarray = encodeURIComponent(JSON.stringify(jsonarray))
                    $.ajax({

                        data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&t=newquicklubedata&json=" + jsonarray,
                        url: "saveData.php",
                        type: "post",
                        success: function (r) {
                            if (r == "Unable to save info without VIN") {
                                sbalert(r)
                            } else {

                                getQuickLube()
                            }
                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            console.log(xhr.status);
                            console.log(xhr.responseText);
                            console.log(thrownError);

                        }
                    })


                } else {
                    if (r == "no data") {
                        $('#quicklubemodal').modal('hide')
                        sbalert("No Data Available")
                    }
                }

                hideLoader()


            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);

            }
        })

    }

    function getEngines(y, m, md) {

        showLoader()

        ds = "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&t=quicklubedata&yr=" + y + "&md=" + md + "&mk=" + m
        $.ajax({

            data: ds,
            url: "saveData.php",
            type: "post",
            success: function (r) {

                if (r.indexOf("|") > 0) {

                    rar = r.split("|")
                    typ = rar[0]
                    json = JSON.parse(rar[1])

                    if (typ == "ymme") {
                        table = "<table class='table table-condensed table-striped table-sm sbdatatable w-100'>"
                        table += "<thead><tr><th>Year</th><th>Make</th><th>Model</th><th>Engine</th></tr></thead>"
                        table += "<tbody>"
                        for (i = 0; i < json.length; i++) {
                            obj = json[i]
                            table += "<tr><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.year + "</td><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.make.replace("-", " ").toUpperCase() + "</td><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.model.replace("-", " ").toUpperCase() + "</td><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.engine_name.toUpperCase() + "</td></tr>"
                        }

                        table += "</tbody></table>"
                        $('#quicklubedata').html(table)
                        $('#quicklubemodal').modal('show')
                        var options = {
                            info: false,
                            paging: false,
                            searching: false,
                            order: []
                        };

                        var table = $("#quicklubemodal .sbdatatable").dataTable(options);


                    }
                } else {
                    if (r == "no data") {
                        $('#quicklubemodal').modal('hide')
                        sbalert("No Data Available")
                    }
                }

                hideLoader()
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);

            }
        })


    }

    function getModels(selmake) {


        ds = "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&t=quicklubedata&yr=<?php echo $vehyear; ?>&md=&mk=" + selmake
        $.ajax({

            data: ds,
            url: "saveData.php",
            type: "post",
            success: function (r) {
                if (r.indexOf("|") > 0) {

                    // we have results
                    rar = r.split("|")
                    typ = rar[0]
                    json = JSON.parse(rar[1])

                    if (typ == "ymm") {
                        table = "<table class='table table-condensed table-striped table-sm sbdatatable w-100'>"
                        table += "<thead><tr><th>Year</td><th>Make</th><th>Model</th></tr></thead>"
                        table += "<tbody>"
                        for (i = 0; i < json.length; i++) {
                            obj = json[i]
                            table += "<tr><td onclick='getEngines(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\")'>" + obj.year + "</td><td onclick='getEngines(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\")'>" + obj.make.replace("-", " ").toUpperCase() + "</td><td onclick='getEngines(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\")'>" + obj.model_name + "</td></tr>"
                        }

                        table += "</tbody></table>"
                        $('#quicklubedata').html(table)
                        $('#quicklubemodal').modal('show')
                        var options = {
                            info: false,
                            paging: false,
                            searching: false,
                            order: []
                        };

                        var table = $("#quicklubemodal .sbdatatable").dataTable(options);

                    }
                    if (typ == "ymme") {
                        table = "<table class='table table-condensed table-striped table-sm sbdatatable w-100'>"
                        table += "<thead><tr><th>Year</th><th>Make</th><th>Model</th><th>Engine</th></tr></thead>"
                        table += "<tbody>"
                        for (i = 0; i < json.length; i++) {
                            obj = json[i]
                            table += "<tr><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.year + "</td><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.make.replace("-", " ").toUpperCase() + "</td><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.model.replace("-", " ").toUpperCase() + "</td><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.engine_name.toUpperCase() + "</td></tr>"
                        }

                        table += "</tbody></table>"
                        $('#quicklubedata').html(table)
                        $('#quicklubemodal').modal('show')
                        var options = {
                            info: false,
                            paging: false,
                            searching: false,
                            order: []
                        };

                        var table = $("#quicklubemodal .sbdatatable").dataTable(options);

                    }
                } else {
                    if (r == "no data") {
                        $('#quicklubemodal').modal('hide')
                        sbalert("No Data Available")
                    }
                }

            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);

            }
        })


    }

    function getQuickLube() {

        showLoader()

        ds = "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&t=storedquicklubedata&vin=<?php echo $Vin; ?>";
        $.ajax({

            data: ds,
            url: "saveData.php",
            type: "post",
            success: function (r) {
                if (r !== "no data") {

                    // now use the data to show the ql pop up
                    json = JSON.parse(r)

                    html = "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Year</div><div class='col-md-9'>" + json[0] + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Make</div><div class='col-md-9'>" + json[1].toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Model</div><div class='col-md-9'>" + json[2].toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Engine</div><div class='col-md-9'>" + json[3].toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Viscosity</div><div class='col-md-9'>" + json[4].toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Capacity</div><div class='col-md-9'>" + json[5].toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Description</div><div class='col-md-9'>" + json[6].toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Plug Torque</div><div class='col-md-9'>" + json[7].toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Oil Brand 1</div><div class='col-md-9'>" + json[8].toUpperCase() + " #" + json[9].toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Oil Brand 2</div><div class='col-md-9'>" + json[10].toUpperCase() + " #" + json[11].toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Coolant Capacity</div><div class='col-md-9'>" + json[12].toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Coolant Description</div><div class='col-md-9'>" + json[13].toUpperCase() + "</div></div>";

                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Pronto #</div><div class='col-md-9'>" + json[17] + " ".toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Valvoline #</div><div class='col-md-9'>" + json[18] + " ".toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>PG #</div><div class='col-md-9'>" + json[19] + " ".toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>FVP #</div><div class='col-md-9'>" + json[20] + " ".toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Mighty #</div><div class='col-md-9'>" + json[21] + " ".toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Carquest #</div><div class='col-md-9'>" + json[22] + " ".toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Napa #</div><div class='col-md-9'>" + json[23] + " ".toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Super Champ #</div><div class='col-md-9'>" + json[24] + " ".toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Warner #</div><div class='col-md-9'>" + json[25] + " ".toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Fram #</div><div class='col-md-9'>" + json[26] + " ".toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Mobil #</div><div class='col-md-9'>" + json[27] + " ".toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Pennzoil #</div><div class='col-md-9'>" + json[28] + " ".toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Performax #</div><div class='col-md-9'>" + json[29] + " ".toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Purolator #</div><div class='col-md-9'>" + json[30] + " ".toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Quaker State #</div><div class='col-md-9'>" + json[30] + " ".toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='text-transform:uppercase' class='col-md-3'>Service Pro #</div><div class='col-md-9'>" + json[32] + " ".toUpperCase() + "</div></div>";
                    html += "<div class='row'><div style='font-weight:bold' class='col-md-12 text-primary'><br>I understand and agree that the data provided here is from 3rd Parties. All data should be verified by shop personnel</div></div>";

                    $('#quicklubedata').html(html)
                    hideLoader()
                    $('#quicklubemodal').modal('show')

                } else {
                    <?php
                    if (strlen($vehyear) == 2) {
                        $vyear = "20" . $vehyear;
                    } else {
                        $vyear = $vehyear;
                    }
                    ?>
                    ds = "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&t=quicklubedata&yr=<?php echo $vyear; ?>&mk=<?php echo $vehmake; ?>&md=<?php echo trim(addslashes($vehmodel)); ?>"
                    $.ajax({

                        data: ds,
                        url: "saveData.php",
                        type: "post",
                        success: function (r) {
                            if (r.indexOf("|") > 0) {


                                rar = r.split("|")
                                typ = rar[0]
                                json = JSON.parse(rar[1])
                                if (typ == "ym") {
                                    table = "<table class='table table-condensed table-striped table-sm sbdatatable w-100'>"
                                    table += "<thead><tr><th>Make</th></tr></thead>"
                                    table += "<tbody>"
                                    for (i = 0; i < json.length; i++) {
                                        obj = json[i]
                                        //for (key in obj)
                                        table += "<tr><td onclick='getModels(\"" + obj.make + "\")'>" + obj.make_name + "</td></tr>"
                                    }
                                    //console.log("rendering table")
                                    table += "</tbody></table>"
                                    $('#quicklubedata').html(table)
                                    $('#quicklubemodal').modal('show')
                                    var options = {
                                        info: false,
                                        paging: false,
                                        searching: false,
                                        order: []
                                    };

                                    var table = $("#quicklubemodal .sbdatatable").dataTable(options);

                                }
                                if (typ == "ymm") {
                                    table = "<table class='table table-condensed table-striped table-sm sbdatatable w-100'>"
                                    table += "<thead><tr><th>Year</th><th>Make</th><th>Model</th></tr></thead>"
                                    table += "<tbody>"
                                    for (i = 0; i < json.length; i++) {
                                        obj = json[i]
                                        table += "<tr><td onclick='getEngines(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\")'>" + obj.year + "</td><td onclick='getEngines(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\")'>" + obj.make.replace("-", " ").toUpperCase() + "</td><td onclick='getEngines(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\")'>" + obj.model_name + "</td></tr>"
                                    }

                                    table += "</tbody></table>"
                                    $('#quicklubedata').html(table)
                                    $('#quicklubemodal').modal('show')
                                    var options = {
                                        info: false,
                                        paging: false,
                                        searching: false,
                                        order: []
                                    };

                                    var table = $("#quicklubemodal .sbdatatable").dataTable(options);

                                }
                                if (typ == "ymme") {
                                    table = "<table class='table table-condensed table-striped table-sm sbdatatable w-100'>"
                                    table += "<thead><tr><th>Year</th><th>Make</th><th>Model</th><th>Engine</th></tr></thead>"
                                    table += "<tbody>"
                                    for (i = 0; i < json.length; i++) {
                                        obj = json[i]
                                        table += "<tr><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.year + "</td><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.make.replace("-", " ").toUpperCase() + "</td><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.model.replace("-", " ").toUpperCase() + "</td><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.engine_name.toUpperCase() + "</td></tr>"
                                    }

                                    table += "</tbody></table>"
                                    $('#quicklubedata').html(table)
                                    $('#quicklubemodal').modal('show')
                                    var options = {
                                        info: false,
                                        paging: false,
                                        searching: false,
                                        order: []
                                    };

                                    var table = $("#quicklubemodal .sbdatatable").dataTable(options);

                                }
                            } else {
                                $('#quicklubemodal').modal('hide')

                                sbalert("No information is available for this vehicle.")
                            }

                            hideLoader()

                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            console.log(xhr.status);
                            console.log(xhr.responseText);
                            console.log(thrownError);

                        }
                    })
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);

            }
        })
    }

    function updateDiscounts(v) {

        // get the discount amounts and put them in the correct boxes
        v = encodeURIComponent(v)

        if (v != "none") {
            $.ajax({

                data: "roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>&t=getdiscountinfo&dname=" + v,
                type: "post",
                url: "saveData.php",
                success: function (r) {
                    rar = r.split("|")
                    parts = parseFloat(rar[0])
                    labor = parseFloat(rar[1])
                    type = rar[2].toLowerCase()
                    partsmax = parseFloat(rar[3])
                    labormax = parseFloat(rar[4])

                    if (type == "dollar") {
                        $('#partsdiscount').val("-" + parts)
                        $('#labordiscount').val("-" + labor)
                    } else {
                        totalparts = <?php echo $totalparts . "\r\n"; ?>;
                        totallabor = <?php echo $totallabor, "\r\n"; ?>;
                        partsd = totalparts * ((parts / 100)).toFixed(2)
                        labord = totallabor * ((labor / 100)).toFixed(2)
                        if (partsmax > 0 && parseFloat(partsd) > partsmax) partsd = partsmax
                        if (labormax > 0 && parseFloat(labord) > labormax) labord = labormax
                        $('#partsdiscount').val("-" + partsd.toFixed(2))
                        $('#labordiscount').val("-" + labord.toFixed(2))
                    }

                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            })
        } else {
            $('#partsdiscount').val(0)
            $('#labordiscount').val(0)
        }
    }

    function importEMS() {

        eModal.iframe({
            title: "Import EMS",
            url: "<?php echo INTEGRATIONS; ?>/ems-v2/uploadems.php?roid=<?php echo $roid; ?>",
            size: eModal.size.xl
        });


    }

    String.prototype.count = function (c) {
        var result = 0, i = 0;
        for (i; i < this.length; i++) if (this[i] == c) result++;
        return result;
    };

    function dupIt(comid) {

        sbconfirm("Duplicate Concern", "This will duplicate this Customer Concern and all parts and labor.", function () {

            // call saveData.php to duplicate the concern and the parts and labor
            ds = "t=dupconcern&comid=" + comid + "&roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>"
            $.ajax({
                data: ds,
                url: "saveData.php",
                type: "post",
                success: function (r) {
                    if (r != 'success') {
                        sbalert("Error Duplicating Vehicle Issue")
                    } else {
                        location.reload()
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            })
        });

    }

    function lockIt(comid) {

        sbconfirm("Lock Issue", "This will lock this vehicle issue and prevent customer from declining it.", function () {

            showLoader()

            ds = "t=lockconcern&comid=" + comid + "&roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>"
            $.ajax({
                data: ds,
                url: "saveData.php",
                type: "post",
                success: function (r) {
                    $('#lock-' + comid).hide()
                    $('#unlock-' + comid).show()
                    hideLoader()
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            })
        });

    }

    function unlockIt(comid) {

        sbconfirm("Unlock Issue", "This will Unlock this vehicle issue and prevent customer from declining it.", function () {

            showLoader()
            ds = "t=unlockconcern&comid=" + comid + "&roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>"
            $.ajax({
                data: ds,
                url: "saveData.php",
                type: "post",
                success: function (r) {
                    $('#unlock-' + comid).hide()
                    $('#lock-' + comid).show()
                    hideLoader()
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            })
        });

    }

    function showLyft() {
        showLoader()
        $.ajax({
            data: "roid=<?php echo $roid; ?>",
            url: "<?php echo INTEGRATIONS; ?>/lyft/ridestatuscountro2.php",
            type: "get",
            success: function (r) {
                r = parseFloat(r)
                hideLoader()
                if (r > 0) {
                    eModal.iframe({
                        title: "Lyft Activity",
                        url: "<?php echo INTEGRATIONS; ?>/lyft/ridestatusro.php?roid=<?php echo $roid; ?>",
                        size: eModal.size.xl,

                    });
                } else if (r == 0) {
                    eModal.iframe({
                        title: 'Request a Lyft',
                        url: 'lyft.php?shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>',
                        size: eModal.size.lg,


                    });

                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });


    }


    function inputRefName() {
        $('#refnamemodal').modal('show')
    }

    function registerTires() {

        locationid = encodeURIComponent("<?php echo $locationid; ?>");
        dealerid = encodeURIComponent("<?php echo $dealerid; ?>");

        useridpassword = encodeURIComponent("<?php echo $useridpassword; ?>");

        cimspartid = $('#cimspartid').val()
        fn = encodeURIComponent($('#cimsfname').val())
        ln = encodeURIComponent($('#cimslname').val())
        a = encodeURIComponent($('#cimsaddr').val())
        c = encodeURIComponent($('#cimscity').val())
        s = encodeURIComponent($('#cimsstate').val())
        z = encodeURIComponent($('#cimszip').val())
        p = encodeURIComponent($('#cimsphone').val())
        e = encodeURIComponent($('#cimsemail').val())
        brand = encodeURIComponent($('#cimsbrand').val())
        qty = encodeURIComponent($('#cimsqty').val())
        tin = encodeURIComponent($('#cimstin').val())
        datesold = '<?php echo date("Y-m-d"); ?>';


        ds = "cimspartid=" + cimspartid + "&datesold=" + datesold + "&roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>&t=cimsreg&locationid=" + locationid + "&dealerid=" + dealerid + "&useridpassword=" + useridpassword + "&fn=" + fn + "&ln=" + ln + "&a=" + a + "&c=" + c + "&s=" + s + "&z=" + z + "&p=" + p + "&e=" + e + "&brand=" + brand + "&qty=" + qty + "&tin=" + tin

        $.ajax({

            data: ds,
            url: "<?php echo INTEGRATIONS ?>/cims/postdata.php",
            type: "post",
            success: function (r) {
                //console.log(r)
                if (r == "success") {
                    sbalert("Tire Registration Successful")
                    $('#dotmodal').modal('hide')

                } else {
                    $('#dotmodal').modal('hide')

                    sbconfirm("Error", "There was an error registering. Please check your info: " + r, function () {

                        $('#dotmodal').modal('show')
                    });

                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });

    }


    function DOTReg(partid, partdesc, qty) {

        locid = "<?php echo $locationid; ?>";
        if (locid != "") {

            // process the reg
            $('#cimsbrand').val(partdesc.toUpperCase())
            $('#cimsqty').val(qty)
            $('#cimspartid').val(partid)
            $('#cimsqtylbl').css("-webkit-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)")
            $('#cimsbrandlbl').css("-webkit-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)")
            $('#dotmodal').modal('show')

        } else {
            $('#dotalertmodal').modal('show')

        }

    }


    function calculatePercent() {

        subtotal = "<?php echo $subtotal; ?>";
        percent = $('#discountpercentcalc').val()
        if ($.isNumeric(percent) && $.isNumeric(subtotal)) {
            discdollars = parseFloat(subtotal) * (parseFloat(percent) / 100)
            discdollars = discdollars.toFixed(2)
            $('#discountpercentcalcresult').html("Enter a labor or parts discount of $" + discdollars)
        }

    }


    function reloadWOQuoteid() {

        location.href = 'ro.php?roid=<?php echo $roid; ?>'

    }

    function createCanned(comid) {

        sbconfirm("Create New Canned Job", "This function will create a NEW Canned Job from the parts and labor listed here. You will be able to use this new Canned Job on future RO's.  Are you sure?", function () {

            htmlstr = "<div style='text-align:left'><br><span style='font-weight:normal;color:black'>Job Name:</span> <input style='text-transform:uppercase' class='form-control' placeholder='Enter job name' id='newcjname' type='text'><br><span style='font-weight:normal;color:black'>Taxable:</span> <select class='form-control' id='newcjtaxable'>"
            htmlstr += "<option value='Yes'>Taxable</option><option value='No'>Non-Taxable</option></select></div>"

            setTimeout(function () {

                setTimeout(function () {
                    $('#newcjname').focus()
                }, 500)

                sbconfirm("Enter Details", "Enter a name for the new Canned Job and select Taxable or not<br>" + htmlstr, function () {

                    setTimeout(function () {
                        jobname = encodeURIComponent($('#newcjname').val())
                        taxable = $('#newcjtaxable').val()
                        if (jobname.length > 0) {
                            ds = "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&t=createcannedjob&comid=" + comid + "&jobname=" + jobname + "&taxable=" + taxable
                            $.ajax({
                                data: ds,
                                url: "saveData.php",
                                type: "post",
                                success: function (r) {
                                    if (r == "success") {
                                        sbalert("New Canned Job Added")
                                    }
                                },
                                error: function (xhr, ajaxOptions, thrownError) {
                                    console.log(xhr.status);
                                    console.log(xhr.responseText);
                                    console.log(thrownError);
                                }
                            });
                        } else {
                            sbalert("You must enter a job name");
                        }
                    }, 500)

                });
            }, 200);

        })

    }

    function calendarSet() {

        localStorage.setItem("calendarroid", "<?php echo $roid; ?>")
        location.href = '<?= COMPONENTS_PRIVATE ?>/v2/calendar/calendar.php?roid=<?php echo $roid; ?>'

    }

    function saveOrigRO() {

        amt = $('#origro').val()
        ds = "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&t=origro&amt=" + amt
        $.ajax({
            data: ds,
            url: "saveData.php",
            type: "post",
            success: function (r) {
                //console.log(r)
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });

    }

    function saveRefName() {

        sourcerefname = $('#refname').val()
        so = $('#currentsource').val().toUpperCase()
        ds = "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&t=refname&origtech=" + encodeURIComponent(sourcerefname) + "&source=" + so

        $.ajax({
            data: ds,
            url: "saveData.php",
            type: "post",
            success: function (r) {
                //console.log(r)
                $('#refnamemodal').modal('hide')

                location.reload()

            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });

    }


    function saveFromQuote() {

        var str = [];
        var cont = true
        $('#btn-sq').attr('disabled', 'disabled')

        $("tr.qrtr").each(function () {
            var $this = $(this)
            var type = $this.attr('data-type')
            var itemid = $this.attr('id')

            <?php if($pickissues == 'yes'){?>

            if (type == 'p' || type == 's') {
                var pcomm = $this.find("select.qrcomm").val()
                if (pcomm == 'none') cont = false
                str.push(type + '*|*' + itemid + '*|*' + pcomm)
            } else if (type == 'l') {
                var pcomm = $this.find("select.qrcomm").val()
                if (pcomm == 'none') cont = false
                var ptech = $this.find("select.qrtech").val()
                if (ptech == 'none') cont = false
                str.push(type + '*|*' + itemid + '*|*' + pcomm + '*|*' + ptech)
            }

            <?php }else{?>

            var ptech = $this.find("select.qrtech").val()
            if (ptech == 'none') cont = false
            str.push(itemid + '*|*' + ptech)

            <?php }?>

        })

        if (!cont) {
            <?php if($pickissues == 'yes'){?>
            sbalert("Please select Customer Concern and tech for each part / labor")
            <?php }else{?>
            sbalert("Please select tech for each labor")
            <?php }?>
            $('#btn-sq').attr('disabled', false)
            return
        }

        var str_list = str.join("*||*")
        console.log(str_list)

        <?php if($pickissues == 'yes'){?>
        ds = "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&t=savequoteitem&dstr=" + str_list + "&quoteid=<?php echo $quoteid; ?>&updateinv=<?= $updateinvonadd?>"
        <?php }else{?>
        ds = "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&t=savenewquoteitem&dstr=" + str_list
        <?php }?>

        $.ajax({
            data: ds,
            url: "saveData.php",
            type: "post",
            success: function (r) {
                if (r == 'success')
                    reloadWOQuoteid()
                else
                    sbalert(r)
                $('#btn-sq').attr('disabled', false)
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });


    }

    function setTag() {

        tag = $('#newtagnumber').val()
        $.ajax({

            data: "t=settag&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&tag=" + tag,
            url: "saveData.php",
            type: "post",
            success: function (r) {
                if (r == "success") {
                    location.reload()
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });

    }

    function editSavePayment() {

        amt = $('#editpaymentamount').val()
        pdate = $('#editpaymentdate').val()
        ptype = $('#editpaymentmethod').val()
        pnumber = $('#editpaymentref').val()
        id = $('#editpaymentid').val()

        $.ajax({

            data: "t=editpayment&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&id=" + id + "&pdate=" + pdate + "&ptype=" + ptype + "&pnumber=" + pnumber + "&amt=" + amt,
            url: "saveData.php",
            type: "post",
            success: function (r) {
                if (r == "success") {
                    location.href = 'ro.php?roid=<?php echo $roid; ?>&showpayments=yes'
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });

    }

    function vehicleSearch() {
        eModal.iframe({
            title: 'Repair History Search <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="This function will allow you to search all of your repair history for the type of vehicle listed below and show you jobs you have done on other vehicles similar to this one. This will enable you to lookup oil filter numbers and capacities, or brake pad part numbers and transfer the parts and labor right to your RO.  You can also expand your search by removing one or more of the search criteria below.  You can also modify the search criteria manually."></i>',
            url: 'searchhistory.php?roid=<?php echo $roid; ?>',
            size: eModal.size.xl

        });

    }

    function editPayment(id) {

        <?php
        if ($deletepaymentsreceived == "YES"){
        ?>
        $('#editpaymentid').val(id)

        showLoader()

        $('#editpaymentmethod').val('').removeClass("active")

        $.ajax({

            data: "t=getpayment&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&id=" + id,
            url: "saveData.php",
            type: "post",
            success: function (r) {
                if (r.indexOf("|") > 0) {
                    rar = r.split("|")
                    pdate = rar[0]
                    ptype = rar[1].toLowerCase()
                    pnumber = rar[2]
                    amt = rar[3]

                    $('#editpaymentamount').val(amt)
                    $('#editpaymentdate').val(pdate)
                    $('#editpaymentmethod').val(ptype).addClass('active')
                    $('#editpaymentref').val(pnumber)
                    hideLoader()
                    $('#editpmtmodal').modal('show')
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });


        <?php
        }
        ?>

    }

    function sendEsigRequest() {


        sigrequestemail = $('#sigrequestemail').val()
        sigrequestsubject = $('#sigrequestsubject').val()
        sigrequestmessage = $('#sigrequestmessage').val()
        $('.btn-md').attr('disabled', 'disabled')
        showLoader()

        $.ajax({

            data: "email=" + sigrequestemail + "&message=" + sigrequestmessage + "&subject=" + sigrequestsubject + "&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>",
            url: "requestesig.php",
            type: "post",
            success: function (r) {
                if (r == "success") {
                    $('#esigrequestmodal').modal('hide')
                    sbalert("The request was successfully sent")
                }
                $('.btn-md').attr('disabled', false)
                hideLoader()
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }

        })


    }

    function applyDiscount(discountpercentage, discounttype) {

        sbconfirm("Discount", "This will apply a discount of " + discountpercentage + "% to this RO.  Are you sure?", function () {

            if (discounttype == "") {
                discounttype = "partsandlabor"
            }
            discounttype = discounttype.toLowerCase()
            totalparts = <?php echo $totalparts; ?>;
            totallabor = <?php echo $totallabor; ?>;

            if (totalparts > 0 && (discounttype == "parts" || discounttype == "partsandlabor")) {
                partsdisc = totalparts * (discountpercentage / 100)
            } else {
                partsdisc = 0
            }
            if (totallabor > 0 && (discounttype == "labor" || discounttype == "partsandlabor")) {
                labordisc = totallabor * (discountpercentage / 100)
            } else {
                labordisc = 0
            }
            if (partsdisc > 0 || labordisc > 0) {
                //console.log(totalparts+":"+totallabor)
                $.ajax({

                    data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&t=applydiscount&partsdiscount=-" + partsdisc + "&labordiscount=-" + labordisc,
                    type: "post",
                    url: "saveData.php",
                    success: function (r) {
                        //console.log(r)
                        location.reload()
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    }

                });

            }
        });

    }

    function launchPartsTech(partstech) {

        $('#partsordering').modal('hide')
        ;
        // check for open sessions that have not been ordered
        $.ajax({

            data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&type=getopen",
            url: "<?= INTEGRATIONS ?>/partstech-v2/postpartstech.php",
            type: "post",
            success: function (r) {
                //console.log("'"+r+"'")
                if (r.length > 0 && r[0].hasOwnProperty('sessionId') && r[0].sessionId != 'none') {
                    $('#partstechrows').html('')
                    var json = r;
                    for (i = 0; i < json.length; i++) {
                        var cart = json[i];
                        console.log(cart)
                        str = '<tr class="partstechtr"><td style="width: 80%" onclick="openPartsTechQuote(\'' + cart.sessionId + '\',\'' + cart.mfg + '\')">' + cart.sessionId + '</td><td class="text-end" style=""></td></tr>'

                        if (cart.hasOwnProperty('items')) {
                            var items = cart.items
                            var tprice = 0;
                            if (items.length > 0) {
                                str += '<tr class="partstechchild"><td style="padding-left:15px; width:100%" onclick="openPartsTechQuote(\'' + cart.sessionId + '\',\'' + cart.mfg + '\')" colspan="2"><table style="width:100%; #ededed;">';
                                str += '<tr><th>Part Number</th><th>Part Description</th><th class="text-end">Qty</th><th class="text-end">Cost</th><th class="text-end">Price</th></tr>';
                                for (j = 0; j < items.length; j++) {
                                    var item = items[j];
                                    str += '<tr><td>' + item.partnumber + '</td><td>' + item.part_description + '</td><td class="text-end">' + item.quantity + '</td><td class="text-end">$' + item.cost + '</td><td class="text-end">$' + item.price + '</td></tr>';
                                    tprice = tprice + item.price;
                                }
                                str += '</table></td></tr>';
                                str += '<tr><td class="text-end">Total</td><td class="text-end"><strong>$' + tprice + '</strong></td></tr>';
                                str += '<tr class="nohover"><td colspan="2"><hr /></td></tr>'
                            }
                        }

                        $('#partstechrows').append(str)
                        $('#partstechheader').html("Quotes Not Ordered")
                        $('#partstechmessage').html("You have open Quotes for this Repair Order that have not been ordered.  If you wish to place an order for the parts for any quote listed below, click the Quote ID number. If you want to create a new Quote, click the Create New Quote button")
                    }
                    $('#partstechmodal').modal('show')
                    $('#partsordering').modal('hide')
                } else {
                    //console.log("launching new")
                    path = "<?= INTEGRATIONS ?>/partstech-v2/main.php?existing=no&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&vin=<?php echo str_replace('"', '\"', $Vin); ?>"
                    winPopUp(path)
                    ;
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });

    }

    function openPartsTech() {
        path = "<?= INTEGRATIONS ?>/partstech-v2/main.php?existing=no&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&vin=<?php echo str_replace('"', '\"', $Vin); ?>"
        winPopUp(path)

    }

    function openPartsTechQuote(id, mfg) {

        path = "<?= INTEGRATIONS ?>/partstech-v2/main.php?existing=yes&mfg=" + mfg + "&id=" + id + "&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&vin=<?php echo str_replace('"', '\"', $Vin); ?>"
        winPopUp(path)

    }

    function closePartsTechQuote() {

        $('.partstechtr').remove()
        $('#partstechmodal').modal('hide')

    }

    function launchRepairLink(uid) {
        uid = (typeof (uid) !== "undefined") ? uid : "";

        path = "<?= INTEGRATIONS ?>/repairlink-v2/index.php?uid=" + uid + "&entity=ro&entity_id=<?= $roid ?>";
        h = screen.availHeight - 80
        w = screen.availWidth - 20
        str = 'addressbar=0,toolbar=0,scrollbars=1,location=0,statusbar=0,menubar=0,resizable=yes,screenX=0,screenY=0,top=0,left=0,maximize=1,'
        str = str + 'height=' + h + ', width=' + w
        var mywin = window.open(path, "rlwin", str)
        wintimer = setInterval(function () {
            if (mywin.closed) {
                location.reload()
            }
        }, 1000)

    }

    function createMVR() {

        email = $('#mvremail').val()
        ds = "e=" + email + "&cid=<?php echo $CustomerID; ?>&cell=<?php echo $CellPhone; ?>&shopid=<?php echo $shopid; ?>"
        //console.log(ds)
        if (email.length == 0) {
            sbalert("You must have a valid email address to create a MyVehicleRepairs.net account")
            return
        } else {

            $.ajax({
                data: ds,
                url: "mvr/mvr.php",
                type: "post",
                success: function (r) {
                    if (r == "success") {
                        $('#mvrmodal').modal('hide')
                        sbalert("Customer was successfully registered.  They will receive an email in the next few moments.")
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }

            })

        }

    }

    jQuery(function () {


        $('#cc').change(function () {
            v = $(this).val()
            // check for validation characters
            uparrows = v.count('^')
            if (v.indexOf("%B") >= 0 && uparrows == 2 && v.indexOf("?") >= 0) {
                // now parse the string to get a valid cc number
                /*tar = v.split("^")
                		testcc = tar[0]
                		testcc = textcc.replace("%B","")*/
                //if (testcc.length == 16 || testcc.length == 15){
                $('#cardstring').val(v)
                // hide the cc fields
                $('#ccinfo').fadeOut()
                $('#cc').val('')
                $('#authorizemsg').html("Credit Card successfully swiped.  <br>Click the Process CC button to charge the customers credit card.").removeClass("alert-danger").addClass("alert-success")
                //}
            } else {
                sbalert("Invalid Credit Card Swipe.  Please try again")
                $('#cardstring').val('')
                $('#cc').val('')
            }
        });
        checkSMS()


        <?php
        if (isset($_GET['restore'])){
        ?>
        showRecRepairs();
        var currentURL = window.location.href;
        var updatedURL = removeURLParameter(currentURL, 'restore');
        <?php
        }
        ?>
        if (localStorage.getItem("touch") == "yes") {
            $('#sigfirst').css("display", "")
        }
    });

    function removeURLParameter(url, parameter) {

        var urlObject = new URL(url);
        var searchParams = urlObject.searchParams;
        searchParams.delete(parameter);
        var updatedURL = urlObject.origin + urlObject.pathname + '?' + searchParams.toString();
        window.history.replaceState({path: updatedURL}, '', updatedURL);
        return updatedURL;
    }

    function getEsigFirst() {


        $.ajax({
            data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&i=new",
            url: "<?= COMPONENTS_PUBLIC ?>/invoices/printpdfro.php",
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            },
            success: function (r) {
                $('#invpath').val(r)
            }
        });
        invpath = $('#invpath').val()

        eModal.iframe({
            title: 'Repair Order',
            url: '<?= COMPONENTS_PRIVATE ?>/esig-v2/touchsignature.php?path=' + invpath + '&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>',
            size: eModal.size.lg,

        });


    }

    function addMobileSigToRO(ptype = '') {

        // eModal.close()
        //use the sigpath and send it to the signaturesaveimage.php to attach, then reshow the
        //console.log($('#signame').val()+":"+$('#invpath').val())
        ds = "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&sig=" + $('#signame').val() + "&pdf=" + $('#invpath').val()
        //console.log(ds)
        $.ajax({
            url: "<?= COMPONENTS_PRIVATE ?>/esig-v2/signaturesavetoro.php",
            data: ds,
            success: function (r) {
                filepath = '<?= $_SERVER['DOCUMENT_ROOT'] ?>/sbp/savedinvoices/<?php echo $shopid; ?>/<?php echo $roid; ?>/' + r

                if (ptype == 'WO')
                    $('#invpath').val(filepath)
                else
                    $("#invpath").val(r)

                sbconfirm("Email Invoice", "Do you want to email this invoice?", function () {
                    eModal.close()
                    emailRO()
                });
            }
        });

    }


    function sigPad() {

        eModal.close()

        setTimeout(function () {

            eModal.iframe({
                title: 'Signature',
                url: "<?= COMPONENTS_PRIVATE ?>/esig-v2/signaturepad.php?shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>",
                size: eModal.size.lg,

            });
        }, 1000)


    }

    function delPayment(id) {

        sbconfirm("Delete Payment", "Are you sure you want to delete this payment?", function () {

            ds = "t=delpayment&id=" + id + "&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>"
            $.ajax({
                data: ds,
                url: "saveData.php",
                type: "post",
                success: function (r) {
                    if (r == "success") {
                        location.href = 'ro.php?roid=<?php echo $roid; ?>&showpayments=yes'
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });
        });
    }

    function saveTP(id, val) {

        ds = "t=tp&id=" + id + "&val=" + val + "&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>"
        $.ajax({
            data: ds,
            url: "saveData.php",
            type: "post",
            success: function (r) {
                if (r == "success") {
                    $('#saveResults').show()
                    setTimeout(function () {
                        $('#saveResults').fadeOut()
                    }, 1000)
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });

    }

    function editTP() {

        $('#tpmodal').modal('show')

    }

    function saveComments() {

        c = $('#comments').val()
        oric = $('#oricomments').val()
        if (c == oric) return
        ds = "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&t=comments&c=" + c
        //console.log(ds)
        $.ajax({
            type: "post",
            url: "saveData.php",
            data: ds,
            success: function (r) {
                //console.log(r)
                $('#commentssaved').html("Comments Saved")
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            },

        })

    }

    function saveRecallDetails() {

        c = $('#recall_text').val()
        oric = $('#orig_recall_text').val()
        if (c == oric) return
        ds = "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&t=recall&c=" + c
        //console.log(ds)
        $.ajax({
            type: "post",
            url: "saveData.php",
            data: ds,
            success: function (r) {
                //console.log(r)
                $('#recall_saved').html("Recall Details Saved")
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            },

        })

    }

    function checkSMS() {

        timer2 = setInterval(function () {
            $.ajax({
                data: "t=getcount&shopid=<?php echo $shopid; ?>",
                url: "<?= COMPONENTS_PRIVATE ?>/shared/smsliveaction-v2.php",
                type: "post",
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                },
                success: function (r) {
                    if (r > 0) {
                        $('.smstab').show()
                        $('#livetext_badge').html(r);
                    } else {
                        $('.smstab').hide()
                        $('#livetext_badge').html('');
                    }
                }
            });
        }, 60000)


    }

    function showSMS() {

        eModal.iframe({
            title: 'Live Text',
            url: "<?= COMPONENTS_PRIVATE ?>/shared/smslive-v2.php?p=<?php echo $CellPhone; ?>&roid=<?php echo $roid; ?>",
            size: eModal.size.xl
        });

    }

    function sendEmailMessage() {

        if ($('#emailmodal').css("display") == "none") {
            $('#emailmodal').modal('show')
        } else {

            $('.btn-md').attr('disabled', 'disabled')
            showLoader();
            email = $('#emailmessageaddress').val()
            msg = encodeURIComponent($('#emailmessagemessage').val())
            subj = encodeURIComponent($('#emailmessagesubject').val())
            if (email.length >= 1 && msg.length > 0 && subj.length > 0) {
                $.ajax({
                    data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&t=sendemail&email=" + email + "&subj=" + subj + "&msg=" + msg,
                    type: "post",
                    url: "saveData.php",
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    },
                    success: function (r) {

                        if (r == "success") {
                            sbalert("Email Message Sent")
                            $('#emailmodal').modal('hide')
                            hideLoader();
                        } else {
                            sbalert(r);
                            hideLoader();
                        }

                        $('.btn-md').attr('disabled', false)
                    }
                });
            } else {
                sbalert("You must enter an email, subject and message")

            }

        }
    }

    function sendTextMessage(c) {

        if ($('#textmodal').css("display") == "none") {
            $('#textmodal').modal('show')
        } else {
            // send text message

            $('.btn-md').attr('disabled', 'disabled')

            cell = $('#textmessagecell').val()
            sms = encodeURIComponent($('#textmessage').val())
            if (cell.length == 10 && sms.length > 0) {
                $.ajax({
                    data: "shopname=<?php echo urlencode($shopname); ?>&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&t=sendsms&cell=" + cell + "&sms=" + sms,
                    type: "post",
                    url: "saveData.php",
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    },
                    success: function (r) {

                        sbalert("Text Message Sent")
                        $('#textmodal').modal('hide')
                        $('.btn-md').attr('disabled', false)
                    }
                });
            } else {
                sbalert("You must have a 10 digit cell number and type a message to send")

            }
        }

    }

    function checkDiscount(v, id) {

        if (v > 0) {
            $('#' + id).val(v * -1)
        }
        if (v.length == 0) {
            $('#' + id).val(0)
        }

    }

    function closeMotor() {

        $('#motor-iframe').attr('src', '')
        $('#motorModal,#newmotorModal').modal('hide')

        $.ajax({
            data: "t=check&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>",
            url: "updatemotor.php",
            success: function (r) {
                if (r == "found") {
                    showLoader();
                    showMotorInfo()
                }

            }
        });

    }

    function closeProDemand() {

        $('.modal').modal('hide')
    }

    function showMotorInfo() {

        setTimeout(function () {

            hideLoader()

            eModal.iframe({
                title: 'Motor',
                url: "motorlist.php?roid=<?php echo $roid; ?>",
                size: eModal.size.xl
            });

        }, 500)

    }

    function loadMotor(comid, pid) {
        <?php if($motor == 'no'){?>
        $('#motorGlobalModal').modal('show');
        <?php }else{ ?>

        var iOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
        vin = "<?php echo str_replace('"', '\"', $Vin); ?>";

        $('#motor-iframe').attr('src', "loadmotor.php?ios=" + iOS + "&pid=" + pid + "&vin=" + vin + "&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&comid=" + comid)
        $('#motorModal').modal('show')

        <?php }?>

    }

    function loadNewMotor(comid, pid) {
        <?php if($motor == 'no'){?>
        $('#motorGlobalModal').modal('show');
        <?php }else{ ?>

        var iOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
        vin = "<?php echo str_replace('"', '\"', $Vin); ?>";

        $('#new-motor-iframe').attr('src', "motorfiles/loadnewmotor.php?pid=" + pid + "&vin=" + vin + "&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&comid=" + comid)
        $('#newmotorModal').modal('show')

        <?php }?>
    }

    function loadProDemand(type) {
        vin = "<?php echo str_replace('"', '\"', $Vin); ?>";

        eModal.iframe({
            title: 'ProDemand',
            url: "prodemand.php?vin=" + vin + "&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&pdtype=" + type,
            size: eModal.size.xl
        });

    }


    function getDisclosures() {

        showLoader()

        $.ajax({
            data: "t=getdisc&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>",
            url: "saveData.php",
            type: "post",
            success: function (r) {

                if (r.indexOf("|") >= 0) {
                    rar = r.split("|")
                    $('#rodisc').val(rar[0]).trigger('input')
                    $('#warrdisc').val(rar[1]).trigger('input')
                    hideLoader()
                    $('#disclosuremodal').modal('show')
                }
            }
        });

    }

    function itemStatus(type, id) {

        var stat = $('#' + type + '-' + id).attr('data-status')

        $.ajax({
            url: "saveData.php",
            data: "t=itemstatus&id=" + id + "&status=" + stat + "&type=" + type + "&roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>",
            type: "post",
            success: function (r) {
                if (r == "success") {

                    if (type == 'part') {
                        if (stat == '') {
                            $('#part-' + id).html("<i class='fas fa-circle-check red'></i>")
                            $('#part-' + id).attr('data-status', 'other')
                        } else if (stat == 'other') {
                            $('#part-' + id).html("<i class='fas fa-circle-check green'></i>")
                            $('#part-' + id).attr('data-status', 'done')
                        } else if (stat == 'done') {
                            $('#part-' + id).html("<i class='fas fa-circle-check yellow'></i>")
                            $('#part-' + id).attr('data-status', '')
                        }

                    } else if (type == 'labor') {
                        if (stat == '') {
                            $('#labor-' + id).html("<i class='fas fa-circle-check green'></i>")
                            $('#labor-' + id).attr('data-status', 'done')
                        } else {
                            $('#labor-' + id).html("<i class='fas fa-circle-check yellow'></i>")
                            $('#labor-' + id).attr('data-status', '')
                        }

                    } else if (type == 'sublet') {
                        if (stat == '') {
                            $('#sublet-' + id).html("<i class='fas fa-circle-check green'></i>")
                            $('#sublet-' + id).attr('data-status', 'yes')
                        } else if (stat == 'yes') {
                            $('#sublet-' + id).html("<i class='fas fa-circle-check red'></i>")
                            $('#sublet-' + id).attr('data-status', 'no')
                        } else if (stat == 'no') {
                            $('#sublet-' + id).html("<i class='fas fa-circle-check yellow'></i>")
                            $('#sublet-' + id).attr('data-status', '')
                        }

                    }

                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });

    }


    function clearUserFee(feenumber) {
        $('#feesmodal').modal('hide')

        sbconfirm("Clear Custom Fee", "Are you sure you want to clear this fee?", function () {

            ds = "t=clearfee&feenumber=" + feenumber + "&roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>"

            $.ajax({
                data: ds,
                type: "post",
                url: "saveData.php",
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                },
                success: function (r) {
                    if (r == "success") {
                        location.reload()
                    }
                }
            });

        });
    }

    function openTPMS() {
        <?php
        // get tpms account if it exists
        $showtpms = "no";
        $stmt = "select username,password from tpmsaccounts where shopid = '$shopid'";
        $uname = "";
        $pword = "";
        if ($query = $conn->prepare($stmt)) {
            $query->execute();
            $query->store_result();
            $query->bind_result($uname, $pword);
            $query->fetch();
        } else {
            echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }

        if (strlen($uname) > 0){
        ?>
        URL = "http://portal.tpmsmanager.com?u=<?php echo $uname; ?>&pwd=<?php echo $pword; ?>&vin=<?php echo str_replace('"', '\"', $Vin); ?>"
        window.open(URL)
        <?php
        }else{
        ?>
        sbalert("You do not have a TPMS Speed Account.  If you would like to add it to your subscription, please contact Support")
        <?php
        }
        ?>
    }


    function showGP() {

        if ($('#gpbutton').attr("class") == "btn ") {
            sbalert("You have no parts or labor to calcuate GP")
        } else {
            eModal.iframe({
                title: 'Gross Profit',
                url: "<?= COMPONENTS_PRIVATE ?>/gp-v2/gpdetail.php?shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&subtotal=<?php echo sbpround($subtotal, 2);?>",
                size: eModal.size.lg,
                buttons: [
                    {
                        text: 'Print', style: 'secondary', close: false,
                        click: function () {
                            contents = $("#emodal-box").find("iframe");
                            contents[0].contentWindow.focus();
                            contents[0].contentWindow.print();
                        }
                    },
                ]
            });
        }
    }

    function showPIF() {

        if ($('#gpbutton').attr("class") == "btn ") {
            sbalert("You have no PPH")
        } else {
            eModal.iframe({
                title: 'Profit Per Hour (PPH)',
                url: "<?= COMPONENTS_PRIVATE ?>/pif-v2/pif_detail.php?shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&subtotal=<?php echo sbpround($subtotal, 2);?>&fees=<?php echo sbpround($totalfees, 2);?>",
                size: eModal.size.lg,
                buttons: [
                    {
                        text: 'Print', style: 'secondary', close: false,
                        click: function () {
                            contents = $("#emodal-box").find("iframe");
                            contents[0].contentWindow.focus();
                            contents[0].contentWindow.print();
                        }
                    }
                ]
            });
        }
    }

    function showPPFIssue(compid) {
        eModal.iframe({
            title: 'Profit Per Hour (PPH)',
            url: "<?= COMPONENTS_PRIVATE ?>/pif-v2/pif_issue_detail.php?shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&compid=" + compid + "&subtotal=<?php echo sbpround($subtotal, 2);?>&fees=<?php echo sbpround($totalfees, 2);?>",
            size: eModal.size.lg,
            buttons: [
                {
                    text: 'Print', style: 'secondary', close: false,
                    click: function () {
                        contents = $("#emodal-box").find("iframe");
                        contents[0].contentWindow.focus();
                        contents[0].contentWindow.print();
                    }
                }
            ]
        });
    }


    function niSuppliers() {

        eModal.iframe({
            title: 'External Parts Ordering <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Non-Integrated Parts Ordering - you will not be able to transfer ordered parts to Shop Boss"></i>',
            url: "nonintegratedpartsordering.php?vin=<?php echo str_replace('"', '\"', $Vin); ?>",
            size: eModal.size.lg
        });

    }


    function showSuppliers() {

        eModal.iframe({
            title: 'Part Supplier List',
            url: "suppliers.php?shopid=<?php echo $shopid; ?>",
            size: eModal.size.lg

        });

    }

    function cfServiceHistory() {

        vin = "<?php echo str_replace('"', '\"', $Vin); ?>";
        lic = "<?php echo addslashes(str_replace('"', "", $vehlicense)); ?>";
        state = "<?php echo $vehstate; ?>";
        shopid = "<?php echo $shopid; ?>";

        eModal.iframe({
            title: 'Carfax Service History',
            url: "<?= INTEGRATIONS ?>/carfax-v2/service-history.php?vin=" + vin + "&lic=" + lic + "&state=" + state + "&shopid=" + shopid,
            size: eModal.size.xl,


        });

    }

    function showHours(comid, laborid) {

        showLoader()

        $.ajax({
            data: "t=showhours&comid=" + comid + "&laborid=" + laborid + "&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>",
            url: "techclock.php",
            type: "post",
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            },
            success: function (r) {
                $('#tcresults').html(r)
                hideLoader()
                $('#tcmodal').modal('show')
            }
        });
    }

    function selectTechClockin(laborid, comid, clid) {

        showLoader();
        $('#techclockincomid').val(comid)
        $('#techclockinlaborid').val(laborid)

        <?php if($_COOKIE['mode'] == 'full'){?>

        ds = "roid=<?php echo $roid; ?>&t=showtechs&shopid=<?php echo $shopid; ?>&laborid=" + laborid + "&comid=" + comid
        $.ajax({

            data: ds,
            url: "techclock.php",
            type: "post",
            success: function (r) {
                $('#techlistclockin tbody').html(r)
                $('.btn-clock').attr('disabled', false)
                hideLoader()
                $('#techselectclockinmodal').modal('show')
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }

        })

        <?php }else{?>

        if (clid == 'in')
            clockin('<?= $techname?>', '')
        else
            clockout(clid, '')

        <?php }?>


    }

    function clockin(tech, buttonid) {

        $('.btn-clock').attr('disabled', 'disabled')

        laborid = $('#techclockinlaborid').val()
        comid = $('#techclockincomid').val()

        $('#clockin-' + laborid).hide()
        showLoader()

        ds = "rtco=<?php echo $requiretechclockout; ?>&t=clockin&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&laborid=" + laborid + "&comid=" + comid + "&tech=" + encodeURIComponent(tech)
        $.ajax({
            type: "post",
            data: ds,
            url: "techclock.php",
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            },
            success: function (r) {
                if (r == "success") {
                    location.reload()
                }
                if (r == "openclock") {
                    sbalert("There is an open timeclock for this tech.  Please clock out on the current labor job first")
                    hideLoader()
                    $('.btn-clock').attr('disabled', false)
                }


            }
        });
    }

    function clockout(clockid, tech) {

        $('.btn-clock').attr('disabled', 'disabled')

        laborid = $('#techclockinlaborid').val()
        comid = $('#techclockincomid').val()

        showLoader()

        $.ajax({
            type: "post",
            data: "t=clockout&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&clockid=" + clockid,
            url: "techclock.php",
            success: function (r) {
                if (r == "success") {
                    location.reload()
                } else
                    $('.btn-clock').attr('disabled', false)

                hideLoader()
            }
        });
    }

    function showPrintedRO(invpath) {
        $('#invpath').val(invpath)
        eModal.iframe({
            title: 'Printed RO',
            url: invpath,
            size: eModal.size.xl,
            buttons: [
                {
                    text: 'Email',
                    style: 'secondary',
                    close: true,
                    click: emailRO
                },
                {
                    text: 'Print',
                    style: 'primary',
                    close: false,
                    click: function () {
                        contents = $("#emodal-box").find("iframe");
                        contents[0].contentWindow.focus();
                        contents[0].contentWindow.print();
                    }
                }
            ]

        });

    }

    function showPrintedROs() {

        // get all printed RO's from the folder and show the printedros li
        $.ajax({
            data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>",
            url: "getprintedros.php",
            success: function (r) {
                console.log(r)
                if (r.indexOf("|") >= 0) {
                    $('.pro').remove()
                    rar = r.split("|")
                    console.log(rar[1])
                    $('.printedros').append(rar[1])
                }
            }
        });

    }


    <?php
    if (strlen($authnetclientkey) > 10){
    ?>

    function showProcessCC(modalname) {
        eModal.iframe({
            title: 'Process Credit Card',
            url: '<?= INTEGRATIONS?>/authorize.net/payment.php?shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&cid=<?php echo $CustomerID; ?>&amt=<?php echo $balancero; ?>',
            size: eModal.size.lg,
            buttons: [
                {text: 'Close', style: 'danger', close: true}

            ]

        });
    }

    <?php
    }else{
    ?>

    function showProcessCC(modalname) {
        $('#' + modalname).modal('show')
        setTimeout(function () {
            $('#cc').focus()
        }, 500);
    }

    <?php
    }
    ?>

    function addSigToRO() {

        // eModal.close()
        //use the sigpath and send it to the signaturesaveimage.php to attach, then reshow the
        //console.log($('#signame').val()+":"+$('#invpath').val())
        ds = "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&sig=" + $('#signame').val() + "&pdf=" + $('#invpath').val()
        //console.log(ds)
        $.ajax({
            url: "<?= COMPONENTS_PRIVATE ?>/esig-v2/signaturesavetoro.php",
            data: ds,
            success: function (r) {
                $('#cardknoxsigmodal').modal('hide')
                $('#tnpsigmodal').modal('hide')
                $('#invpath').val(r)
                eModal.iframe({
                    title: 'Repair Order',
                    url: r,
                    size: eModal.size.xl,
                    buttons: [
                        {
                            text: 'Print',
                            style: 'primary',
                            close: false,
                            click: function () {
                                contents = $("#emodal-box").find("iframe");
                                contents[0].contentWindow.focus();
                                contents[0].contentWindow.print();
                            }
                        },
                        {
                            text: 'Email',
                            style: 'secondary',
                            close: true,
                            click: emailRO
                        },

                    ]

                });
            }
        });

    }

    function processCC() {

        ccnum = $('#cc').val()
        amt = $('#swipepmtamount').val()
        pdate = encodeURIComponent($('#swipepmtdate').val());
        cid = "<?php echo $CustomerID; ?>";
        cardstring = encodeURIComponent($('#cardstring').val())
        //console.log(ccnum.length)
        if (ccnum.length == 0 && cardstring.length >= 70) {
            ds = "cid=" + cid + "&pdate=" + pdate + "&t=cardswipe&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&cardstring=" + cardstring + "&amt=" + amt
            //console.log(ds)
            $.ajax({
                data: ds,
                url: "chargecc.php",
                type: "get",
                success: function (r) {
                    //console.log(r)
                    if (r == "success") {

                        sbconfirm("Success", "Credit Card successfully processed.  Screen will be reloaded", function () {
                            location.reload()
                        });
                    } else {
                        sbalert(r)
                    }
                }
            });

        } else {
            sbalert("You must get a clean swipe of the customer's card or manually enter the card info")
        }

    }

    function savePmt() {

        showLoader()
        $('.btn-md').attr('disabled', 'disabled')

        setTimeout(function () {
            amt = encodeURIComponent($('#pmtamount').val())
            ptype = encodeURIComponent($('#pmttype').val())
            ref = encodeURIComponent($('#pmtref').val())
            pdate = encodeURIComponent($('#pmtdate').val())
            ds = "t=addpmt&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&cid=<?php echo $CustomerID; ?>&amt=" + amt + "&ptype=" + ptype + "&ref=" + ref + "&pdate=" + pdate
            $.ajax({
                data: ds,
                type: "post",
                url: "saveData.php",
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);

                },
                success: function (r) {
                    location.href = 'ro.php?roid=<?php echo $roid; ?>&showpayments=yes'
                }
            });
        }, 200)


    }

    function showRecRepairs() {

        eModal.iframe({
            title: 'Recommended Repairs <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Bold = Current RO"></i>',
            url: "recrepairs.php?r=767676&vehid=<?php echo $VehID; ?>&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>",
            size: eModal.size.lg


        });

    }

    function refreshRO() {
        // Save current scroll position before refreshing
        const scrollPos = window.pageYOffset || document.documentElement.scrollTop;
        sessionStorage.setItem('roScrollPosition', scrollPos);

        // Add a timestamp parameter to prevent browser caching
        const timestamp = new Date().getTime();
        location.href = 'ro.php?roid=<?php echo $roid; ?>&_=' + timestamp;
    }

    function launchWorldpac() {

        <?php
        if ($worldpac == "yes"){
        ?>
        URL = '<?= INTEGRATIONS ?>/worldpac-v2/main.php?shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>'
        var mywin = window.open(URL, "npwin")
        myx = setInterval(function () {
            //console.log("checking closed")
            if (mywin.closed) {
                window.location.reload()
                clearInterval(myx)
            }
        }, 1000)
        <?php
        }else{
        ?>
        alert("You must add WorldPac parts ordering in settings before you can use this integration")
        <?php
        }
        ?>
    }


    function addMaint() {

        maintlist = $('#joblist').val()
        if (maintlist.length > 0) {

            sbconfirm("Adding Scheduled Maintenance to the RO", "Click Confirm to add your selected maintenance items to the RO", function () {

                    showLoader()

                    ds = "t=addmaint&list=" + maintlist + "&roid=<?php echo $roid; ?>"
                    //console.log(ds)
                    $.ajax({
                        data: ds,
                        type: "post",
                        url: "viorder.php",
                        success: function (r) {
                            if (r == "success") {
                                location.reload()
                            } else {
                                sbalert("Error Adding Scheduled Maintainance")
                            }
                        }
                    });

                }
            );

        } else
            sbalert("No items selected")

    }

    function getMaint() {


        vin = "<?php echo str_replace('"', '\"', $Vin); ?>";
        ds = "t=vin&vin=" + vin
        if (vin.length == 17) {
            showLoader()
            $.ajax({
                url: "edmunds.php",
                data: ds,
                success: function (r) {

                    if (r.indexOf('yearid') >= 0) {
                        rar = r.split("|")
                        yearid = rar[1]
                        veh = "<?php echo urlencode($VehInfo); ?>";
                        vehmiles = $('#milesin').html()
                        vehmiles = vehmiles.replace('<span class="fa fa-pencil"></span>', '')
                        vehmiles = vehmiles.replace(' &nbsp;<i style="float:right" class="fa fa-pencil"></i>')
                        if ($.isNumeric(vehmiles)) {
                            vehmiles = vehmiles
                        } else {
                            vehmiles = ""
                        }
                        upath = 'edmunds.php?miles=' + vehmiles + '&veh=' + veh + '&yearid=' + yearid + '&t=yearid&roid=<?php echo $roid; ?>'
                        hideLoader()
                        eModal.iframe({
                            title: 'Scheduled Maintenance <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Click a column to sort the list.  Type in the box to search. Click an item to add it to your RO"></i>',
                            url: upath,
                            size: eModal.size.lg
                        });


                    }
                }
            });
        } else {

            sbalert("A valid 17 digit VIN is required to retrieve maintenance")
        }
    }


    function showSendUpdate() {

        $('#sendupdatemodal').modal('show')

    }

    function showSendCustomUpdate() {

        $('#sendupdatemodal').modal('hide')
        $('#customemailto').val($('#updateemailto').val())
        $('#sendcustomupdatemodal').modal('show')

    }

    function showIssues() {

        eModal.iframe({
            title: 'Vehicle Issues',
            url: 'vehicleissuesedit.php?roid=<?php echo $roid; ?>',
            size: eModal.size.xl
        });


    }

    function sendUpdate(t) {

        // t = email, text or both
        em = $('#updateemailto').val()
        cp = $('#updatecellphone').val()
        send_attachment = $("#attach_invoice").is(":checked")

        if (t === "email" && em.length === 0) {
            sbalert("You must have an email to send an email update")
            return
        }
        if (t === "text" && cp.length === 0) {
            sbalert("You must have a cell phone to send a text message update")
            return
        }
        if ((t === "both" && em.length === 0) || (t === "both" && cp.length === 0)) {

            sbalert("You must have an email and cell phone to send an update to both");
            return
        }

        $('.btn-md').attr('disabled', 'disabled')

        // now send the update via ajax

        ds = "u=update&t=" + t + "&cell=" + cp + "&email=" + em + "&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&send_attachment=" + send_attachment
        $.ajax({
            data: ds,
            url: "sendupdates.php",
            success: function (r) {
                //console.log(r)
                if (r === "success") {
                    sbalert("Your update message has been sent")
                    $('#sendupdatemodal').modal('hide')

                } else {
                    sbalert(r)
                    $('#sendupdatemodal').modal('hide')

                }
                $('.btn-md').attr('disabled', false)
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        })

    }

    function showReminders() {

        eModal.iframe({
            title: 'Service Reminders <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Click on any row to edit that row.  Click the Add Reminder button to add a new reminder."></i>',
            url: 'reminderlist.php?cid=<?php echo $CustomerID; ?>&vid=<?php echo $VehID; ?>&shopid=<?php echo $shopid; ?>',
            size: eModal.size.xl

        });


    }


    function showInspection(name) {

        if (name == '') {

            eModal.iframe({
                title: 'Vehicle Inspection',
                url: '<?= COMPONENTS_PRIVATE ?>/v2/inspection_classic/dvi.php?roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>',
                size: eModal.size.xl,
                buttons: [
                    {text: 'Close', style: 'secondary', close: true, click: inspectionUpdateAlert}

                ]

            });
        } else {
            name = encodeURIComponent(name)
            eModal.iframe({
                title: 'Vehicle Inspection',
                url: '<?= COMPONENTS_PRIVATE ?>/v2/inspection_classic/dvi.php?roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>&name=' + name,
                size: eModal.size.xl
            });
        }


    }

    function inspectionUpdateAlert() {

        $.ajax({

            data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&t=alertinspection",
            url: "saveData.php",
            type: "post",
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            },
            success: function (r) {
                //console.log(r)

            }

        })


    }

    function postAllParts() {

        sbconfirm("Post All Parts", "This will post all parts on this RO to Accounting.", function () {

            showLoader()
            $.ajax({
                data: "roid=<?php echo $roid;?>&shopid=<?php echo $shopid;?>",
                url: "ropostallparts.php",
                type: "post",
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                },
                success: function (r) {
                    hideLoader()
                    if (r == "success") {
                        sbalert("All parts posted to accounting")
                    }
                }
            });
        });
    }

    function managePO() {
        eModal.iframe({
            title: 'Issue PO',
            url: '<?= COMPONENTS_PRIVATE ?>/v2/po/roshowpo.php?src=new&roid=<?php echo $roid;?>&shopid=<?php echo $shopid;?>',
            size: eModal.size.xl


        });

    }

    function emailRO() {

        // open the modal
        $('#emailinvmodal').modal('show')

    }


    function emailWO() {

        // open the modal
        $('#emailWOmodal').modal('show')

    }


    function sendWOEmail() {

        invpath = $('#invpath').val()


        sendto = encodeURIComponent($('#woemailto').val())
        emailsubject = encodeURIComponent($('#woemailsubject').val())
        emailmessage = encodeURIComponent($('#woemailmessage').val())

        ds = "updatecustomer=n&roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>&cid=<?php echo $CustomerID; ?>&sendfrom=<?php echo urlencode($_COOKIE['shopname']);?>&sendto=" + sendto + "&invpath=" + invpath + "&subject=" + emailsubject + "&message=" + emailmessage + "&shopemail=<?php echo urlencode($companyemail); ?>&ptype=WO"

        $.ajax({
            data: ds,
            url: "roemailinvoice.php",
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            },
            success: function (r) {
                //console.log(r)
                if (r == "success") {

                    sbalert("Tech Work Order Sent")
                    $('#emailWOmodal').modal('hide')
                } else if (r == "success|") {

                    $('#emailWOmodal').modal('hide')
                    sbalert("Tech Work Order Sent.  Reloading with new email address")
                    setTimeout(function () {
                        location.reload()
                    }, 2000)
                } else {
                    //console.log(r)
                }
                printit = $('#printit').val()
                if (printit == "yes") {
                    location.href = '<?= COMPONENTS_PRIVATE ?>/v2/wip/wip.php'
                }
            }
        });
    }


    function sendInvoiceEmail() {

        showLoader()
        $('.btn-md').attr('disabled', 'disabled')

        invpath = encodeURIComponent($('#invpath').val())
        if (invpath.length > 0) {

            sendto = encodeURIComponent($('#emailto').val())
            emailsubject = encodeURIComponent($('#emailsubject').val())
            emailmessage = encodeURIComponent($('#emailmessage').val())
            if ($("#updatecustomeremail").is(':checked')) {
                ucstr = "updatecustomer=y&"
            } else {
                ucstr = "updatecustomer=n&"
            }
            ds = ucstr + "roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>&cid=<?php echo $CustomerID; ?>&sendfrom=<?php echo urlencode($_COOKIE['shopname']);?>&sendto=" + sendto + "&invpath=" + invpath + "&subject=" + emailsubject + "&message=" + emailmessage + "&shopemail=<?php echo urlencode($companyemail); ?>"
            $.ajax({
                data: ds,
                url: "roemailinvoice.php",
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                },
                success: function (r) {

                    if (r == "success") {

                        sbalert("Invoice Sent")
                        $('#emailinvmodal').modal('hide')
                    } else if (r == "success|") {

                        $('#emailinvmodal').modal('hide')
                        sbalert("Invoice Sent.  Reloading with new email address")
                        setTimeout(function () {
                            location.reload()
                        }, 2000)
                    } else {
                        //console.log(r)
                    }
                    printit = $('#printit').val()
                    if (printit == "yes") {
                        location.href = '<?= COMPONENTS_PRIVATE ?>/v2/wip/wip.php'
                    }

                    hideLoader()
                    $('.btn-md').attr('disabled', false)
                }
            });

        } else {
            $.ajax({
                data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>",
                url: "<?= COMPONENTS_PUBLIC ?>/invoices/printpdfro.php",
                success: function (r) {
                    $('#invpath').val(r)
                    sendto = encodeURIComponent($('#emailto').val())
                    emailsubject = encodeURIComponent($('#emailsubject').val())
                    emailmessage = encodeURIComponent($('#emailmessage').val())
                    if ($("#updatecustomeremail").is(':checked')) {
                        ucstr = "updatecustomer=y&"
                    } else {
                        ucstr = "updatecustomer=n&"
                    }

                    ds = ucstr + "roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>&cid=<?php echo $CustomerID; ?>&sendfrom=<?php echo urlencode($_COOKIE['shopname']);?>&sendto=" + sendto + "&invpath=" + invpath + "&subject=" + emailsubject + "&message=" + emailmessage + "&shopemail=<?php echo urlencode($companyemail); ?>"

                    $.ajax({
                        data: ds,
                        url: "roemailinvoice.php",
                        success: function (r) {
                            if (r == "success") {

                                sbalert("Invoice Sent")
                                $('#emailinvmodal').modal('hide')
                            } else if (r == "success|") {
                                $('#emailinvmodal').modal('hide')
                                sbalert("Invoice Sent.  Reloading with new email address")
                                setTimeout(function () {
                                    location.reload()
                                }, 2000)
                            }

                            hideLoader()
                        }
                    });

                }
            });
        }

    }

    function sendCustomEmail() {
        customsendto = encodeURIComponent($('#customemailto').val())
        customemailsubject = encodeURIComponent($('#customemailsubject').val())
        customemailmessage = encodeURIComponent($('#customemailmessage').val())
        send_attachment = $("#customemail_attach_invoice").is(":checked")

        if (customsendto === '' || customemailsubject === "" || customemailmessage === "") {
            sbalert("You must enter all the required fields")
            return
        }

        $('.btn-md').attr('disabled', 'disabled')

        if ($("#customupdatecustomeremail").is(':checked')) {
            ucstr = "updatecustomer=y&"
        } else {
            ucstr = "updatecustomer=n&"
        }
        ds = ucstr + "roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>&cid=<?php echo $CustomerID; ?>&sendfrom=<?php echo urlencode($_COOKIE['shopname']);?>&sendto=" + customsendto + "&subject=" + customemailsubject + "&message=" + customemailmessage + "&shopemail=<?php echo urlencode($companyemail); ?>&send_attachment=" + send_attachment
        $.ajax({
            data: ds,
            url: "sendcustomemail.php",
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            },
            success: function (r) {
                if (r == "success") {
                    $('.btn-md').attr('disabled', false)
                    sbalert("Custom Email Sent")
                    $('#sendcustomupdatemodal').modal('hide')
                } else if (r == "success|") {
                    $('#sendcustomupdatemodal').modal('hide')
                    sbalert("Custom Email Sent. Reloading with new email address")
                    setTimeout(function () {
                        location.reload()
                    }, 2000)
                } else {
                    sbalert(r);
                    hideLoader();
                }
            }
        });
    }


    function printWO() {

        showLoader()

        $.ajax({
            data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>",
            url: "<?= COMPONENTS_PUBLIC ?>/invoices/printpdfwo.php",
            success: function (r) {

                $('#invpath').val(r)
                setTimeout(function () {
                    hideLoader()
                    eModal.iframe({
                        title: 'Tech Work Order',
                        url: '<?= COMPONENTS_PUBLIC ?>/invoices' + r,
                        size: eModal.size.xl,
                        buttons: [
                            {
                                text: 'Print',
                                style: 'primary',
                                close: false,
                                click: function () {
                                    contents = $("#emodal-box").find("iframe");
                                    contents[0].contentWindow.focus();
                                    contents[0].contentWindow.print();
                                }
                            },
                            <?php if($shopid == '14015'){?>
                            {text: 'E-Signature', style: 'warning', close: false, click: showESigWO},
                            {
                                text: 'Email',
                                style: 'primary',
                                close: true,
                                click: emailWO
                            },
                            <?php };?>

                        ]

                    });
                }, 1000)
            }
        });
    }

    function showOilChangeSticker() {
        eModal.iframe({
            title: "Oil Change Sticker",
            url: "../oil-change-stickers/oil-change-stickers.php?roid=<?php echo $roid; ?>",
            id: "oilChangeSticker"
        });
    }

    function printSignedRO(invpath) {

        eModal.iframe({
            title: 'Repair Order',
            url: invpath,
            size: eModal.size.xl,
            buttons: [
                {
                    text: 'Print',
                    style: 'primary',
                    close: false,
                    click: function () {
                        contents = $("#emodal-box").find("iframe");
                        contents[0].contentWindow.focus();
                        contents[0].contentWindow.print();
                    }
                },
                {
                    text: 'Email',
                    style: 'secondary',
                    close: true,
                    click: emailRO
                },

            ]

        });

    }

    function printRO() {

        if ("<?= strtolower($requiremileagetoprintro)?>" == 'yes' && $('#inmiles').val() == '') {
            sbalert("Mileage is Required");
            return false
        }

        showLoader()

        $.ajax({
            data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&i=new",
            url: "<?= COMPONENTS_PUBLIC ?>/invoices/printpdfro.php",
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            },
            success: function (r) {

                $('#invpath').val(r)
                setTimeout(function () {

                    hideLoader()

                    eModal.iframe({
                        title: 'Repair Order',
                        url: '<?= COMPONENTS_PUBLIC ?>/invoices' + r,
                        size: eModal.size.xl,
                        id: "printromodal",
                        buttons: [
                            {
                                text: 'Print',
                                style: 'primary',
                                close: false,
                                click: function () {
                                    contents = $("#printromodal").find("iframe");
                                    contents[0].contentWindow.focus();
                                    contents[0].contentWindow.print();
                                }
                            },

                            <?php if($Status != 'CLOSED'){?>

                            {text: 'E-Signature', style: 'secondary', close: false, click: showESig},
                            <?php
                            if ($merchantaccount == "cardknox"){
                            ?>
                            {text: 'Cardknox', style: 'secondary', close: true, click: requestSignature},
                            <?php
                            }elseif ($merchantaccount == "360"){
                            ?>
                            {text: '360 Payments', style: 'secondary', close: true, click: requestSignature},
                            <?php
                            }elseif ($merchantaccount == "stax"){
                            ?>
                            {text: 'Stax', style: 'secondary', close: true, click: requestSignature},
                            <?php
                            }elseif ($merchantaccount == "tnp"){
                            ?>
                            {text: 'TransNational', style: 'secondary', close: true, click: requestSignature},
                            <?php
                            }
                            ?>
                            {text: 'Scriptel', style: 'secondary', close: false, click: sigPad},

                            <?php }?>
                            {
                                text: 'Email',
                                style: 'secondary',
                                close: true,
                                click: emailRO
                            }


                        ]

                    });

                    $("#printromodal").on("hidden.mdb.modal", function () {
                        exitIfClosed();
                    });

                }, 1000)
            }
        });
    }

    function exitIfClosed() {

        printit = $('#printit').val()
        if (printit == "yes") {
            location.href = '<?= COMPONENTS_PRIVATE ?>/v2/wip/wip.php'
        }

    }

    function showESig() {

        invpath = $('#invpath').val()
        eModal.iframe({
            title: 'E-Sign the RO',
            url: '<?= COMPONENTS_PRIVATE ?>/esig-v2/touchsignature.php?path=' + invpath + '&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>',
            size: eModal.size.lg
        });

    }

    function showESigWO() {

        invpath = $('#invpath').val()
        eModal.iframe({
            title: 'E-Sign the Tech Work Order',
            url: '<?= COMPONENTS_PRIVATE ?>/esig-v2/touchsignature.php?path=' + invpath + '&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&ptype=WO',
            size: eModal.size.lg
        });

    }

    function addSigToRO(ptype = '') {

        ds = "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&sig=" + $('#signame').val() + "&pdf=" + $('#invpath').val()
        $.ajax({
            url: "<?= COMPONENTS_PRIVATE ?>/esig-v2/signaturesavetoro.php",
            data: ds,
            success: function (r) {
                if (ptype == 'WO')
                    $('#invpath').val('<?= $_SERVER['DOCUMENT_ROOT'] ?>/sbp/savedinvoices/<?php echo $shopid; ?>/<?php echo $roid; ?>/' + r)
                else
                    $('#invpath').val(r)
                eModal.iframe({
                    title: (ptype == 'WO' ? 'Tech Work Order' : 'Repair Order'),
                    url: r,
                    size: eModal.size.xl,
                    buttons: [
                        {
                            text: 'Print',
                            style: 'primary',
                            close: false,
                            click: function () {
                                contents = $("#emodal-box").find("iframe");
                                contents[0].contentWindow.focus();
                                contents[0].contentWindow.print();
                            }
                        },
                        {
                            text: 'Email',
                            style: 'secondary',
                            close: true,
                            click: (ptype == 'WO' ? emailWO : emailRO)
                        }

                    ]

                });
            }
        });

    }

    function historyClose() {

        setTimeout(function () {
            location.reload()
        }, 500)
    }

    function winPopUp(URL) {
        h = screen.availHeight - 80
        w = screen.availWidth - 20
        str = 'addressbar=0,toolbar=0,scrollbars=1,location=0,statusbar=0,menubar=0,resizable=yes,screenX=0,screenY=0,top=0,left=0,maximize=1,'
        str = str + 'height=' + h + ', width=' + w
        //eval('var ' + id+'=window.open(URL, id,str )')
        var mywin = window.open(URL, "ewin", str)
        wintimer = setInterval(function () {
            if (mywin.closed) {
                location.reload()
                //console.log(localStorage.getItem("epicormsg"))
            }
        }, 1000)

    }

    function launchEpicor() {

        path = '<?= INTEGRATIONS ?>/epicor-v2/main.php?shopid=<?php echo $shopid; ?>&om=' + $('#epicortype').val() + '&vin=<?php echo str_replace('"', '\"', $Vin); ?>&supp=' + $('#epicorsupplier').val() + '&roid=<?php echo $roid; ?>'
        //console.log(path)
        winPopUp(path)

    }

    function launchNexpart() {

        unpw = $('#nexpartcreds').val()
        rar = unpw.split("|")
        un = rar[0]
        pw = rar[1]
        // cert url  http://www.nexpart.com/extpart.php...
        // qa url http://www.nexpartqa.com/nexlink_cert2/extpart.php
        path = 'http://www.nexpart.com/extpart.php?rev=4.0&clientversion=2.2&requesttype=launch&sms_form=X&provider=NEXLINKCSBTECH&pwd=' + pw + '&nexpartuname=' + un + '&vin=<?php echo str_replace('"', '\"', strtoupper($Vin)); ?>&identifier=<?php echo $shopid; ?>-<?php echo $roid; ?>&webpost=<?= SBP; ?>nexpart/return.php'
        //console.log(path)
        winPopUp(path)
    }


    function showOrdering(t) {

        if (t == "epicor") {
            $('#epicorselect').toggle();
            $('#epicorbutton').toggle();
            $('#nexpartbutton').toggle();
            $('#worldpacbutton').toggle();
            $('#partstechbutton').toggle();
            $('#repairlinkbutton').toggle();
            if (!$('#epicorsupplier').hasClass('select-initialized'))
                new mdb.Select(document.getElementById('epicorsupplier'))
            if (!$('#epicortype').hasClass('select-initialized'))
                new mdb.Select(document.getElementById('epicortype'))
        } else if (t == "nexpart") {
            $('#nexpartselect').toggle();
            $('#epicorbutton').toggle();
            $('#nexpartbutton').toggle();
            $('#worldpacbutton').toggle();
            $('#partstechbutton').toggle();
            $('#repairlinkbutton').toggle();
            if (!$('#nexpartcreds').hasClass('select-initialized'))
                new mdb.Select(document.getElementById('nexpartcreds'))

        } else if (t == "partstech") {
            $('#partstechselect').toggle();
            $('#epicorbutton').toggle();
            $('#nexpartbutton').toggle();
            $('#worldpacbutton').toggle();
            $('#partstechbutton').toggle();
            $('#repairlinkbutton').toggle();
        }

    }

    function cancelOrdering(t) {

        if (t == "epicor") {
            $('#epicorselect').toggle();
            $('#epicorbutton').toggle();
            $('#nexpartbutton').toggle();
            $('#worldpacbutton').toggle();
            $('#partstechbutton').toggle();
            $('#repairlinkbutton').toggle();

        } else if (t == "nexpart") {
            $('#nexpartselect').toggle();
            $('#epicorbutton').toggle();
            $('#nexpartbutton').toggle();
            $('#worldpacbutton').toggle();
            $('#partstechbutton').toggle();
            $('#repairlinkbutton').toggle();

        } else if (t == "partstech") {
            $('#partstechselect').toggle();
            $('#epicorbutton').toggle();
            $('#nexpartbutton').toggle();
            $('#worldpacbutton').toggle();
            $('#repairlinkbutton').toggle();

        } else if (t == "worldpac") {

        }
    }

    function calcDiscountPercent(v) {

        subtotal = <?php echo $subtotal + $DiscountAmt; ?>;
        if (!$.isNumeric(v) && v.length > 0) {
            sbalert("You must enter a number in order enter a discount")

        } else if ($.isNumeric(v) && $.isNumeric(subtotal)) {

            $('#editdiscountamount').css("color", "red")

        } else if (v.length == 0) {
            $('#editdiscountamount').val(0).css("color", "red")

        }
    }

    function saveCommunication() {

        comm = encodeURIComponent($('#communication').val())
        by = '<?php echo str_replace("'", "\'", $_COOKIE['usr']);?>'
        ds = "t=comm&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&comm=" + comm + "&by=" + by
        $.ajax({
            type: "post",
            url: "saveData.php",
            data: ds,
            success: function (r) {
                $('#commmodal').modal('hide')
                $('#commlogdiv').html(r)
                $('#communication').val('')
            }
        });


    }

    function saveDisclosure() {
        rodisc = encodeURIComponent($('#rodisc').val())
        warrdisc = encodeURIComponent($('#warrdisc').val())
        ds = "t=disc&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&warrdisc=" + warrdisc + "&rodisc=" + rodisc
        $.ajax({
            type: "post",
            url: "saveData.php",
            data: ds,
            success: function (r) {
                $('#disclosuremodal').modal('hide')
            }
        });

    }

    function setIssueStatus(s, comid) {

        tests = s.toUpperCase()
        raws = s
        switch (tests) {
            case "PENDING":
                stattouse = "pending";
                break;
            case "SCHEDULED":
                stattouse = "scheduled";
                break;
            case "APPROVED":
                stattouse = "approved";
                break;
            case "PARTS ORDERED":
                stattouse = "partsordered";
                break;
            case "DECLINED":
                stattouse = "declined";
                break;
            case "ASSEMBLY":
                stattouse = "assembly";
                break;
            case "JOB COMPLETE":
                stattouse = "jobcomplete";
                break;

        }

        s = encodeURIComponent(s)
        currstat = $('#status-' + comid).attr('data-status')
        currstat = currstat.toLowerCase()


        if (stattouse !== "declined" && currstat !== "declined") {
            showLoader()
            $.ajax({
                data: "t=comstatus&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&comid=" + comid + "&status=" + s,
                type: "post",
                url: "saveData.php",
                success: function (r) {
                    if (r == "success") {
                        statbutton = '#status-' + comid
                        $(statbutton).html(raws).removeClass().addClass('btn btn-' + stattouse + ' dropdown-toggle fixed-width-button')
                        $('#accstatus-' + comid).html(raws)
                        $(statbutton).attr('data-status', raws)
                        $('#accordion-bg-' + comid).removeClass().addClass('accordion-item ro-status-' + stattouse)
                        hideLoader()


                    }
                }
            });
        }

        if (stattouse == "declined" && currstat !== "declined") {
            // decline this issue and move to recommended
            $('#statusmodal').modal('hide')

            sbconfirm("Decline Concern", "Declining this issue will move all parts and labor to Recommended Repairs.  Are you sure?", function () {

                showLoader()

                $.ajax({
                    data: "t=declined&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&comid=" + comid + "&status=" + s,
                    type: "post",
                    url: "saveData.php",
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    },
                    success: function (r) {
                        if (r == "success") {
                            location.reload()
                        } else {
                            sbalert(r)
                            hideLoader()
                            $('#statusmodal').modal('hide')
                        }

                    }
                });

            });
        }

        if (stattouse !== "declined" && currstat == "declined") {

            showLoader()
            roid = "<?php echo $roid; ?>";
            shopid = "<?php echo $shopid; ?>";
            $.ajax({
                data: "t=get_declined_recid&roid=" + roid + "&shopid=" + shopid + "&comid=" + comid,
                url: "saveData.php",
                type: "post",
                success: function (r) {
                    recid = r
                    ds = "t=undecline&roid=" + roid + "&shopid=" + shopid + "&recid=" + recid + "&comid=" + comid + "&newstat=" + encodeURIComponent(raws)
                    $.ajax({
                        data: ds,
                        url: "recrepairsedit.php",
                        type: "get",
                        success: function (r) {
                            if (r == "success") {
                                location.reload()
                            }
                        }
                    });

                }
            });
        }
    }

    function selectStatus(comid) {

        var currstat = $('#status-' + comid).attr('data-status')

        $('#statusmodal').modal('show')
        $('#activecomid').val(comid)
        $('#currstat').val(currstat)
    }

    function scrollToIssue(elem) {
        // Ensure the target element exists
        const targetElement = document.getElementById(elem);
        if (!targetElement) return;

        // Get the fixed menu height to adjust scroll position accordingly
        // Add a small buffer to prevent the element from being hidden under the menu
        const menuOffset = fixedMenu && fixedMenu.classList.contains('d-md-flex') ?
            fixedMenu.offsetHeight + 20 : 20;

        // Calculate the target position
        const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - menuOffset;

        // Save current position in case we need to restore it
        const startPosition = window.pageYOffset;

        // Smooth scroll with proper offset accounting for fixed header
        $([document.documentElement, document.body]).animate({
            scrollTop: targetPosition
        }, {
            duration: 500,
            // After scrolling completes, ensure the fixed menu is properly positioned
            complete: function() {
                // Force a recalculation of the fixed menu position
                handleFixedMenu();
            }
        });
    }


    function pnCheck(partnum) {

        $.ajax({
            data: "t=checkinv&roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>&pn=" + partnum,
            url: "saveData.php",
            type: "post",
            success: function (r) {
                json = JSON.parse(r)
                $.each(json, function (i, item) {
                    if (item == "yes") {
                        $('.pncheck').each(function () {
                            txt = $(this).html()
                            if (txt.indexOf("(P)") >= 0) {
                                pn = $(this).find('span.partnumber').html().trim()
                                thishtml = $(this).html()
                                if (pn == i && thishtml.indexOf("IN-STOCK") == -1) {
                                    $(this).append(" <span style='color:var(--red);'>IN-STOCK</span>")
                                }
                            }
                        });
                    } else if (item == "last") {
                        $('.pncheck').each(function () {
                            txt = $(this).html()
                            if (txt.indexOf("(P)") >= 0) {
                                pn = $(this).find('span.partnumber').html().trim()
                                thishtml = $(this).html()
                                if (pn == i && thishtml.indexOf("IN-STOCK") == -1) {
                                    $(this).append("<br><span style='color:var(--red);'>IN-STOCK-LAST</span>")
                                }
                            }
                        });

                    } else if (item == "oos") {
                        $('.pncheck').each(function () {
                            txt = $(this).html()
                            if (txt.indexOf("(P)") >= 0) {
                                pn = $(this).find('span.partnumber').html().trim()
                                thishtml = $(this).html()
                                if (pn == i && thishtml.indexOf("IN-STOCK") == -1) {
                                    $(this).append("<br><span style='color:var(--red);'>OUT-OF-STOCK</span>")
                                }
                            }
                        });

                    }

                });
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });

    }

    function showHistory() {

        eModal.iframe({
            title: 'Vehicle History <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Click any line for more detail"></i>',
            url: 'history.php?roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>&vin=<?php echo str_replace('"', '\"', $Vin); ?>&vid=<?php echo $VehID; ?>',
            size: eModal.size.xl,
            buttons: [
                {
                    text: 'Print',
                    style: 'primary',
                    close: false,
                    click: function () {
                        contents = $("#emodal-box").find("iframe");
                        contents[0].contentWindow.focus();
                        contents[0].contentWindow.print();
                    }
                }

            ]

        });

    }

    function showUploads() {

        eModal.iframe({
            title: 'Upload<?= $Status == 'CLOSED' ? 'ed' : ''?> Pics',
            url: 'uploads.php?roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>&status=<?= $Status?>',
            size: eModal.size.xl,
            buttons: [
                {
                    text: 'Print',
                    style: 'primary',
                    close: false,
                    click: function () {
                        contents = $("#emodal-box").find("iframe");
                        contents[0].contentWindow.location.href = 'printpics.php?roid=<?php echo $roid; ?>&status=<?= $Status?>';

                    }
                }
            ]

        });

    }

    function editCustomer(cid) {
        eModal.iframe({
            title: 'Edit Customer <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="PLEASE NOTE: This Edit function is to make small changes to one or two fields, not change the customer who brought in this vehicle. Any changes made here will affect the underlying customer information permanently."></i>',
            url: '<?= COMPONENTS_PRIVATE ?>/customer-v2/customer-edit-ro.php?roid=<?php echo $roid; ?>&cid=<?php echo $CustomerID; ?>&oshopid=<?php echo $origshopid; ?>',
            size: eModal.size.xl,
            loading: false

        });

    }

    function editVehicle(vid) {
        eModal.iframe({
            title: 'Edit Vehicle <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="PLEASE NOTE: This vehicle is connected to a specific customer in Shop Boss. Changing ALL values here to will affect all vehicle history. This Edit function is to make small changes to one or two fields."></i>',
            url: '<?= COMPONENTS_PRIVATE ?>/customer-v2/customer-vehicle-edit-ro.php?roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>&cid=<?php echo $CustomerID; ?>&vid=<?php echo $VehID; ?>&oshopid=<?php echo $origshopid; ?>',
            size: eModal.size.xl,
            loading: false

        });
    }

    function showVehicle(vid) {
        eModal.iframe({
            title: 'Vehicle Information',
            url: '<?= COMPONENTS_PRIVATE ?>/customer-v2/customer-vehicle-edit-ro.php?roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>&cid=<?php echo $CustomerID; ?>&vid=<?php echo $VehID; ?>&oshopid=<?php echo $origshopid; ?>&readonly=true',
            size: eModal.size.xl,
            loading: false

        });
    }

    function saveToRO() {
        ds = "t=customer&cid=<?php echo $CustomerID; ?>&roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>"
        $.ajax({
            data: ds,
            type: "post",
            url: "savedata.php",
            success: function (r) {
                if (r == "success") {
                    location.reload()
                }
            }
        });
    }

    function saveMiles() {
        milesin = $('#savemilesin').val()
        milesout = $('#savemilesout').val()
        $.ajax({
            data: "t=miles&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&milesin=" + milesin + "&milesout=" + milesout,
            type: "post",
            url: "savedata.php",
            success: function (r) {
                if (r == "success") {
                    $('#milesin').html(milesin)
                    $('#inmiles').val(milesin)
                    $('#milesout').html(milesout)
                    $('#outmiles').val(milesout)
                    $('#milesmodal').modal('hide')
                }
            }
        });
    }

    function openMiles() {

        $('#milesmodal').modal('show')

    }

    function editPO() {
        $('#pomodal').modal('show')
        setTimeout(function () {
            $('#editponumber').focus()
        }, 700);
    }

    function savePO() {

        ponumber = $('#editponumber').val()
        ds = "t=ponumber&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&ponumber=" + ponumber
        $.ajax({
            data: ds,
            url: "saveData.php",
            type: "post",
            success: function (r) {
                if (r == "success") {
                    $('#ponumber').html(ponumber)
                    $('#pomodal').modal('hide')
                }
            }
        })

    }

    function deletePart(partid, comid) {

        sbconfirm("Delete Part", "Are you sure you want to delete this part?", function () {

            showLoader()

            $.ajax({

                data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&partid=" + partid + "&comid=" + comid,
                url: "deletepart.php",
                type: "post",
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                },
                success: function (r) {
                    if (r != "success") {
                        sbalert("Error Deleting part")
                        hideLoader()
                    } else {
                        location.reload()
                    }
                }

            });
        });

    }

    function deleteLabor(laborid, comid) {

        sbconfirm("Delete Labor", "Are you sure you want to delete this labor?", function () {

            showLoader()

            $.ajax({

                data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&laborid=" + laborid + "&comid=" + comid,
                url: "deletelabor.php",
                type: "post",
                success: function (r) {
                    if (r == "success") {
                        location.reload()
                    } else {
                        sbalert("Error Deleting Labor")
                        hideLoader()
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });
        });

    }

    function deleteSublet(subletid, comid) {

        sbconfirm("Delete Sublet", "Are you sure you want to delete this sublet?", function () {

            showLoader()

            $.ajax({

                data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&subletid=" + subletid + "&comid=" + comid,
                url: "deletesublet.php",
                type: "post",
                success: function (r) {
                    if (r == "success") {
                        location.reload()
                    } else {
                        sbalert("Error Deleting Sublet")
                        hideLoader()
                    }
                }

            });
        });

    }

    function setStatus(s) {

        checkdigit = s.substring(0, 1)
        if ($.isNumeric(checkdigit)) {
            st = s.substring(1)
        } else {
            st = s
        }

        displaystatus = st.substring(0, 1).toUpperCase() + st.substring(1).toLowerCase()
        displaycolor = $("i[data-status='" + s + "']").css('background-color')

        if ('<?= $sourcerequired?>' == 'yes' && $('#currentsource').val().toLowerCase() == 'none') {
            sbalert("You must select a Source to change the RO status");
            return false
        }

        if (st != "CLOSED") {

            showLoader()

            $.ajax({
                data: "t=status&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&s=" + s,
                type: "post",
                url: "savedata.php",
                success: function (r) {

                    if (r == 'success')
                        $('#statusSelect').html("<i class='dot align-self-center mx-2' style='background-color:" + displaycolor + "'></i> " + displaystatus + "<div class='col d-flex justify-content-end'><span class='select-arrow'></span></div>");
                    else
                        sbalert(r)

                    hideLoader()

                }
            });
        } else {
            // close the RO

            <?php
            if ($shopid == "5605") {
                echo "requirets = 'yes';\r\n";
            } else {
                echo "requirets = 'no';\r\n";
            }
            ?>
            requirebalancero = "<?php echo $requirebalancero; ?>".toLowerCase();
            requirepayments = "<?php echo $requirepayments; ?>".toLowerCase();
            requiresource = "<?php echo $requiresource; ?>".toLowerCase();
            requireoutmileage = "<?php echo $requireoutmileage; ?>".toLowerCase();
            requireinspectiontofinal = "<?php echo $requireinspectiontofinal; ?>".toLowerCase();
            inspectioncomplete = "<?php echo $inspectioncomplete; ?>".toLowerCase();
            requiremileagetoprintro = "<?php echo strtolower($requiremileagetoprintro)?>"
            outmiles = $('#outmiles').val()
            balance = Math.abs(<?php echo $balancero; ?>);
            pmts = <?php echo $totalpayments; ?>;
            source = $('#currentsource').val();
            //console.log(source)

            if (requirets == "yes" && $('#lents').val() == "yes") {
                sbalert("Tech Story is Required for All Issues");
                return false

            }

            if (requirepayments == "yes" && pmts == 0 && balance != 0) {
                sbalert("You must receive a payment to close the RO");
                return false
            }

            if (requiresource == "yes" && source == "NONE") {
                sbalert("You must enter a Source to close the RO");
                return false
            }

            if (requireoutmileage == "yes" && outmiles == "") {
                sbalert("You must enter Miles Out to close the RO");
                return false
            }

            if (requirebalancero == "yes" && balance != 0) {
                sbalert("Your RO is not balanced.  You cannot close until the balance is zero");
                return false
            }

            if (requireinspectiontofinal == "yes" && '<?= $inspectionstarted?>' == 'no') {
                sbalert("You must perform an inspection to close the RO");
                return false
            }


            sbconfirm("Closing RO", "Ready to close the RO.  Are you sure?", function () {

                showLoader()

                $.ajax({
                    data: "overridestatusdate=<?php echo $overridestatusdate; ?>&t=closero&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&s=" + s + "&balance=" + balance,
                    type: "post",
                    url: "savedata.php",
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    },
                    success: function (r) {

                        if (r == "success") {

                            if (requiremileagetoprintro == "no" || (requiremileagetoprintro == 'yes' && $('#inmiles').val() != '')) {
                                $('#closedromodal').modal('show')
                            } else
                                location.href = '<?= COMPONENTS_PRIVATE ?>/v2/wip/wip.php'
                        } else
                            sbalert(r)

                        hideLoader()
                    }
                });
            });
        }

    }

    function setROType(r) {
        showLoader()
        $.ajax({
            data: "t=rotype&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&r=" + r,
            type: "post",
            url: "savedata.php",
            success: function (ret) {
                if (ret == "success") {
                    location.reload();
                }
                hideLoader()
            }
        });
    }

    function setWriter(w) {
        wo = encodeURIComponent(w)
        $.ajax({
            data: "t=writer&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&w=" + wo,
            type: "post",
            url: "savedata.php",
            success: function (r) {
                if (r == "success") {
                    $('#writer').html(w).append(' &nbsp;<i style="float:right" class="fa fa-chevron-down"></i>')
                }
            }
        });
    }

    function setSource(s) {

        $('#currentsource').val(s)
        so = encodeURIComponent(s)

        if (so.toLowerCase().indexOf("referral") > -1) {

            $('#refnamemodal').modal('show')

            setTimeout(function () {
                $('#refname').focus()
            }, 500);

        } else {

            $.ajax({
                data: "t=source&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&s=" + so,
                type: "post",
                url: "savedata.php",
                success: function (r) {
                }
            });
        }
    }


    function editLabor(laborid, comid) {

        $('#issues-parent').css("z-index", "1040")
        setCookie('comid', comid, 1)
        eModal.iframe({
            title: 'Edit Labor',
            url: 'editlabor.php?laborid=' + laborid + '&shopid=<?php echo $shopid . "&roid=" . $roid?>&comid=' + comid,
            size: eModal.size.lg
        });

    }

    function editPart(partid, comid) {

        <?php if($editparts == 'YES'){?>

        $('#issues-parent').css("z-index", "1040")
        setCookie('comid', comid, 1)
        eModal.iframe({
            title: 'Edit Part',
            url: 'editpart.php?partid=' + partid + '&shopid=<?php echo $shopid . "&roid=" . $roid?>&comid=' + comid,
            size: eModal.size.xl
        });

        <?php }?>

    }

    function editSublet(subletid, comid) {

        <?php if($editsublet == 'YES'){?>

        $('#issues-parent').css("z-index", "1040")
        setCookie('comid', comid, 1)
        eModal.iframe({
            title: 'Edit Sublet',
            url: 'editsublet.php?subletid=' + subletid + '&shopid=<?php echo $shopid . "&roid=" . $roid?>&comid=' + comid,
            size: eModal.size.lg

        });

        <?php }?>

    }

    function showExtras(v) {

        $('.extras').hide();
        $('#' + v).show();
        setCookie('shown_extras_<?= $roid ?>', v, 5)

    }

    function addPart(comid) {
        setCookie('comid', comid, 1)
        eModal.iframe({
            title: 'Add a Part to this RO',
            url: 'addpart.php?shopid=<?php echo $shopid . "&roid=" . $roid?>&comid=' + comid,
            size: eModal.size.xl
        });
    }

    function addCannedJob(comid) {

        eModal.iframe({
            title: 'Add a Canned Job to this RO',
            url: 'cannedjobs.php?shopid=<?php echo $shopid . "&roid=" . $roid?>&comid=' + comid,
            size: eModal.size.lg

        });
    }

    function closeCannedJobs() {
        location.reload()
    }


    function partDetail(partid, invtype, comid) {
        setCookie('comid', comid, 1);
        eModal.iframe({
            title: 'Part Detail',
            url: 'partdetail.php?invtype=' + invtype + '&partid=' + partid + '&shopid=<?php echo $shopid . "&roid=" . $roid?>&comid=' + comid,
            size: eModal.size.xl

        });

    }

    function addLabor(comid) {
        setCookie('comid', comid, 1)
        eModal.iframe({
            title: 'Add Labor',
            url: 'addlabor.php?shopid=<?php echo $shopid . "&roid=" . $roid?>&comid=' + comid,
            size: eModal.size.lg

        });
    }

    function reloadRO() {
        // Save current scroll position before reloading
        const scrollPos = window.pageYOffset || document.documentElement.scrollTop;
        sessionStorage.setItem('roScrollPosition', scrollPos);

        // Force a true reload by adding a cache-busting parameter
        const timestamp = new Date().getTime();
        location.href = 'ro.php?roid=<?php echo $roid; ?>&_=' + timestamp;
    }

    function addSublet(comid) {
        setCookie('comid', comid, 1)
        eModal.iframe({
            title: 'Add Sublet',
            url: 'addsublet.php?shopid=<?php echo $shopid . "&roid=" . $roid?>&comid=' + comid,
            size: eModal.size.lg
        });
    }

    function addRev() {

        revamt = encodeURIComponent($('#revamt').val())
        revdt = $('#revdt').val()
        revby = encodeURIComponent($('#revby').val())
        revphone = encodeURIComponent($('#revphone').val())
        revappmethod = encodeURIComponent($('#revappmethod').val())
        revappby = encodeURIComponent($('#revappby').val())
        ds = "t=rev&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&revamt=" + revamt + "&revdt=" + revdt + "&revby=" + revby + "&revphone=" + revphone + "&revappmethod=" + revappmethod + "&revappby=" + revappby

        if ('<?= $requirerevapp?>' == 'yes' && (revappmethod == '' || revappby == '')) {
            sbalert("You must select and mention Approval method");
            return false
        }

        if ($.isNumeric(revamt)) {
            $('.btn-md').attr('disabled', 'disabled')
            $.ajax({
                data: ds,
                type: "post",
                url: "saveData.php",
                success: function (r) {
                    $('#revisiondiv').html(r)
                    $('#revmodal').modal('hide')
                    $('.btn-md').attr('disabled', false)
                }
            });
        } else {
            sbalert("Please only enter numbers and decimals in the Revision Amount box")
        }
    }

    function saveWarranty() {

        warrmos = $('#editwarrmos').val()
        warrmiles = $('#editwarrmiles').val()
        $.ajax({
            data: "t=warr&roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>&warrmos=" + warrmos + "&warrmiles=" + warrmiles,
            url: "saveData.php",
            type: "post",
            success: function (r) {
                if (r == "success") {
                    $('#warrmiles').html(warrmiles)
                    $('#warrmos').html(warrmos)
                    $('#warrmodal').modal('hide')
                }
            }
        })

    }

    function techStory(comid) {
        eModal.iframe({
            title: 'Add / Modify Tech Story',
            url: 'techstory.php?shopid=<?php echo $shopid . "&roid=" . $roid?>&comid=' + comid,
            size: window.innerHeight > 1000 ? eModal.size.lg : eModal.size.xl,
            loading: false

        });
    }


    function showCFP() {

        $('.btn-cfp').hide()
        $('#cfpresponse').html('')
        $('#financeavail').html('')
        $('#cfpmodal .row').hide()
        showLoader()

        $('#cfpapplyurldynamic').remove()

        ds = "t=cfpcheck&merchantId=<?= $cfpid?>&phone=<?= $cfpphone?>&altphone=<?= $cfpaltphone?>&zip=<?= $customerzip?>&firstName=<?= addslashes($customerfirst)?>&lastName=<?= addslashes($customerlast)?>&street=<?= addslashes($CustomerAddress)?>&state=<?= $customerstate?>&city=<?= $customercity?>&email=<?= $email?>&Amount=<?= $balancero ?>&roid=<?= $roid?>"
        $.ajax({
            data: ds,
            type: "post",
            dataType: 'JSON',
            url: "saveData.php",
            success: function (r) {

                hideLoader()

                $('#cfpmodal').modal('show')

                if (r.status == 'error') {
                    $('#cfpresponse').html("<div class='text-center text-primary'>" + r.response + "</div>")
                    $('#btn-cfp-cancel').show()
                } else
                    $('#cfpresponse').html("<div class='text-center text-primary'>" + r.response + "</div>")

                if (r.status == 'found') {
                    $('#cfptype').val(r.type)
                    $('#cfpphone').val(r.phone)

                    if (r.type != 'synchrony') {
                        $('#easypayappid').val(r.appid)
                        if (r.type == 'wisetack')
                            $('#cfpmodal .modal-footer').prepend("<button onclick=\"processEasypay('yes')\" class='btn btn-warning pull-left btn-cfp'>ReApply in CFP</button>")
                        showEasypay()
                    } else {
                        $('#financeavail').html(r.credits)
                        $('#btn-scfp-pay').show()
                        $('#btn-cfp-cancel').show()
                        $('#syncpay').show()
                        setTimeout(function () {
                            $('#scpamount').focus()
                        }, 500);
                    }
                } else if (r.status == 'notfound') {
                    $('#cfpapplyurl').attr('href', r.applyurl)
                    $('#applycfp').show()
                    $('#btn-cfp-cancel').show()
                }

            }
        });
    }

    function showEasypay() {
        $('#applycfp').hide()
        if ($('#cfptype').val() == 'easypay')
            $('#cfpidcheckdiv').show()
        else
            $('#cfpidcheckdiv').hide()
        $('#easypay').show()
        $('#btn-ecfp-pay').show()
        $('#btn-cfp-cancel').show()
    }

    function calcDiscount(v) {

        subtotal = <?php echo $subtotal + $DiscountAmt; ?>;
        if (!$.isNumeric(v) && v.length > 0) {
            sbalert("You must enter a number in order enter a discount");
        } else if ($.isNumeric(v) && $.isNumeric(subtotal)) {
            discpercent = v / 100
            discamt = subtotal * discpercent
            $('#editdiscountamount').val(discamt.toFixed(2)).css("color", "red")
        } else if (v.length == 0) {
            $('#editdiscountamount').val(0).css("color", "red")
        }

    }

    function saveFees() {

        $('.btn-md').attr('disabled', 'disabled')

        ds = $('#feesform').serialize() + "&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&t=fees"
        $.ajax({
            type: "post",
            data: ds,
            url: "saveData.php",
            success: function (r) {
                if (r != 'success') {
                    sbalert("Error Updating Fees")
                } else {
                    location.reload()
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }

        });

    }

    function setCookie(cname, cvalue, exdays) {
        var d = new Date();
        d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
        var expires = "expires=" + d.toUTCString();
        document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
    }

    function isJson(str) {
        try {
            JSON.parse(str);
        } catch (e) {
            return false;
        }
        return true;
    }


    function getCookie(cname) {
        var name = cname + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == ' ') {
                c = c.substring(1);
            }
            if (c.indexOf(name) == 0) {
                return c.substring(name.length, c.length);
            }
        }
        return "";
    }

    $(document).ready(function () {

        $("#ta_checkall").on('click', function () {
            if ($(this).is(":checked"))
                $("input[name='ta_complaints']").prop('checked', true);
            else
                $("input[name='ta_complaints']").prop('checked', false);
        });

        $("input.ta_complaints").change(function () {
            console.log("ta checkbox changed", $(this).prop('checked'));
            if ($("input.ta_complaints:not(:checked)").length <= 0) {
                $("#ta_checkall").prop('checked', true);
            } else {
                $("#ta_checkall").prop('checked', false);
            }
        })

        $("#tech_assign_table tr td:not(:first-child)").click(function () {
            var tr = $(this).closest('tr')
            var checkbox = $(tr).find('input:checkbox');
            $(checkbox).prop('checked', !$(checkbox).prop('checked')).change();
        })

        <?php
        if (isset($_GET['showpayments'])) {
            echo "showExtras('pmts')\r\n";
        }
        ?>
        var clipboard = new ClipboardJS('#copybtn');

        clipboard.on('success', function (e) {
            sbalert("Copied")
            setTimeout(function () {
                $('.modal').modal('hide')
            }, 1000)
            e.clearSelection();
        });

        setTimeout(function () {

            <?php
            if ($Status != 'CLOSED' && ($rofetch != "none" || $laborfetch != "none" || $allpartsfetch != "none" || $taxpartsfetch != "none" || $subletfetch != "none" || $roupdate != "none" || $pmtfetch != "none")) {
                echo "sbalert('One or more database updates did not complete and totals may not be accurate.  Please contact support with this RO number.  $rofetch|$laborfetch|$allpartsfetch|$taxpartsfetch|$subletfetch|$roupdate|$pmtfetch');";
            }
            ?>

            w = $(window).width() * .75
            h = $(document).height() * .75

            // check for mycarfax account and vehicle
            /*$.ajax({
                data: "email=<?php echo $email; ?>&shopid=<?php echo $shopid; ?>&vin=<?php echo str_replace('"', '\"', $Vin); ?>",
                url: "<?= INTEGRATIONS ?>/carfax/checkemail.php",
                type: "post",
                success: function (r) {
                    //console.log(r)
                    if (isJson(r)) {
                        jo = JSON.parse(r)
                        ce = jo.accountExists
                        ve = jo.vehicleExists
                        //console.log(ce+":"+ve)
                        if (ce == true && ve == true) {
                            $('#mycarfaxuser').show()
                        }
                        if (ce == true && ve == false) {
                            $('#mycarfaxaddveh').show()
                        }
                        if (ce == false) {
                            $('#mycarfaxadduser').show()
                        }
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }

            });*/



            <?php
            if ($picktechs == "yes" || $pickissues == 'yes'){
            ?>
            $('#quotemodal').modal('show')
            <?php
            }

            if(!empty($ptapikey)){
            ?>

            $.ajax({
                data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&type=getopentire",
                url: "<?= INTEGRATIONS ?>/partstech-v2/postpartstech.php",
                type: "post",
                success: function (r) {
                    if (r == 'yes') {
                        eModal.iframe({
                            title: 'Partstech Tire Confirmation',
                            url: "<?= INTEGRATIONS ?>/partstech-v2/partstech.php?shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&type=tire",
                            size: eModal.size.xl,

                        });
                    }
                }
            });

            <?php }?>

        }, 2500)

        <?php
        if (isset($_GET['schid']))
        {?>

        if (localStorage.getItem("schid") == '<?= $_GET['schid']?>') {
            // update the schedule id with the new ro number
            ds = "t=updateschedule&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&schid=" + localStorage.getItem("schid")
            //console.log(ds)
            localStorage.removeItem("schid")
            $.ajax({
                data: ds,
                url: "saveData.php",
                type: "post",
                success: function (r) {
                    if (r == "success") {
                        localStorage.removeItem("schid")
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            })
        }

        <?php }?>

        <?php
        if (isset($_GET['showpmts'])) {
            echo "setTimeout(function(){showExtras('pmts')},500)";
        }
        ?>

        $("#communication").keyup(function () {
            $("#commlogcounter").text("Characters left: " + (500 - $(this).val().trim().length));
        });


        <?php if($_COOKIE['mode'] == 'full' && $Status != 'CLOSED' && $showcfp == 'yes' && !empty($cfpid)){?>

        ds = "t=cfpcheck&merchantId=<?= $cfpid?>&phone=<?= $cfpphone?>&altphone=<?= $cfpaltphone?>&zip=<?= $customerzip?>&firstName=<?= addslashes($customerfirst)?>&lastName=<?= addslashes($customerlast)?>&street=<?= addslashes($CustomerAddress)?>&state=<?= $customerstate?>&city=<?= $customercity?>&email=<?= $email?>&Amount=<?= $balancero ?>&roid=<?= $roid?>"
        $.ajax({
            data: ds,
            type: "post",
            dataType: 'JSON',
            url: "saveData.php",
            success: function (r) {
                if (r.response != null)
                    $('#cfpalert').html("<div><div class='text-primary' id='cfpmessage'>" + r.response + "</div><hr /></div>").show()
            }
        })


        <?php }?>


        <?php if($_COOKIE['mode'] == 'full' && $Status != 'CLOSED'){?>

        if ("<?= $showwaiting?>" == 'yes' && "<?= $waiter?>" == "select")
            $('#waitingmodal').modal('show');

        $('#revdt').datetimepickerbs({
            format: "MM/DD/YYYY hh:mm a",
            sideBySide: true,
            keepOpen: true,
        });


        $('#promisedate').datetimepickerbs({
            format: "MM/DD/YYYY hh:mm a",
            sideBySide: true,
            keepOpen: true,
        }).on('dp.hide', function (e) {
            console.log(e)
            d = moment(e.date).format("YYYY-MM-DD HH:mm") + ":00"
            $.ajax({
                data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&t=setpromisedate&d=" + encodeURIComponent(d),
                type: "post",
                url: "saveData.php",
                success: function (r) {
                    //console.log(r)
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            })

        });

        <?php }?>

        $('#motorModal,#newmotorModal').on('hidden.bs.modal', function () {
            closeMotor()
        })

        <?php
        if ($_COOKIE['mode'] == 'full' && $showinstockparts == "yes"){
        ?>
        pnlist = ""
        $('.pncheck').each(function () {
            txt = $(this).html()
            if (txt.indexOf("(P)") >= 0 && $(this).attr('data-type') != 'NON') {
                pn = $(this).find('span.partnumber').html().trim()
                console.log(pn)
                pnlist += pn + ","
            }
        });

        pnCheck(pnlist)
        <?php
        }
        ?>

        $('.accordion').on('hidden.bs.collapse', function (event) {

            const id = $(this).attr('id').replace('accordion-vi-', '')
            let color = $('#status-' + id).css('background-color')

            <?php
            if (!empty($_COOKIE['theme']) && $_COOKIE['theme'] == 1){
            ?>
            let acstatus = $('#accstatus-' + id).text().trim()
            if (acstatus == "Scheduled") {
                color = "rgb(151 122 178)"
            } else if (acstatus == "Job Complete") {
                color = "rgb(25 137 241)"
            } else if (acstatus == "Declined") {
                color = "rgb(116 115 115)"
            } else if (acstatus == "Assembly") {
                color = "rgb(77 165 63)"
            }
            <?php
            }
            ?>

            $('#accstatus-' + id).css('color', color)
            $('#accstatus-' + id).show()

        });

        $('#collapseAll').on('click', function () {

            var $this = $(this)

            const type = $this.attr('data-type')

            if (type == 'up') {
                $(".card .collapse").each(function () {
                    $(this).collapse('hide')
                });

                $this.removeClass('fa-chevron-up').addClass('fa-chevron-down')
                $this.attr('data-type', 'down')
            } else if (type == 'down') {
                $(".card .collapse").each(function () {
                    $(this).collapse('show')
                });

                $this.removeClass('fa-chevron-down').addClass('fa-chevron-up')
                $this.attr('data-type', 'up')
            }

        });

        $('.accordion').on('shown.bs.collapse', function (event) {

            const id = $(this).attr('id').replace('accordion-vi-', '')
            $('#accstatus-' + id).hide()

        });

        if ("<?= $collapseissues?>" == "yes") {
            $('.accordion').each(function () {

                const id = $(this).attr('id').replace('accordion-vi-', '')
                let color = $('#status-' + id).css('background-color')

                <?php
                if (!empty($_COOKIE['theme']) && $_COOKIE['theme'] == 1){
                ?>
                let acstatus = $('#accstatus-' + id).text().trim()
                if (acstatus == "Scheduled") {
                    color = "rgb(151 122 178)"
                } else if (acstatus == "Job Complete") {
                    color = "rgb(25 137 241)"
                } else if (acstatus == "Declined") {
                    color = "rgb(116 115 115)"
                } else if (acstatus == "Assembly") {
                    color = "rgb(77 165 63)"
                }
                <?php
                }
                ?>

                $('#accstatus-' + id).css('color', color)

            })

        }


        $('body').on('click', '#printromodal .btn-close', function () {
            exitIfClosed()

        })

        <?php if($Status != 'CLOSED' && $lyftcount > 0){?>

        setLyftInterval()

        <?php }?>

        $('.drag-el').each(function () {
            var $dragEl = $(this);
            var modalId = $dragEl.closest(".modal").attr('id');
            // console.log('Modal ID:', modalId);

            $dragEl.draggable({
                dragHandle: ".move_icon",
                draggingClass: "dragging",
            });

            var savedPosition = JSON.parse(localStorage.getItem("pos_" + modalId));
            //   console.log('Applying saved position for', modalId, ':', savedPosition);

            if (savedPosition) {
                $(this).css({
                    top: savedPosition.top + 'px',
                    left: savedPosition.left + 'px',
                });
            } else {

                var $modalClone = $(this).clone().css({
                    visibility: 'hidden',
                    display: 'block',
                    position: 'absolute',
                    top: -9999,
                    left: -9999
                });

                $('body').append($modalClone);

                var modalHeight = $modalClone.outerHeight();
                var modalWidth = $modalClone.outerWidth();

                $modalClone.remove();

                var windowHeight = $(window).height();
                var windowWidth = $(window).width();

                var centeredTop = Math.max((windowHeight - modalHeight) / 2, 0);
                var centeredLeft = Math.max((windowWidth - modalWidth) / 2, 0);
                //       console.log("setting v canter", windowHeight, centeredTop, windowWidth, centeredLeft);

                $(this).css({
                    top: centeredTop + 'px',
                    left: centeredLeft + 'px',
                });
            }

            $dragEl.on("end.mdb.draggable", function (e) {

                var newPosition = this.getBoundingClientRect();
                // Ensure the modal doesn't go outside the viewport
                var windowWidth = $(window).width();
                var windowHeight = $(window).height();
                var modalWidth = $(this).outerWidth();
                var modalHeight = $(this).outerHeight();

                var top = Math.max(0, Math.min(newPosition.top, windowHeight - modalHeight));
                var left = Math.max(0, Math.min(newPosition.left, windowWidth - modalWidth));

                var savedPosition = {
                    top: Math.abs(Math.round(top)),
                    left: Math.abs(Math.round(left)),
                };
                localStorage.setItem("pos_" + modalId, JSON.stringify(savedPosition));

            });

        });

        $('.drag-el').each(function () {
            var $dragEl = $(this);
            var modalId = $dragEl.closest(".modal").attr('id');
            // console.log('Modal ID:', modalId);

            $dragEl.draggable({
                dragHandle: ".move_icon",
                draggingClass: "dragging",
            });

            var savedPosition = JSON.parse(localStorage.getItem("pos_" + modalId));
            //   console.log('Applying saved position for', modalId, ':', savedPosition);

            if (savedPosition) {
                $(this).css({
                    top: savedPosition.top + 'px',
                    left: savedPosition.left + 'px',
                });
            } else {

                var $modalClone = $(this).clone().css({
                    visibility: 'hidden',
                    display: 'block',
                    position: 'absolute',
                    top: -9999,
                    left: -9999
                });

                $('body').append($modalClone);

                var modalHeight = $modalClone.outerHeight();
                var modalWidth = $modalClone.outerWidth();

                $modalClone.remove();

                var windowHeight = $(window).height();
                var windowWidth = $(window).width();

                var centeredTop = Math.max((windowHeight - modalHeight) / 2, 0);
                var centeredLeft = Math.max((windowWidth - modalWidth) / 2, 0);
                //       console.log("setting v canter", windowHeight, centeredTop, windowWidth, centeredLeft);

                $(this).css({
                    top: centeredTop + 'px',
                    left: centeredLeft + 'px',
                });
            }

            $dragEl.on("end.mdb.draggable", function (e) {

                var newPosition = this.getBoundingClientRect();
                // Ensure the modal doesn't go outside the viewport
                var windowWidth = $(window).width();
                var windowHeight = $(window).height();
                var modalWidth = $(this).outerWidth();
                var modalHeight = $(this).outerHeight();

                var top = Math.max(0, Math.min(newPosition.top, windowHeight - modalHeight));
                var left = Math.max(0, Math.min(newPosition.left, windowWidth - modalWidth));

                var savedPosition = {
                    top: Math.abs(Math.round(top)),
                    left: Math.abs(Math.round(left)),
                };
                localStorage.setItem("pos_" + modalId, JSON.stringify(savedPosition));

            });

        });

    });

    $(document).ready(function(){

        console.log(getCookie('shown_extras_<?= $roid ?>'))
        selected_extra = getCookie('shown_extras_<?= $roid ?>');
        if (selected_extra != ''){
            $('.extras').hide();
            $("#"+selected_extra).show();
        }

        $("textarea.limit_length[maxlength]").on("input", function () {
            $(this).siblings(".form-helper").text((this.maxLength - this.value.length) + " characters remaining");
        });
    })


    function transferVehicle() {

        eModal.iframe({
            title: 'Transfer Vehicle',
            url: "<?= COMPONENTS_PRIVATE ?>/v2/customer/customer-select-transfer.php?shopid=<?php echo $shopid; ?>&cid=<?php echo $CustomerID; ?>&vid=<?= $VehID?>&fromro='yes'",
            size: eModal.size.xl,

        });

    }

    function showtechpaidlogs() {

        eModal.iframe({
            title: 'Tech Payment Logs',
            url: "techpaidlog.php?shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>",
            size: eModal.size.xl,

        });

    }

    function showMyCarfax() {

        mycarfax = "<?php echo $mycarfax; ?>";
        if (mycarfax == "eula") {
            $('#mycarfaxeula').modal('show')
        } else if (mycarfax == "yes") {
            $('#mycarfaxsignup').modal('show')
        } else if (mycarfax == "no") {
            $('#mycarfaxeuladeclined').modal('show')
        }

    }

    function myCarfaxAccept() {

        $.ajax({
            data: "t=mycarfax&shopid=<?php echo $shopid; ?>&user=<?php echo addslashes($_COOKIE['usr']); ?>&roid=<?php echo $roid; ?>",
            url: "saveData.php",
            type: "post",
            success: function (r) {
                if (r == "success") {
                    // now we get the pitch info for the customer
                    $('#mycarfaxeula').modal('hide')
                    $('#mycarfaxterms').modal('hide')
                    $('#mycarfaxsignup').modal('show')
                }

            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });

    }

    function showMyCarfaxTerms() {

        eModal.iframe({
            title: 'myCarfax Terms and Conditions',
            url: "https://service.carfax.com/csn/csnTerms",
            size: eModal.size.xl,

        });


    }

    function myCarfaxSignup() {

        email = $('#mycarfaxemail').val()
        vin = $('#mycarfaxvin').val()
        ischecked = $('#updatecarfaxcustomer').is(':checked')
        if (ischecked === true) {
            ischecked = "yes"
        } else {
            ischecked = "no"
        }
        //console.log(ischecked)
        if (email.length == 0 || vin.length == 0) {
            sbalert("Email and VIN are required")
        } else {
            $.ajax({
                data: "ischecked=" + ischecked + "&cid=<?php echo $CustomerID; ?>&t=mycarfaxsignup&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&email=" + email + "&vin=" + vin,
                url: "<?= INTEGRATIONS ?>/carfax/mycarfaxsignup.php",
                type: "post",
                success: function (r) {
                    //console.log(r)
                    jo = JSON.parse(r)
                    // accountExists":true,"token":null,"accountId":********,"vehicleCreated":false,"vehicleError":"The VIN you entered is already associated with this account","accountError":null
                    accountcreated = jo.accountCreated
                    accountexists = jo.accountExists
                    vehiclecreated = jo.vehicleCreated
                    vehicleerror = jo.vehicleError
                    accounterror = jo.accountError
                    sbalert("Complete")

                    if (accountcreated == true) {
                        if (vehiclecreated == true) {
                            sbalert("Customer and vehicle were successfully registered");
                        }

                        if (vehiclecreated == false) {
                            sbalert("Customer was registered but the vehicle returned the following error:\n\n" + vehicleerror);
                        }

                    } else {
                        if (accountexists == true) {
                            if (vehiclecreated == true) {
                                sbalert("Vehicle was successfully added");
                            } else {
                                if (vehicleerror == "The VIN you entered is already associated with this account") {
                                    sbalert("Customer and vehicle are already registered with myCarfax");
                                } else {
                                    sbalert("Customer account exists but the vehicle returned the following error:\n\n" + vehicleerror);
                                }
                            }
                        } else {
                            sbalert(accounterror);
                        }
                    }

                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }

            });
        }
    }

    function myCarfaxUserExists() {
        sbalert("This customer and vehicle are already registered with myCarfax")
    }

    function updateWaiting(w) {

        $.ajax({
            data: "t=waiting&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&w=" + w,
            url: "saveData.php",
            type: "post",
            success: function (r) {
                if (r == "success") {
                    $('#waitingmodal').modal("hide")
                    setTimeout(function () {
                        location.reload()
                    }, 200)
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            },
        });

    }

    function saveRODate() {

        nd = $('#changerodateval').val()
        pwd = encodeURIComponent($('#changerodatepwd').val())

        showLoader()

        $.ajax({

            data: "t=changerodate&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&empid=<?php echo $empid; ?>&nd=" + nd + "&pwd=" + pwd,
            url: "saveData.php",
            type: "post",
            success: function (r) {
                if (r == "success") {
                    location.reload()
                } else if (r == "pwd") {
                    sbalert("Invalid Password")
                } else {
                    console.log(r)
                }

                hideLoader()
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }


        });

    }

    function showMassChange() {

        eModal.iframe({
            title: 'Vehicle Issues Mass Status Change',
            url: "massstatuschange.php?shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>",
            size: eModal.size.xl,
            id: "mcEmodal"
        });

        setTimeout(function () {
            $("#mcEmodal").on("hidden.bs.modal", function () {
                location.reload();
            });
        }, 300);

    }

    function reopenRO() {
        sbconfirm("Re-open RO", "Re-Opening the RO will recalculate the RO, so if your Shop Fees and/or Taxes have changed, it will change the balance of the RO. Do you still want to continue?", function () {

            $('.btn-md').attr('disabled', 'disabled')
            showLoader();
            ds = "shopid=<?php echo $shopid; ?>&ronumber=<?php echo $roid; ?>&stat=" + $('#reopenstatus').val()
            console.log(ds)
            $.ajax({
                data: ds,
                type: "post",
                url: "../settings/reopenrofiles/reopenro.php",
                success: function (r) {

                    if (r.indexOf("success|") >= 0) {
                        location.reload()
                    } else {
                        sbalert("RO not found <br> The RO or Tag number you entered does not exists");
                        hideLoader();
                        $('.btn-md').attr('disabled', false)
                    }
                }
            });

        })
    }

    function showParts() {
        var table = '<table class="sbdatatable w-100"><thead><tr><th>Part#</th><th>Description</th><th>Qty/Price</th><th>Total</th></thead><tbody>'

        $("tr.partsrow").each(function () {

            var partnumber = $(this).find('.partnumber').html()
            var partdesc = $(this).find('.partdesc').html()
            var partqty = $(this).find('.partqty').html()
            var partprice = $(this).find('.partprice').html()

            table += "<tr><td>" + partnumber + "</td><td>" + partdesc + "</td><td>" + partqty + "</td><td>" + partprice + "</td></tr>"
        })

        table += "</tbody></table>"

        $('#roinfolLabel').html("Parts")
        $('#roinfomodal #infocontent').html(table)
        $('#roinfomodal').modal('show')
    }

    function showLabor() {
        var table = '<table class="sbdatatable w-100"><thead><tr><th>Tech</th><th>Labor</th><th>Hours</th><th>Total</th></thead><tbody>'

        $("tr.laborsrow").each(function () {

            var tech = $(this).find('.labortech').html()
            var labor = $(this).find('.labordesc').html()
            var hours = $(this).find('.laborhours').html()
            var total = $(this).find('.labortotal').html()

            table += "<tr><td>" + tech + "</td><td>" + labor + "</td><td>" + hours + "</td><td>" + total + "</td></tr>"
        })

        table += "</tbody></table>"

        $('#roinfolLabel').html("Labor")
        $('#roinfomodal #infocontent').html(table)
        $('#roinfomodal').modal('show')
    }

    function showSublets() {
        var table = '<table class="sbdatatable w-100"><thead><tr><th>Invoice#</th><th>Description</th><th>Price</th><th>Total</th></thead><tbody>'

        $("tr.subletrow").each(function () {

            var inv = $(this).find('.subletinv').html()
            var desc = $(this).find('.subletdesc').html()
            var price = $(this).find('.subletprice').html()
            var total = $(this).find('.sublettotal').html()

            table += "<tr><td>" + inv + "</td><td>" + desc + "</td><td>" + price + "</td><td>" + total + "</td></tr>"
        })

        table += "</tbody></table>"

        $('#roinfolLabel').html("Sublet")
        $('#roinfomodal #infocontent').html(table)
        $('#roinfomodal').modal('show')
    }

    function showFees() {
        var table = '<table class="sbdatatable w-100"><thead><tr><th>Fee</th><th>Amount</th></thead><tbody>'

        $("tr.feerow").each(function () {

            var fee = $(this).find('.feelabel').html()
            var total = $(this).find('.feeamount').html()

            table += "<tr><td>" + fee + "</td><td>$" + total + "</td></tr>"
        })

        table += "</tbody></table>"

        $('#roinfolLabel').html("Fees")
        $('#roinfomodal #infocontent').html(table)
        $('#roinfomodal').modal('show')
    }

    function formatDecimalHours(decimal) {
        let totalMinutes = Math.round(decimal * 60);
        let hours = Math.floor(totalMinutes / 60);
        let minutes = totalMinutes % 60;
        return `${hours > 0 ? hours + ' hr' + (hours > 1 ? 's' : '') : ''}${hours > 0 && minutes > 0 ? ' ' : ''}${minutes > 0 ? minutes + ' min' + (minutes > 1 ? 's' : '') : ''}` || '0 min';
    //    return `${hours > 0 ? hours + ' h': ''}${hours > 0 && minutes > 0 ? ' ' : ''}${minutes > 0 ? minutes + ' m' : ''}` || '0 m';
    }

    function load_timeclock_diff(){
        $.ajax({
            data: "roid=<?php echo $roid; ?>",
            url: "getlabortime.php",
            type: "get",
            dataType: "json",
            success: function (response) {
                if (Array.isArray(response)) {
                    $.each(response, function (index, item) {
                        let complaintId = item.complaintid;
                        let laborHours = parseFloat(item.labor_hours) || 0;
                        let timeclockHours = parseFloat(item.timeclock_hours) || 0;
                        let remaining_hours = laborHours - timeclockHours;

                        let percentage = ((timeclockHours / laborHours) * 100).toFixed(2);

                        let laborReadable = formatDecimalHours(laborHours);
                        let timeclockReadable = formatDecimalHours(timeclockHours);

                        let remaininghoursReadable = formatDecimalHours(remaining_hours);

                        let container = "#tcdiff-"+complaintId;
                        if ($(container).length > 0){
                            $(container).html("Approved Time : "+laborReadable + "<br />Remaining Time: "+remaininghoursReadable);
                            $(container).removeClass(["text-success", "text-warning", "text-danger"]);
                            if (percentage < 75){
                                $(container).addClass("text-success");
                            }
                            if (percentage >= 75 && percentage < 90){
                                $(container).addClass("text-warning");
                            }
                            if ( percentage >= 90){
                                $(container).addClass("text-danger");
                            }

                            $(container).tooltip({
                                title: "Labor Hours: "+laborHours.toFixed(2)+"<br />Clocked Hours : "+timeclockHours.toFixed(2)+"<br />Percentage :"+percentage+"%",
                                html : true
                            });

                            $("#tchrs-"+complaintId).text(timeclockHours.toFixed(2))
                        } else {
                            console.log("container not found");
                        }
                    })
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });
    }
    <?php if (isset($showtimeclockoncomplaint) && strtolower($showtimeclockoncomplaint) == "yes"){ ?>
    $(document).ready(function(){
        load_timeclock_diff();
        setInterval(load_timeclock_diff,30000);
    })
    <?php } ?>
</script>
