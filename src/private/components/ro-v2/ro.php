<?php
$component = getComponent();
include getRulesGlobal($component);
include getHeadGlobal($component);
?>
<!-- body display set to show once the page is loaded -->
<body style="display:none">
<?php
include getPreheaderGlobal($component);
if (!isset($_GET['nohead'])) {
    include getHeaderGlobal($component);
    include getMenuGlobal($component);
}
include getPostheaderGlobal($component);
?>

<!-- Content-->
<main id="RO" class="mt-0 <?= isset($_GET['nohead']) ? 'm-0' : '' ?>">
    <div class="row">
        <div class="col-xxl-<?= $_COOKIE['mode'] == 'full' ? '9' : '12' ?> col-xl-12">
            <div class="row mb-3">
                <!-- Customer Information -->
                <div class="col-lg-4 mb-3 d-flex align-items-stretch">
                    <div class="card card-square w-100">
                        <div class="card-body d-flex flex-column pb-0 no-hover-effect">
                            <div class="row align-items-center p-2 pb-2 border-bottom">
                                <div class="col-9">
                                    <h5 class="d-inline card-title">Customer</h5>
                                </div>
                                <div class="col-3 text-end">
                                    <?php if ($_COOKIE['mode'] == 'full' && $Status != 'CLOSED') { ?>
                                        <a href="javascript:void(null)" data-mdb-toggle="tooltip"
                                           onclick="editCustomer(<?php echo $CustomerID; ?>)" data-mdb-placement="left"
                                           title="Edit"><i class="fas fa-edit"></i></a>
                                    <?php } ?>
                                </div>
                            </div>
                            <div class="card-text m-0 pt-3">
                                <table class="table table-borderless table-sm ro-card-text m-0">
                                    <tbody>
                                    <tr>
                                        <th width="30%" ondblclick="calendarSet()">Name
                                            <?php
                                            if ($_COOKIE['mode'] == 'full' && $customerdiscountamount > 0)
                                                echo "<span style='color:var(--primary);cursor:pointer' onclick='applyDiscount(\"$customerdiscountamount\",\"$discounttype\")'> (*$customerdiscountamount*)</span>";
                                            ?>
                                        </th>
                                        <td><?= substr(ucwords(strtolower($Customer)), 0, 20) ?></td>
                                    </tr>
                                    <tr>
                                        <th>Address</th>
                                        <td><?= $showcustomerinfo == 'YES' ? ucwords(strtolower($CustomerAddress)) : '' ?></td>
                                    </tr>
                                    <tr>
                                        <th>CSZ</th>
                                        <td><?= $showcustomerinfo == 'YES' ? ucwords(strtolower(implode(', ', $cszarray))) : '' ?></td>
                                    </tr>
                                    <tr>
                                        <th>Home</th>
                                        <td><?= $showcustomerinfo == 'YES' ? formatPhone(strtoupper($CustomerPhone)) : '' ?></td>
                                    </tr>
                                    <tr>
                                        <th>Work</th>
                                        <td><?= $showcustomerinfo == 'YES' ? formatPhone(strtoupper($CustomerWork)) . (!empty($ext) ? " Ext. " . $ext : '') : '' ?></td>

                                    </tr>
                                    <tr>
                                        <th>Cell</th>
                                        <td><a href="javascript:void(null)"
                                               onclick="sendTextMessage('<?php echo $CellPhone; ?>')"><?= $showcustomerinfo == 'YES' ? formatPhone(strtoupper($CellPhone)) : '' ?></a>
                                        </td>
                                    </tr>
                                    <?php if ($_COOKIE['mode'] == 'full') { ?>
                                        <tr>
                                            <th>Email</th>
                                            <td><a href="javascript:void(null)"
                                                   onclick="sendEmailMessage()"><?= strtolower($email) ?></a></td>
                                        </tr>
                                        <tr>
                                            <th>Contact</th>
                                            <td><?= strtoupper($contact) ?></td>
                                        </tr>
                                        <tr>
                                            <th>Pay Type</th>
                                            <td><?= ucwords(strtolower($customertype)) ?></td>
                                        </tr>
                                        <?php
                                        if (!empty($spousename) or !empty($spousecell) or !empty($spousework)) {
                                            ?>
                                            <tr>
                                                <th>Other Contact</th>
                                                <th><?= strtoupper($spousename) ?></th>
                                            </tr>
                                            <?php
                                            if (!empty($spousecell)) {
                                                ?>
                                                <tr>
                                                    <th>Cell</th>
                                                    <th><?= strtoupper($spousecell) ?></th>
                                                </tr>
                                                <?php
                                            }
                                            if (!empty($spousework)) {
                                                ?>
                                                <tr>
                                                    <th>Work</th>
                                                    <th><?= strtoupper($spousework) ?></th>
                                                </tr>
                                                <?php
                                            }
                                        }
                                    }
                                    ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Vehicle Information -->
                <div class="col-lg-4 mb-3 d-flex align-items-stretch">
                    <div class="card card-square w-100">
                        <div class="card-body d-flex flex-column pb-0 no-hover-effect">
                            <div class="row align-items-center p-2 pb-2 border-bottom">
                                <div class="col-9">
                                    <h5 class="d-inline card-title">Vehicle</h5>
                                </div>
                                <div class="col-3 text-end">
                                    <?php if ($Status != 'CLOSED') { ?>
                                        <?php if ($shopid != '16462') { ?>
                                            <a href="javascript:void(null)" class="d-inline me-2"
                                               data-mdb-toggle="tooltip" onclick="editTP()" data-mdb-placement="left"
                                               title="Tire Pressure">
                                                <i class="fa fa-tire-pressure-warning"></i></a>
                                        <?php }
                                        if ($_COOKIE['mode'] == 'full') {
                                            ?>
                                            <a href="javascript:void(null)" class="d-inline" data-mdb-toggle="tooltip"
                                               data-mdb-placement="left" title="Edit"
                                               onclick="editVehicle(<?php echo $VehID; ?>)"><i class="fas fa-edit"></i></a>
                                        <?php }
                                    } else { ?>
                                        <a href="javascript:void(null)" class="d-inline" data-mdb-toggle="tooltip"
                                           data-mdb-placement="left" title="View"
                                           onclick="showVehicle(<?php echo $VehID; ?>)"><i class="fas fa-eye"></i></a>
                                    <?php } ?>
                                </div>
                            </div>
                            <div class="card-text m-0 pt-3">
                                <table class="table table-borderless table-sm  ro-card-text m-0">
                                    <tbody>
                                    <tr>
                                        <th width="30%">Vehicle
                                            <?php
                                            if (strlen($fleetno) > 0)
                                                echo " (<b>#" . $fleetno . "</b>)";
                                            ?>
                                        </th>
                                        <td><?= substr(strtoupper($VehInfo), 0, 40) ?></td>
                                    </tr>
                                    <tr>
                                        <th><?= ucwords($enginelabel) ?></th>
                                        <td><?= strtoupper($VehEngine) ?></td>
                                    </tr>
                                    <tr>
                                        <th><?= $translabel ?>/<?= $drivelabel ?></th>
                                        <td><?= (!empty($VehTrans) && !empty($DriveType)) ? strtoupper($VehTrans . " / " . $DriveType) : strtoupper($VehTrans . $DriveType) ?></td>

                                    </tr>
                                    <tr>
                                        <th><?= $vinlabel ?></th>
                                        <td>
                                            <?php if (!empty($Vin)) { ?>
                                                <a id="copybtn" href="javascript:void(null)"
                                                   data-clipboard-action="copy"
                                                   data-clipboard-text="<?php echo strtoupper($Vin); ?>"
                                                   data-mdb-toggle="tooltip" data-mdb-placement="left" title="Copy"><i
                                                            class="fas fa-copy"></i>
                                                    <?php
                                                    $vinlen = strlen($Vin);
                                                    if ($vinlen = 17) {
                                                        for ($i = 0; $i <= $vinlen; $i++) {
                                                            $char = substr($Vin, $i, 1);
                                                            if ($i == 9) {
                                                                echo "<span style='font-weight:bold;color:var(--primary)'>";
                                                            }
                                                            echo $char;
                                                        }

                                                        echo "</span>";
                                                    }
                                                    ?></a>
                                            <?php } ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th><?= $licenselabel ?></th>
                                        <td><?= strtoupper($vehlicense) ?></td>
                                    </tr>
                                    <?php if ($_COOKIE['mode'] == 'full') { ?>
                                        <tr>
                                            <th><?= $statelabel ?></th>
                                            <td><?= strtoupper($vehstate) ?></td>
                                        </tr>
                                    <?php } ?>
                                    <tr>
                                        <th><?= $colorlabel ?></th>
                                        <td><?= ucwords(strtolower(($color))) ?></td>
                                    </tr>
                                    <tr>
                                        <th><?= ucwords(strtolower($milesinlabel)) ?></th>
                                        <td><?php if ($Status != 'CLOSED' && $editmilesin == 'YES') { ?><i class="fas fa-pen-square"
                                                                                                           onclick="openMiles()"></i> <?php } ?>
                                            <span
                                                    id="milesin"><?= strtoupper($VehicleMiles) ?></span><input
                                                    type="hidden" id="inmiles"
                                                    value="<?php echo strtoupper($VehicleMiles); ?>"></td>
                                    </tr>
                                    <tr>
                                        <th><?= ucwords(strtolower($milesoutlabel)) ?></th>
                                        <td>
                                            <?php if ($Status != 'CLOSED' && $editmilesout == 'YES') { ?><i class="fas fa-pen-square"
                                                                                                            onclick="openMiles()"></i> <?php } ?>
                                            <span
                                                    id="milesout"><?= $MilesOut ?></span><input type="hidden"
                                                                                                id="outmiles"
                                                                                                value="<?php echo strtoupper($MilesOut); ?>">
                                        </td>
                                    </tr>
                                    <?php if ($_COOKIE['mode'] == 'full') { ?>
                                        <tr>
                                            <th>Tag/Hat #</th>
                                            <td><?php if ($Status != 'CLOSED') { ?><i class="fas fa-pen-square"
                                                                                      data-mdb-toggle="modal"
                                                                                      data-mdb-target="#tagmodal"></i> <?php } ?><?= strtoupper($tagnumber) ?>
                                            </td>
                                        </tr>
                                    <?php } ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- RO Information -->
                <div class="col-lg-4 mb-3 d-flex align-items-stretch">
                    <div class="card card-square w-100">
                        <div class="card-body d-flex flex-column pb-0 no-hover-effect overflow-visible">
                            <div class="row align-items-center p-2 pb-2 border-bottom">
                                <div class="col-11">
                                    <h5 class="d-inline card-title ">RO</h5>
                                </div>
                            </div>
                            <div class="card-text m-0 pt-3">
                                <table class="table table-borderless table-sm ro-card-text m-0">
                                    <tbody>
                                    <tr>
                                        <th width="30%">Status</th>
                                        <td>
                                            <?php
                                            $rostatuses = array();
                                            $stmt = "select colorcode,status,isdefault from rostatus where shopid = ? order by displayorder";
                                            if ($query = $conn->prepare($stmt)) {
                                                $query->bind_param("s", $shopid);
                                                $query->execute();
                                                $r = $query->get_result();
                                                while ($rs = $r->fetch_array()) {
                                                    if ($rs['isdefault'] == 'yes' && is_numeric(substr($rs['status'], 0, 1)))
                                                        $displaystatus = substr($rs['status'], 1);
                                                    else
                                                        $displaystatus = $rs['status'];

                                                    $rostatuses[strtolower($rs['status'])] = array('color' => $rs['colorcode'], 'displaystatus' => $displaystatus);
                                                }

                                            }
                                            ?>

                                            <?php if ($Status != 'CLOSED' && $changerostatus == 'YES') { ?>
                                                <button class="btn btn-secondary w-100 dropdown-toggle col d-flex justify-content-start p-0 m-0 btn-input"
                                                        type="button" id="statusSelect" data-mdb-toggle="dropdown"
                                                        aria-expanded="false"><i
                                                            class='dot align-self-center mx-2' style="background-color: <?= isset($rostatuses[strtolower($Status)]) ? $rostatuses[strtolower($Status)]['color'] : '#cccccc' ?>;"></i> <?= ucwords(strtolower($status)) ?>
                                                    <div class="col d-flex justify-content-end"><span
                                                                class="select-arrow"></span></div>
                                                </button>

                                                <ul class="dropdown-menu" aria-labelledby="statusSelect">
                                                    <?php if (!empty($rostatuses)) {
                                                        foreach ($rostatuses as $key => $arr) {
                                                            ?>
                                                            <li>
                                                                <a href="javascript:void(null)" class="dropdown-item" onclick="setStatus('<?= strtoupper($key) ?>')"><i class='dot me-2' data-status="<?= strtoupper($key) ?>" style="background-color: <?= $arr['color'] ?>;"></i><?= ucwords(strtolower($arr['displaystatus'])) ?>
                                                                </a>
                                                            </li>
                                                        <?php }
                                                    } ?>
                                                </ul>


                                            <?php } else echo("<i class='dot align-self-center me-1' style='background-color: " . $rostatuses[strtolower($Status)]['color'] . "'></i> " . ucwords(strtolower($status))); ?>

                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Type</th>
                                        <td>
                                            <?php
                                            $rotypes = array();
                                            $stmt = "select colorcode,rotype from rotype where shopid = ? order by rotype";
                                            if ($query = $conn->prepare($stmt)) {
                                                $query->bind_param("s", $shopid);
                                                $query->execute();
                                                $r = $query->get_result();
                                                while ($rs = $r->fetch_array()) {
                                                    if ($rs['rotype'] == 'No Approval') $rs['colorcode'] = '#000000';
                                                    $rotypes[strtolower($rs['rotype'])] = $rs['colorcode'];
                                                }
                                            }
                                            ?>

                                            <?php if ($Status != 'CLOSED' && $changerotypes == 'YES') { ?>


                                                <button class="btn btn-secondary w-100 dropdown-toggle col d-flex justify-content-start p-0 m-0 btn-input"
                                                        type="button" id="rotypeSelect" data-mdb-toggle="dropdown"
                                                        aria-expanded="false"><i class='dot align-self-center mx-2'
                                                                                 style="background-color:<?= $rotypes[strtolower($ROType)] ?>"></i> <?= ucwords(strtolower($ROType)) ?>
                                                    <div class="col d-flex justify-content-end"><span
                                                                class="select-arrow"></span></div>
                                                </button>

                                                <ul class="dropdown-menu" aria-labelledby="rotypeSelect">
                                                    <?php
                                                    if (!empty($rotypes)) {
                                                        foreach ($rotypes as $key => $val) {
                                                            ?>
                                                            <li><a href="javascript:void(null)" class="dropdown-item"
                                                                   onclick="setROType('<?= $key ?>','<?= $val ?>')"><i
                                                                            class='dot align-self-center me-2'
                                                                            style="background-color:<?= $val ?>"></i><?= ucwords(strtolower($key)) ?>
                                                                </a></li>
                                                            <?php
                                                        }
                                                    }
                                                    ?>
                                                </ul>

                                            <?php } else echo("<i class='dot align-self-center me-2' style='background-color:" . $rotypes[strtolower($ROType)] . "'></i>" . ucwords(strtolower($ROType))); ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Date In</th>
                                        <td><?php $DateIn = date_create($DateIn);
                                            echo strtoupper(date_format($DateIn, 'm/d/Y')) . ($Status != 'CLOSED' ? ' ' . $TimeIn : '') ?></td>
                                    </tr>
                                    <tr>
                                        <th>Status Date</th>
                                        <td><?php $StatusDate = date_create($StatusDate);
                                            if ($_COOKIE['mode'] == 'full' && $Status == 'CLOSED' && $changerodate == "yes")
                                                echo("<span class='text-primary' data-mdb-toggle='modal' data-mdb-target='#changerodatemodal'>" . date_format($StatusDate, 'm/d/Y') . "</span>");
                                            else
                                                echo strtoupper(date_format($StatusDate, 'm/d/Y')) . " <b>(" . $edisplaytime . ")</b>"; ?></td>
                                    </tr>
                                    <?php if ($Status != 'CLOSED') { ?>
                                        <tr>
                                            <th>Promise Date</th>
                                            <td>
                                                <?php if ($_COOKIE['mode'] == 'full') { ?>
                                                    <div class="form-outline" id="promisedateWrap" style="position: relative">
                                                        <input type="text"
                                                               class="form-control"
                                                               id="promisedate"
                                                               value="<?= !empty($datetimepromised) ? date('m/d/Y, g:i A', strtotime($datetimepromised)) : '' ?>">
                                                    </div>
                                                <?php } elseif (!empty($datetimepromised)) echo(date('m/d/Y, g:i A', strtotime($datetimepromised))); ?>
                                            </td>
                                        </tr>
                                    <?php } ?>
                                    <tr>
                                        <th>Comments</th>
                                        <?php if ($Status != 'CLOSED' && $vieweditcomments == 'YES') { ?>
                                            <td data-mdb-toggle="modal" data-mdb-target="#commentmodal">
                                                <i class="fas fa-regular fa-comment-lines"></i>
                                            </td>
                                        <?php } else echo("<td>" . $_COOKIE['mode'] == 'full' ? $_str_ireplace("*||*", ' ', $Comments) : '' . "</td>"); ?>
                                    </tr>
                                    <?php
                                    if ($_COOKIE['mode'] == 'full' && strtoupper($company_state) == "CT" || strtoupper($company_state) == 'CONNECTICUT') {
                                        ?>
                                        <tr>
                                            <th>Recall</th>
                                            <td data-mdb-toggle="modal" data-mdb-target="#recallmodal">
                                                <i class="fas fa-regular fa-truck-tow"></i>
                                            </td>
                                        </tr>
                                        <?php
                                    }
                                    ?>
                                    <tr>
                                        <th>Writer</th>
                                        <td>
                                            <?php if ($_COOKIE['mode'] == 'full' && $Status != 'CLOSED') { ?>
                                                <select class="select" onchange="setWriter(this.value)">
                                                    <option><?= ucwords(strtolower($Writer)) ?></option>
                                                    <?php
                                                    $stmt = "select employeelast, employeefirst from employees where shopid = ? and active = 'yes' and `mode` = 'full'";
                                                    if ($query = $conn->prepare($stmt)) {
                                                        $query->bind_param("s", $shopid);
                                                        $query->execute();
                                                        $r = $query->get_result();

                                                        while ($rs = $r->fetch_array()) {
                                                            $employeeFirst = ucwords(strtolower($rs['employeefirst']));
                                                            $employeeLast = ucwords(strtolower($rs['employeelast']));
                                                            $selected = ($Writer == $employeeFirst) ? 'selected' : '';

                                                            echo "<option " . $selected . ">" . $employeeFirst . ' ' . $employeeLast . "</option>";
                                                        }
                                                    }
                                                    ?>
                                                </select>
                                            <?php } else echo(ucwords(strtolower($Writer))); ?>
                                        </td>
                                    </tr>
                                    <?php if ($_COOKIE['mode'] == 'full') { ?>
                                        <tr>
                                            <th>PO #</th>
                                            <td>
                                                <?php if ($Status != 'CLOSED') { ?><i class="fas fa-pen-square"
                                                                                      onclick="editPO()"></i> <?php } ?>
                                                <span id="ponumber"><?= strtoupper($ponumber) ?></span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Source</th>
                                            <td>
                                                <?php if ($Status != 'CLOSED') { ?>
                                                    <select class="select" onchange="setSource(this.value)">
                                                        <option>None</option>
                                                        <?php
                                                        $stmt = "select source from source where shopid = ?";
                                                        if ($query = $conn->prepare($stmt)) {
                                                            $query->bind_param("s", $shopid);
                                                            $query->execute();
                                                            $r = $query->get_result();
                                                            while ($rs = $r->fetch_array())
                                                                echo("<option value='" . $rs['source'] . "' " . ($rs['source'] == $Source ? 'selected' : '') . ">" . ucwords(strtolower($rs['source'])) . "</option>");
                                                        }

                                                        if (stripos(strtoupper($refsource), "REFERRAL") !== false && strlen($refname) > 0) {
                                                            echo "<option selected>" . $Source . " (" . $refname . ")</option>";
                                                        } ?>
                                                        ?>
                                                    </select>
                                                <?php } else echo($Source); ?>
                                            </td>
                                        </tr>
                                    <?php } ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <?php
            $tparts = 0;
            $tpartscost = 0;
            $tlabor = 0;
            $tlaborhrs = 0;
            $gtlaborhrs = 0;
            $gtpcost = 0;
            $tsublet = 0;
            $totaljob = 0;
            $numrows = 0;
            $lents = "";
            $comsfornewcanned = "";
            $query = "select *, (select technician_notes from inspection_item_finding_inputs where id = complaints.inspection_item_id) as techNotes from complaints where shopid = ? and roid = ? and cstatus = 'no' order by displayorder asc";
            if ($stmt = $conn->prepare($query)){

            $stmt->bind_param("si", $shopid, $roid);
            $stmt->execute();
            $result = $stmt->get_result();
            $issuecount = $result->num_rows;
            $stmt->store_result();
            $icntr = 1;
            $runicntr = "1,"; ?>


            <div class="row mb-3">
                <div class="col">
                    <div class="card card-square w-100">
                        <div class="card-body d-flex flex-column no-hover-effect">
                            <div class="row align-items-center p-2">
                                <div class="col-lg-7 justify-content-lg-start justify-content-center">
                                    <h5 class="d-inline card-title">Vehicle Issues and Customer Concerns</h5>
                                </div>
                                <div class="col-lg-5 d-flex justify-content-lg-end justify-content-center">
                                    <?php if ($_COOKIE['mode'] == 'full') { ?>
                                        <span class="text-secondary me-2">Links to Issues:</span>
                                        <?php for ($i = 1; $i <= $issuecount; $i++) echo("<a href='javascript:void(null)' onclick='scrollToIssue(\"issuelink_" . $i . "\")' class='me-2'>" . $i . "</a>"); ?>
                                        <a href="javascript:void(null)"><i
                                                    class="fas fa-chevron-<?= $collapseissues == 'no' ? 'up' : 'down' ?>"
                                                    id="collapseAll"
                                                    data-type="<?= $collapseissues == 'no' ? 'up' : 'down' ?>"></i></a>
                                    <?php } ?>
                                </div>
                            </div>
                            <div class="card-text pt-3">

                                <?php
                                while ($row = $result->fetch_assoc()) {

                                    if (strtolower($onlytechissues) == "yes") {

                                        $tkquery = "select tech from labor where shopid = ? and roid = ? and complaintid = ? and deleted = 'no' and tech = ?";
                                        if ($tkstmt = $conn->prepare($tkquery)) {
                                            $tkstmt->bind_param("ssss", $shopid, $roid, $row['complaintid'], $techname);
                                            $tkstmt->execute();
                                            $tkresult = $tkstmt->get_result();
                                            $tkstmt->store_result();
                                            $numtechrows = $tkresult->num_rows;
                                        } else {
                                            echo "tech tech stmt error:" . $conn->error;
                                        }
                                        if ($numtechrows == 0) continue;
                                    }

                                    $comstatus = strtolower($row['acceptdecline']);
                                    switch ($comstatus) {
                                        case "pending":
                                            $bs_color_class = "pending";
                                            break;
                                        case "approved":
                                            $bs_color_class = "approved";
                                            break;
                                        case "parts ordered":
                                            $bs_color_class = "partsordered";
                                            break;
                                        case "scheduled":
                                            $bs_color_class = "scheduled";
                                            break;
                                        case "assembly":
                                            $bs_color_class = "assembly";
                                            break;
                                        case "job complete":
                                            $bs_color_class = "jobcomplete";
                                            break;
                                        case "declined":
                                            $bs_color_class = "declined";
                                            break;
                                        default:
                                            $bs_color_class = "none";
                                            break;
                                    }
                                    $techreport = strtoupper($row['techreport']);
                                    $lents = "";
                                    if (strlen($techreport) == 0) {
                                        $lents .= "0,";
                                    } else {
                                        $lents .= "1,";
                                    }
                                    ?>


                                    <div class="accordion <?= $icntr != 1 ? 'pt-3' : '' ?>"
                                         id="accordion-vi-<?= $row['complaintid'] ?>">
                                        <a id="issuelink_<?php echo $icntr; ?>" name="<?php echo $icntr; ?>"></a>
                                        <div class="accordion-item ro-status-<?= $bs_color_class ?>"
                                             id="accordion-bg-<?= $row['complaintid'] ?>">
                                            <h2 class="accordion-header <?= $collapseissues == 'yes' ? 'collapsed' : '' ?>;"
                                                id="flush-vi-<?= $row['complaintid'] ?>">
                                                <button class="accordion-button <?= $collapseissues == 'yes' ? 'collapsed' : '' ?>"
                                                        type="button" data-mdb-toggle="collapse"
                                                        data-mdb-target="#vi-<?= $row['complaintid'] ?>"
                                                        aria-expanded="false"
                                                        aria-controls="vi-<?= $row['complaintid'] ?>">
                                                    <div class="row w-100">
                                                        <div class="col-md-8">
                                                            #<?= $icntr ?> <?php echo trim(strtoupper(iconv('UTF-8', 'ASCII//TRANSLIT', $row['complaint']))); ?>
                                                        </div>
                                                        <div class="col-md-4 d-flex justify-content-end pe-3">
                                                            <span class="small me-3" id="tcdiff-<?= $row['complaintid'] ?>"></span>
                                                            <span class=""
                                                                  style='display: <?= $collapseissues == 'no' ? 'none' : '' ?>;'
                                                                  id="accstatus-<?= $row['complaintid'] ?>"><?= $row['acceptdecline'] ?></span>
                                                        </div>
                                                    </div>

                                                </button>
                                            </h2>
                                            <div id="vi-<?= $row['complaintid'] ?>"
                                                 class="accordion-collapse collapse  <?= $collapseissues == 'no' ? 'show' : '' ?>"
                                                 aria-labelledby="flush-headingOne"
                                                 data-mdb-parent="#vi-<?= $row['complaintid'] ?>">
                                                <div class="accordion-body">
                                                    <div class="row">
                                                        <div class="col">
                                                            <!-- Row 1 -->
                                                            <?php
                                                            $techreport = strtoupper(iconv('UTF-8', 'ASCII//TRANSLIT', $row['techreport']));
                                                            $techNotes = strtoupper(iconv('UTF-8', 'ASCII//TRANSLIT', $row['techNotes']));

                                                            if ($techNotes == $techreport) {
                                                                $techReport = $techreport;
                                                            } else {
                                                                if (!empty($techNotes)) {
                                                                    $techReport = $techreport ? $techreport . ". " . $techNotes : $techNotes;
                                                                } else {
                                                                    $techReport = $techreport;
                                                                }
                                                            }
                                                            ?>
                                                            <div class="align-items-center d-flex justify-content-lg-between justify-content-center">
                                                                <div class="col-xl-6 pe-4" style="cursor:pointer;"
                                                                     onclick="techStory(<?php echo $row['complaintid']; ?>)">
                                                                    <span class="text-secondary d-inline">Tech Story: </span> <?= $techReport ?>
                                                                    &nbsp;<a href="javascript:void(null)"
                                                                             data-mdb-toggle="tooltip"
                                                                             data-mdb-placement="bottom"
                                                                             title="Edit"><?php if ($Status != 'CLOSED') { ?>
                                                                        <i class="fas fa-edit"
                                                                           onclick="techStory('<?php echo $row['complaintid']; ?>')"></i><?php } ?>
                                                                    </a>
                                                                </div>
                                                                <?php if ($_COOKIE['mode'] == 'full') { ?>
                                                                    <div class="col-xl-6 text-end" style="cursor:pointer;"
                                                                         onclick="techStory(<?php echo $row['complaintid']; ?>)">
                                                                        <span class="text-secondary">Category:</span>
                                                                        <strong><?php echo strtoupper($row['issue']); ?></strong>
                                                                        <?php
                                                                        if ($Status == 'CLOSED') { ?>
                                                                                <!--
                                                                            <div class="text-end">
                                                                                <button type="button" disabled style="cursor: inherit"
                                                                                        class="btn btn-<?= $bs_color_class ?> dropdown-toggle w-auto"
                                                                                        id="status-<?php echo $row['complaintid']; ?>"
                                                                                        aria-expanded="false"><?= $row['acceptdecline'] ?>
                                                                                </button>
                                                                            </div>
                                                                            -->
                                                                        <?php } ?>
                                                                    </div>
                                                                <?php } ?>
                                                            </div>
                                                            <!-- Row 2 -->
                                                            <div class="d-flex justify-content-lg-start justify-content-center <?= $matco != 'yes' || empty($row['scanreport']) ? 'pb-3' : '' ?>">
                                                                <span class="text-secondary d-inline">Advisor Comments:</span> <?php echo strtoupper(iconv('UTF-8', 'ASCII//TRANSLIT', $row['advisorcomments'])); ?>
                                                            </div>
                                                            <?php if ($matco == 'yes' && !empty($row['scanreport'])) { ?>
                                                                <div class="d-flex justify-content-lg-start justify-content-center pb-3">
                                                                    <span class="text-secondary d-inline">Scan Tool Results:&nbsp;</span> <?php echo strtoupper(iconv('UTF-8', 'ASCII//TRANSLIT', nl2br($row['scanreport']))); ?>
                                                                </div>
                                                            <?php } ?>
                                                            <!-- Row 4 -->
                                                            <div class="row border-top">
                                                                <!-- Left Elements-->
                                                                <div class="col-xl-9 col-lg-12 col-sm-2 p-0 m-0">
                                                                    <div class="d-flex justify-content-start ms-0 me-0">
                                                                        <!-- Navbar -->
                                                                        <nav class="navbar navbar-expand-lg pt-3 text-start">
                                                                            <!-- Container wrapper -->
                                                                            <div class="container-fluid pe-2">
                                                                                <!-- Toggle button -->
                                                                                <button class="navbar-toggler text-primary"
                                                                                        type="button"
                                                                                        data-mdb-toggle="collapse"
                                                                                        data-mdb-target="#navbarSupportedContent"
                                                                                        aria-controls="navbarSupportedContent"
                                                                                        aria-expanded="false"
                                                                                        aria-label="Toggle navigation">
                                                                                    <i class="fas fa-bars"></i>
                                                                                </button>

                                                                                <!-- Collapsible wrapper -->
                                                                                <div class="collapse navbar-collapse"
                                                                                     id="navbarSupportedContent">

                                                                                    <?php if ($Status != 'CLOSED' && strtoupper($row['acceptdecline']) != "DECLINED") { ?>
                                                                                        <!-- Navbar brand -->
                                                                                        <a class="navbar-brand"
                                                                                           href="javascript:void(null)">
                                                                                        </a>
                                                                                        <!-- Left links -->
                                                                                        <ul class="navbar-nav">
                                                                                            <?php

                                                                                            if ($readonly !== "yes") {
                                                                                                $cid = chr(34) . $row['complaintid'] . chr(34);

                                                                                                if (in_array($newpackagetype, ['platinum', 'premier', 'premier plus']) || ($newpackagetype === 'none' && $egbutton === 'motorfull')) {
                                                                                                    $motorfunction = $cid . chr(44) . chr(34) . "39" . chr(34);
                                                                                                    if ($motordriven) {
                                                                                                        echo "<li class='nav-item d-flex text-center pe-2'>
                                                                                                                <a class='nav-link' data-mdb-toggle='tooltip' data-mdb-placement='bottom' title='Parts and Labor Guide' onclick='loadNewMotor($motorfunction)' href='javascript:void(null)'>Motor Driven</a>
                                                                                                              </li>";
                                                                                                    }
                                                                                                    echo "<li class='nav-item d-flex text-center ps-2 pe-2'>
                                                                                                            <a class='nav-link' data-mdb-toggle='tooltip' data-mdb-placement='bottom' title='Parts and Labor Guide' onclick='loadMotor($motorfunction)' href='javascript:void(null)'>Motor</a>
                                                                                                          </li>";
                                                                                                } elseif ($newpackagetype === 'gold' || ($newpackagetype === 'none' && $egbutton === 'motorest')) {
                                                                                                    $motorfunction = $cid . chr(44) . chr(34) . "40" . chr(34);
                                                                                                    echo "<li class='nav-item d-flex text-center pe-2'>
                                                                                                            <a class='nav-link' data-mdb-toggle='tooltip' data-mdb-placement='bottom' title='Parts and Labor Guide' onclick='loadNewMotor($motorfunction)' href='javascript:void(null)'>Motor Driven</a>
                                                                                                          </li>";
                                                                                                    echo "<li class='nav-item d-flex text-center ps-2 pe-2'>
                                                                                                            <a class='nav-link' data-mdb-toggle='tooltip' data-mdb-placement='bottom' title='Parts and Labor Guide' onclick='loadMotor($motorfunction)' href='javascript:void(null)'>Motor</a>
                                                                                                          </li>";
                                                                                                }
                                                                                            }


                                                                                            ?>

                                                                                            <?php if ($InventoryLookup == 'YES' && $editparts == 'YES') { ?>
                                                                                                <li class="nav-item d-flex text-center ps-2 pe-2">
                                                                                                    <a class="nav-link"
                                                                                                       href="javascript:void(null)"
                                                                                                       data-mdb-toggle="tooltip"
                                                                                                       data-mdb-placement="bottom"
                                                                                                       title='Add Parts to this Issue'
                                                                                                       onclick="addPart(<?php echo $row['complaintid']; ?>)">Parts</a>
                                                                                                </li>
                                                                                            <?php }

                                                                                            if ($editlabor == 'YES') { ?>
                                                                                                <li class="nav-item d-flex text-center ps-2 pe-2">
                                                                                                    <a class="nav-link"
                                                                                                       href="javascript:void(null)"
                                                                                                       data-mdb-toggle="tooltip"
                                                                                                       data-mdb-placement="bottom"
                                                                                                       title='Add Labor to this Issue'
                                                                                                       onclick="addLabor(<?php echo $row['complaintid']; ?>)">Labor</a>
                                                                                                </li>
                                                                                            <?php }
                                                                                            if ($editsublet == 'YES') { ?>
                                                                                                <li class="nav-item d-flex text-center ps-2 pe-2">
                                                                                                    <a class="nav-link"
                                                                                                       href="javascript:void(null)"
                                                                                                       data-mdb-toggle="tooltip"
                                                                                                       data-mdb-placement="bottom"
                                                                                                       title='Add a Sublet item to this issue'
                                                                                                       onclick="addSublet(<?php echo $row['complaintid']; ?>)">Sublet</a>
                                                                                                </li>
                                                                                            <?php }
                                                                                            if ($editcannedjobs == 'YES') { ?>
                                                                                                <li class="nav-item d-flex text-center ps-2 pe-2">
                                                                                                    <a class="nav-link"
                                                                                                       href="javascript:void(null)"
                                                                                                       data-mdb-toggle="tooltip"
                                                                                                       data-mdb-placement="bottom"
                                                                                                       onclick="addCannedJob(<?php echo $row['complaintid']; ?>)"
                                                                                                       title='Add a canned job (canned jobs are multiple parts and labor in one job)'>Canned
                                                                                                        Job</a>
                                                                                                </li>
                                                                                            <?php } ?>
                                                                                        </ul>
                                                                                    <?php } ?>
                                                                                </div>
                                                                            </div>
                                                                        </nav>
                                                                    </div>
                                                                </div>

                                                                <!-- Right elements -->
                                                                <div class="col-xl-3 col-lg-12 col-sm-10 m-0">
                                                                    <div class="d-flex justify-content-end align-items-center pt-3 pb-3">
                                                                        <?php if ($_COOKIE['mode'] == 'full' && strtoupper($row['acceptdecline']) != "DECLINED") { ?>
                                                                            <a href="javascript:void(null)"
                                                                               class="pe-3"
                                                                               data-mdb-toggle="tooltip"
                                                                               data-mdb-placement="bottom"
                                                                               title="Save as Canned Job"
                                                                               id="createcanned<?php echo $row['complaintid']; ?>"
                                                                               onclick="createCanned(<?php echo $row['complaintid']; ?>)"><i
                                                                                        class="fas fa-save"></i></a>

                                                                            <?php if ($Status != 'CLOSED' && $changevistatus == "YES") { ?>
                                                                                <a href="javascript:void(null)"
                                                                                   class="pe-3"
                                                                                   data-mdb-toggle="tooltip"
                                                                                   data-mdb-placement="bottom"
                                                                                   title="Duplicate"
                                                                                   onclick='dupIt(<?php echo $row['complaintid']; ?>)'><i
                                                                                            class="fas fa-copy"></i></a>

                                                                                <a href="javascript:void(null)"
                                                                                   id="lock-<?= $row['complaintid'] ?>"
                                                                                   class="pe-3"
                                                                                   style="display:<?= ($row['locked'] == 'no' && $row['acceptdecline'] != 'Declined') ? '' : 'none' ?>"
                                                                                   data-mdb-toggle="tooltip"
                                                                                   data-mdb-placement="bottom"
                                                                                   title="Lock"
                                                                                   onclick='lockIt(<?php echo $row['complaintid']; ?>)'><i
                                                                                            class="fas fa-lock-open"></i></a>

                                                                                <a href="javascript:void(null)"
                                                                                   id="unlock-<?= $row['complaintid'] ?>"
                                                                                   class="pe-3"
                                                                                   style="display:<?= ($row['locked'] == 'yes') ? '' : 'none' ?>"
                                                                                   data-mdb-toggle="tooltip"
                                                                                   data-mdb-placement="bottom"
                                                                                   title="Unlock"
                                                                                   onclick='unlockIt(<?php echo $row['complaintid']; ?>)'><i
                                                                                            class="fas fa-lock"></i></a>

                                                                                <a href="javascript:void(null)"
                                                                                   id="assigntech-<?= $row['complaintid'] ?>"
                                                                                   class="pe-3"
                                                                                   data-mdb-toggle="tooltip"
                                                                                   data-mdb-placement="bottom"
                                                                                   title="Assign Tech"
                                                                                   onclick='assignTechVehicleIssue(<?php echo $row['complaintid']; ?>, "<?= $row['tech'] ?>")'>
                                                                                 <!--   <i class="fa-kit fa-sharp-solid-wrench-simple-circle-user"></i> -->
                                                                                   <i class="fa-solid fa-user"></i>
                                                                                </a>

                                                                            <?php }
                                                                        }
                                                                            ?>


                                                                            <button type="button"
                                                                                    class="btn btn-<?= $bs_color_class ?> dropdown-toggle fixed-width-button"
                                                                                    data-status="<?php echo $row['acceptdecline']; ?>"
                                                                                    id="status-<?php echo $row['complaintid']; ?>"
                                                                                    <?php if ($Status != 'CLOSED' && $changevistatus == "YES") { ?>
                                                                                    data-mdb-toggle="dropdown" aria-expanded="false"
                                                                                    <?php } else { ?> disabled <?php } ?>><?= $row['acceptdecline'] ?>
                                                                            </button>

                                                                            <?php if ($Status != 'CLOSED' && $changevistatus == "YES") { ?>
                                                                            <ul class="dropdown-menu"
                                                                                aria-labelledby="status-<?php echo $row['complaintid']; ?>">
                                                                                <li><a class="dropdown-item"
                                                                                       href="javascript:void(null)"
                                                                                       onclick="setIssueStatus('Pending','<?= $row['complaintid'] ?>')"><span
                                                                                                class="dot status-pending"></span><span
                                                                                                class="text-md"> Pending</span></a>
                                                                                </li>
                                                                                <li><a class="dropdown-item"
                                                                                       href="javascript:void(null)"
                                                                                       onclick="setIssueStatus('Approved','<?= $row['complaintid'] ?>')"><span
                                                                                                class="dot status-approved"></span><span
                                                                                                class="text-md"> Approved</span></a>
                                                                                </li>
                                                                                <li><a class="dropdown-item"
                                                                                       href="javascript:void(null)"
                                                                                       onclick="setIssueStatus('Scheduled','<?= $row['complaintid'] ?>')"><span
                                                                                                class="dot status-scheduled"></span><span
                                                                                                class="text-md"> Scheduled</span></a>
                                                                                </li>
                                                                                <li><a class="dropdown-item"
                                                                                       href="javascript:void(null)"
                                                                                       onclick="setIssueStatus('Parts Ordered','<?= $row['complaintid'] ?>')"><span
                                                                                                class="dot status-partsordered"></span><span
                                                                                                class="text-md"> Parts Ordered</span></a>
                                                                                </li>
                                                                                <li><a class="dropdown-item"
                                                                                       href="javascript:void(null)"
                                                                                       onclick="setIssueStatus('Assembly','<?= $row['complaintid'] ?>')"><span
                                                                                                class="dot status-assembly"></span><span
                                                                                                class="text-md"> Assembly</span></a>
                                                                                </li>
                                                                                <li><a class="dropdown-item"
                                                                                       href="javascript:void(null)"
                                                                                       onclick="setIssueStatus('Job Complete','<?= $row['complaintid'] ?>')"><span
                                                                                                class="dot status-jobcompleted"></span><span
                                                                                                class="text-md"> Job Complete</span></a>
                                                                                </li>
                                                                                <li><a class="dropdown-item"
                                                                                       href="javascript:void(null)"
                                                                                       onclick="setIssueStatus('Declined','<?= $row['complaintid'] ?>')"><span
                                                                                                class="dot status-declined"></span><span
                                                                                                class="text-md"> Declined</span></a>
                                                                                </li>
                                                                            </ul>
                                                                        <?php }
                                                                         ?>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div class="d-flex">
                                                                <div class="row overflow-auto content-container p-0 mt-0 card-square">
                                                                    <table class="display dt-responsive nowrap complaint_items" data-comid="<?= $row['complaintid'] ?>"
                                                                           style="width:100%;">

                                                                        <tbody>
                                                                        <?php
                                                                        $currcommid = $row['complaintid'];
                                                                        $pquery = "select `shopid`,`PartID`,`PartNumber`,`PartDesc`,`PartPrice`,`Quantity`,`ROID`,`Supplier`,`Cost`,`PartInvoiceNumber`,`PartCode`,`LineTTLPrice`,`LineTTLCost`,`Date`,`PartCategory`,`complaintid`,`discount`,`net`,`bin`,`tax`,`overridematrix`,`posted`,`scheduledreceivedate`,`ponumber`,`allocated`,`received`,`updated`,`displayorder`,`pstatus`,`deleted`,`datedone`,`salesperson`,`orderstatus` from parts where shopid = ? and roid = ? and complaintid = ? and deleted != 'yes' order by partid asc";
                                                                        $partsgp = 0;
                                                                        $laborgp = 0;
                                                                        $subgp = 0;

                                                                        if ($pstmt = $conn->prepare($pquery)) {
                                                                            $pstmt->bind_param("sii", $shopid, $roid, $currcommid);
                                                                            $pstmt->execute();

                                                                            $presult = $pstmt->get_result();
                                                                            $pstmt->store_result();
                                                                            $num_parts_rows = $presult->num_rows;

                                                                            while ($prow = $presult->fetch_assoc()) {

                                                                                // check for tire reg
                                                                                $cimsregid = "";
                                                                                $cimsstmt = "select regid from dotreg where shopid = ? and roid = ? and partid = ?";
                                                                                if ($cimsquery = $conn->prepare($cimsstmt)) {
                                                                                    $cimsquery->bind_param("sii", $shopid, $roid, $prow['PartID']);
                                                                                    $cimsquery->execute();
                                                                                    $cimsquery->bind_result($cimsregid);
                                                                                    $cimsquery->fetch();
                                                                                    $cimsquery->close();
                                                                                }

                                                                                if (strtolower($prow['tax']) == 'yes') {
                                                                                    $taxstr = " (TAX)";
                                                                                } else {
                                                                                    $taxstr = " (NON-TAX)";
                                                                                }
                                                                                $tparts += round($prow['LineTTLPrice'], 2);
                                                                                $tpartscost += round($prow['LineTTLCost'], 2);
                                                                                $gtpcost += round($prow['LineTTLCost'], 2);
                                                                                $prow["pstatus"] = strtolower(trim($prow["pstatus"]));

                                                                                if ($prow["pstatus"] == "done") {
                                                                                    $statuscheck = "<i class='fas fa-circle-check green'></i>";
                                                                                } elseif (empty($prow["pstatus"])) {
                                                                                    $statuscheck = "<i class='fas fa-circle-check yellow'></i>";
                                                                                } elseif ($prow["pstatus"] == "other") {
                                                                                    $statuscheck = "<i class='fas fa-circle-check red'></i>";
                                                                                }

                                                                                $partsgp += ($prow['LineTTLPrice'] - $prow['LineTTLCost']);

                                                                                if ($prow['orderstatus'] == 'order') $orderstr = "<span class='fw-bold text-success'>Ordered</span>";
                                                                                elseif ($prow['orderstatus'] == 'quote') $orderstr = "<span class='fw-bold text-danger'>Quoted</span>";
                                                                                else $orderstr = '';
                                                                                ?>

                                                                                <tr class="partsrow">
                                                                                    <td class="text-center"
                                                                                        onclick="itemStatus('part','<?php echo $prow['PartID']; ?>')"
                                                                                        data-status='<?php echo strtolower($prow["pstatus"]); ?>'
                                                                                        id="part-<?php echo $prow['PartID']; ?>"><?= $statuscheck ?></td>
                                                                                    <td class="pncheck"
                                                                                        data-type="<?= $prow['allocated'] ?>"
                                                                                        onclick="editPart(<?php echo $prow['PartID']; ?>,<?php echo $prow['complaintid']; ?>)">
                                                                                        <strong>(P)</strong>
                                                                                        <span class="partnumber"><?php echo strtoupper($prow['PartNumber']); ?></span> <?= $orderstr ?>
                                                                                    </td>
                                                                                    <td onclick="editPart(<?php echo $prow['PartID']; ?>,<?php echo $prow['complaintid']; ?>)">
                                                                                        <?php
                                                                                        echo "<span class='partdesc'>" . strtoupper($prow['PartDesc']) . "</span>";

                                                                                        if (strtolower($showpartscostonro) == "yes" && $techshowpartscostonro == 'YES')
                                                                                            echo "<span> (Cost Each:" . number_format(round($prow['Cost'], 2), 2) . "/ Cost Total: " . number_format(round($prow['LineTTLCost'], 2), 2) . ")</span>";

                                                                                        if (strlen($prow['ponumber']) > 3)
                                                                                            echo " (PO#" . $prow['ponumber'] . ")";

                                                                                        if ($cimsregid != "")
                                                                                            echo "<BR><span style='font-weight:bold'>DOT RegID: " . $cimsregid . "</span>";

                                                                                        ?></td>
                                                                                    <td class="partqty" onclick="editPart(<?php echo $prow['PartID']; ?>,<?php echo $prow['complaintid']; ?>)">
                                                                                        <?php if ($_COOKIE['mode'] == 'full') echo $prow['Quantity'] . " @ " . number_format(round($prow['PartPrice'], 2), 2) . $taxstr;
                                                                                        else echo("Quantity: " . $prow['Quantity']); ?>
                                                                                    </td>
                                                                                    <td onclick="editPart(<?php echo $prow['PartID']; ?>,<?php echo $prow['complaintid']; ?>)"><?php if ($prow['discount'] > 0) {
                                                                                            echo $prow['discount'] . "% Disc";
                                                                                        } ?></td>

                                                                                    <?php if ($_COOKIE['mode'] == 'full') { ?>

                                                                                        <td class="partprice" onclick="editPart(<?php echo $prow['PartID']; ?>,<?php echo $prow['complaintid']; ?>)">
                                                                                            $<?php echo number_format(round($prow['LineTTLPrice'], 2), 2); ?></td>
                                                                                        <td><a href="javascript:void(null)"
                                                                                               data-mdb-toggle="tooltip"
                                                                                               data-mdb-placement="right"
                                                                                               title="Click to delete this part permanently from the repair order">
                                                                                                <?php if ($candelete == "YES" && $Status != 'CLOSED') { ?>
                                                                                                <i
                                                                                                        class="fas fa-times-circle"
                                                                                                        onclick="deletePart(<?php echo $prow['PartID']; ?>,<?php echo $prow['complaintid']; ?>)"></i><?php } ?>
                                                                                            </a>
                                                                                            <?php if ((strpos(strtolower($prow['PartDesc']), "tire") > 0 || substr(strtolower($prow['PartDesc']), 0, 4) == "tire") && $cimsregid == "" && $Status != 'CLOSED') {
                                                                                                $reg = "<div onclick='DOTReg(" . $prow['PartID'] . ",\"" . str_replace("'", "\'", $prow['PartDesc']) . "\"," . $prow['Quantity'] . ")' id='dot_" . $prow['PartID'] . "' class='text-primary dotbtn'>DOT Tire Reg</div>";
                                                                                            } else {
                                                                                                $reg = "";
                                                                                            }
                                                                                            echo $reg; ?></td>
                                                                                    <?php } ?>
                                                                                </tr>
                                                                                <?php
                                                                            }
                                                                        }


                                                                        $ltax = $extrastr = '';
                                                                        if (strtolower($onlytechissues) == "yes") $extrastr = ' and tech = ?';

                                                                        $lquery = "select `shopid`,`LaborID`,`ROID`,`HourlyRate`,`LaborHours`,`Labor`,`Tech`,`LineTotal`,`LaborOp`,`complaintid`,`techrate`,`discount`,`discountpercent`,`lstatus`,`scheduleddatetime`,`schedulelength`,`scheduletext`,`schedulecat`,`deleted`,`memorate`,`datedone` from labor where shopid = ? and roid = ? and complaintid = ? {$extrastr} order by laborid asc";
                                                                        if ($lstmt = $conn->prepare($lquery)) {
                                                                            if (strtolower($onlytechissues) == "yes")
                                                                                $lstmt->bind_param("siis", $shopid, $roid, $currcommid, $techname);
                                                                            else
                                                                                $lstmt->bind_param("sii", $shopid, $roid, $currcommid);
                                                                            $lstmt->execute();

                                                                            $lresult = $lstmt->get_result();
                                                                            $lstmt->store_result();
                                                                            $num_labor_rows = $lresult->num_rows;
                                                                            while ($lrow = $lresult->fetch_assoc()) {
                                                                                $tlabor += round($lrow['LineTotal'], 2);
                                                                                $tlaborhrs += $lrow['LaborHours'];
                                                                                $gtlaborhrs = $gtlaborhrs + $lrow['LaborHours'];
                                                                                $labtaxline = strtolower($lrow["schedulelength"]);


                                                                                $diffdisplay = "";
                                                                                $clockstr = "";
                                                                                $clockstr2 = "";
                                                                                $clockstmt = "select laborid,id,complaintid,tech from labortimeclock where laborid = " . $lrow['LaborID'] . " and shopid = '" . $shopid . "' and roid = " . $roid . " and complaintid = " . $lrow['complaintid']
                                                                                    . " and enddatetime is null";
                                                                                if ($clockquery = $conn->prepare($clockstmt)) {
                                                                                    $clockquery->execute();
                                                                                    $clockresult = $clockquery->get_result();
                                                                                    $num_clock_rows = $clockresult->num_rows;
                                                                                    $clockquery->store_result();
                                                                                    if ($num_clock_rows >= 1) {
                                                                                        $clockstr2 = "<div style='font-size:8pt;font-weight:bold;color:var(--primary)'>CLOCKED IN: ";
                                                                                        while ($clockrow = $clockresult->fetch_assoc()) {
                                                                                            $clockid = $clockrow['id'];
                                                                                            if ($clockstr == "") {
                                                                                                $clockstr2 .= "" . $clockrow['tech'] . "; ";
                                                                                            }
                                                                                        }
                                                                                        $clockstr2 .= "</div>";
                                                                                        $clocktech = strtoupper($lrow['Tech']);
                                                                                        $clockstr = "<div class='fas fa-clock fa-spin' style='font-size:large;color:var(--primary);font-weight:bold' data-mdb-toggle='tooltip' title='Clock out of this job' onclick='selectTechClockin(" . $lrow['LaborID'] . "," . $lrow['complaintid'] . "," . $clockid . ")'></div>";

                                                                                    } else {
                                                                                        $laborid = $lrow['LaborID'];
                                                                                        $clockcomid = $lrow['complaintid'];
                                                                                        $clocktech = strtoupper($lrow['Tech']);
                                                                                        $clockstr = "<div id='clockin-" . $laborid . "' onclick='selectTechClockin(\"$laborid\",\"$clockcomid\",\"in\")' data-mdb-toggle='tooltip' title='Click to begin recording the actual time the tech is working on this labor item.  Once the tech is done, click again to clock them out and stop recording' style='font-size:large;color:green;font-weight:bold' class='fas fa-clock mytooltip'></div>";
                                                                                    }


                                                                                    // now calculate the number of hours and minutes
                                                                                    $tdiff = 0;
                                                                                    $tstmt = "select coalesce(sum(ROUND(time_to_sec((TIMEDIFF(enddatetime, startdatetime))) / 60)),0) as tiff from labortimeclock where laborid = " . $lrow['LaborID']
                                                                                        . " and shopid = '" . $shopid . "' and roid = " . $roid . " and complaintid = " . $lrow['complaintid'];
                                                                                    if ($tquery = $conn->prepare($tstmt)) {
                                                                                        $tquery->execute();
                                                                                        $tquery->store_result();
                                                                                        $tquery->bind_result($tdiff);
                                                                                        $tquery->fetch();
                                                                                    }
                                                                                    if ($tdiff > 0) {
                                                                                        $thrs = intval($tdiff / 60);
                                                                                        $rem = $tdiff % 60;
                                                                                        $tenthsofhour = round($tdiff / 60, 2);
                                                                                        if (strtoupper($showtechoverhours) == "YES") {
                                                                                            if ($thrs >= 1) {
                                                                                                $tclockhrs = floatval($thrs + ($rem / 60));
                                                                                                if ($tclockhrs > $lrow['LaborHours']) {
                                                                                                    $diffdisplay = "<br><span class='mytooltip t1' title='Click to view labor timeclock details' onclick='showHours(" . $lrow['complaintid'] . "," . $lrow['LaborID'] . ")' style='color:var(--primary);font-weight:bold'>" . $thrs . " hrs " . $rem . " min/" . ($tenthsofhour) . " (exceeds sold)</span>";
                                                                                                } else {
                                                                                                    $diffdisplay = "<br><span class='mytooltip t2' title='Click to view labor timeclock details' onclick='showHours(" . $lrow['complaintid'] . "," . $lrow['LaborID'] . ")' style='color:var(--blue)'>" . $thrs . " hrs " . $rem . " min/" . ($tenthsofhour) . "</span>";
                                                                                                }
                                                                                            } else {
                                                                                                $tclockhrs = floatval($rem / 60);
                                                                                                if ($tclockhrs > $lrow['LaborHours']) {
                                                                                                    $diffdisplay = "<br><span class='mytooltip t3' title='Click to view labor timeclock details' onclick='showHours(" . $lrow['complaintid'] . "," . $lrow['LaborID'] . ")' style='color:var(--primary);font-weight:bold'>" . $rem . " min/" . $tenthsofhour . " (exceeds sold)</span>";
                                                                                                } else {
                                                                                                    $diffdisplay = "<br><span class='mytooltip t4' title='Click to view labor timeclock details' onclick='showHours(" . $lrow['complaintid'] . "," . $lrow['LaborID'] . ")' style='color:var(--blue)'>" . $rem . " min/" . $tenthsofhour . "</span>";
                                                                                                }
                                                                                            }
                                                                                        } else {
                                                                                            if ($thrs >= 1) {
                                                                                                $diffdisplay = "<br><span class='mytooltip t5' title='Click to view labor timeclock details' onclick='showHours(" . $lrow['complaintid'] . "," . $lrow['LaborID'] . ")' style='color:var(--blue)'>" . $thrs . " hrs " . $rem . " min/" . ($tenthsofhour) . "</span>";
                                                                                            } else {
                                                                                                $diffdisplay = "<br><span class='mytooltip t5' title='Click to view labor timeclock details' onclick='showHours(" . $lrow['complaintid'] . "," . $lrow['LaborID'] . ")' style='color:var(--blue)'>" . $rem . " min/" . $tenthsofhour . "</span>";
                                                                                            }
                                                                                        }
                                                                                    } else {
                                                                                        $diffdisplay = "";
                                                                                    }
                                                                                }

                                                                                if (strtolower($lrow["lstatus"]) <> "done") {
                                                                                    $backcolor = "yellow";
                                                                                } else {
                                                                                    $backcolor = "green";
                                                                                }

                                                                                // calc the gp for labor
                                                                                $hourlyrate = 0;
                                                                                if (strpos($lrow['Tech'], ",") > 0) {

                                                                                    $techar = explode(",", $lrow['Tech']);
                                                                                    $techfn = trim($techar[1]);
                                                                                    $techln = $techar[0];

                                                                                    $techstmt = "select coalesce(hourlyrate,0) hourlyrate from employees where active = 'yes' and employeefirst = ? and employeelast = ? and (jobdesc like '%TECHNICIAN%' or showtechlist = 'yes') and shopid = '$shopid'";
                                                                                    if ($techquery = $conn->prepare($techstmt)) {
                                                                                        $techquery->bind_param("ss", $techfn, $techln);
                                                                                        $techquery->execute();
                                                                                        $techresults = $techquery->get_result();
                                                                                        if ($techrow = $techresults->fetch_array()) {
                                                                                            $hourlyrate = $techrow['hourlyrate'];
                                                                                        } else {
                                                                                            $hourlyrate = 0;
                                                                                        }

                                                                                    }

                                                                                }

                                                                                $laborcost = $hourlyrate * $lrow['LaborHours'];
                                                                                $laborgp += ($lrow['LineTotal'] - $laborcost);
                                                                                if ($LaborTaxRate > 0 && (empty($labtaxline) || $labtaxline == 'yes')) {
                                                                                    $ltax = " (TAX)";
                                                                                } else {
                                                                                    $ltax = " (NON-TAX)";
                                                                                }

                                                                                if ($_COOKIE['mode'] == 'tech2') {
                                                                                    if ($editlabor == 'YES' && htmlspecialchars_decode(strtoupper($lrow['Tech']), ENT_QUOTES) == htmlspecialchars_decode(strtoupper($techname), ENT_QUOTES))
                                                                                        $editlabor = "YES";
                                                                                    else
                                                                                        $editlabor = "NO";
                                                                                }
                                                                                ?>

                                                                                <tr class="laborsrow">
                                                                                    <td class="text-center"
                                                                                        id="labor-<?php echo $lrow['LaborID']; ?>"
                                                                                        onclick="itemStatus('labor','<?php echo $lrow['LaborID']; ?>')"
                                                                                        data-status="<?php echo strtolower($lrow["lstatus"]); ?>">
                                                                                        <i class="fas fa-check-circle <?= $backcolor ?>"></i>
                                                                                    </td>
                                                                                    <td <?php if ($editlabor == "YES"){ ?>onclick="editLabor(<?php echo $lrow['LaborID']; ?>,<?php echo $lrow['complaintid']; ?>)"<?php } ?>>
                                                                                        <?php
                                                                                        echo "<strong>(L)</strong> <span class='labortech'>" . (strtoupper($lrow['Tech']) != 'NULL' ? substr(html_entity_decode(strtoupper($lrow['Tech']), ENT_QUOTES | ENT_HTML5), 0, 15) : 'NONE SELECTED') . "</span>" . $diffdisplay;
                                                                                        if ($clockstr != '<div style="font-size:8pt;font-weight:bold;color:var(--primary)">CLOCKED IN: </div>') {
                                                                                            echo $clockstr2;
                                                                                        }
                                                                                        ?></td>

                                                                                    <td <?php if ($editlabor == "YES"){ ?>onclick="editLabor(<?php echo $lrow['LaborID']; ?>,<?php echo $lrow['complaintid']; ?>)"<?php } ?>>
                                                                                        <span class="labordesc"><?php echo strtoupper($lrow['Labor']); ?></span>
                                                                                    </td>
                                                                                    <td class="laborhours" <?php if ($editlabor == "YES"){ ?>onclick="editLabor(<?php echo $lrow['LaborID']; ?>,<?php echo $lrow['complaintid']; ?>)"<?php } ?>>
                                                                                        <?php if ($_COOKIE['mode'] == 'full') echo $lrow['LaborHours'] . " @ " . number_format(round($lrow['HourlyRate'], 2), 2) . $ltax;
                                                                                        elseif ($showlaborhoursonro == "YES") echo($lrow['LaborHours'] . ' hours'); ?></td>
                                                                                    <td><?php if ($Status != 'CLOSED' && ($_COOKIE['mode'] == 'full' || htmlspecialchars_decode(strtoupper($lrow['Tech']), ENT_QUOTES) == htmlspecialchars_decode(strtoupper($techname), ENT_QUOTES))) echo $clockstr; ?><?php if ($lrow['discount'] > 0) {
                                                                                            echo " | " . $lrow['discountpercent'] . "% Disc";
                                                                                        } ?></td>

                                                                                    <?php if ($_COOKIE['mode'] == 'full') { ?>

                                                                                        <td class="labortotal" onclick="editLabor(<?php echo $lrow['LaborID']; ?>,<?php echo $lrow['complaintid']; ?>)">
                                                                                            $<?php echo number_format(round($lrow['LineTotal'], 2), 2); ?></td>
                                                                                        <td data-mdb-toggle="tooltip"
                                                                                            data-mdb-placement="right"
                                                                                            title="Click to delete this labor permanently from the repair order">
                                                                                            <?php
                                                                                            if ($candelete == "YES" && $Status != 'CLOSED') {
                                                                                                ?>
                                                                                                <i onclick="deleteLabor(<?php echo $lrow['LaborID']; ?>,<?php echo $lrow['complaintid']; ?>)"
                                                                                                   class="fas fa-times-circle"></i>
                                                                                                <?php
                                                                                            }
                                                                                            ?>
                                                                                        </td>
                                                                                    <?php } ?>
                                                                                </tr>
                                                                                <?php
                                                                            }
                                                                        }

                                                                        if ($showsublet == "YES") {

                                                                            $tax = '';
                                                                            $squery = "select `shopid`,`SubLetID`,`ROID`,`SubletDesc`,`SubletPrice`,`SubletCost`,`SubletInvoiceNo`,`SubletSupplier`,`complaintid`,`ponumber`,`deleted`,`invdate`,`done`,`taxable` from sublet where shopid = ? and roid = ? and complaintid = ? order by subletid asc";
                                                                            if ($sstmt = $conn->prepare($squery)) {
                                                                                $sstmt->bind_param("sii", $shopid, $roid, $currcommid);
                                                                                $sstmt->execute();

                                                                                $sresult = $sstmt->get_result();
                                                                                $sstmt->store_result();
                                                                                $num_sublet_rows = $sresult->num_rows;
                                                                                while ($srow = $sresult->fetch_assoc()) {
                                                                                    $tsublet += round($srow['SubletPrice'], 2);
                                                                                    $subgp += ($srow['SubletPrice'] - $srow['SubletCost']);

                                                                                    $backcolor = "yellow";
                                                                                    if (strtolower($srow["done"]) == "yes") {
                                                                                        $statuscolor = "green";
                                                                                    } elseif (strtolower($srow["done"]) == "") {
                                                                                        $statuscolor = "yellow";
                                                                                    } elseif (strtolower($srow["done"]) == "no") {
                                                                                        $statuscolor = "red";
                                                                                    }
                                                                                    if ($srow["taxable"] == 'yes') {
                                                                                        $stax = " (TAX)";
                                                                                    } else {
                                                                                        $stax = " (NON-TAX)";
                                                                                    }


                                                                                    ?>
                                                                                    <tr class="sbphover-row subletrow">
                                                                                        <td class="text-center"
                                                                                            id="sublet-<?php echo $srow['SubLetID']; ?>"
                                                                                            onclick="itemStatus('sublet','<?php echo $srow['SubLetID']; ?>')"
                                                                                            data-status="<?php echo strtolower($srow["done"]); ?>">
                                                                                            <i class="fas fa-check-circle <?= $statuscolor ?>"></i>
                                                                                        </td>
                                                                                        <td class="line-hover"
                                                                                            onclick="editSublet(<?php echo $srow['SubLetID']; ?>,<?php echo $srow['complaintid']; ?>)">
                                                                                            <strong>(S)</strong>
                                                                                            Sublet
                                                                                            <span class="subletinv"><?= !empty($srow['SubletInvoiceNo']) ? '#' . strtoupper($srow['SubletInvoiceNo']) : '' ?></span>
                                                                                        </td>
                                                                                        <td class="line-hover subletdesc"
                                                                                            onclick="editSublet(<?php echo $srow['SubLetID']; ?>,<?php echo $srow['complaintid']; ?>)"><?php echo strtoupper($srow['SubletDesc']); ?></td>
                                                                                        <td class="line-hover subletprice"
                                                                                            onclick="editSublet(<?php echo $srow['SubLetID']; ?>,<?php echo $srow['complaintid']; ?>)"><?php echo "1 @ " . number_format(round($srow['SubletPrice'], 2), 2) . ($_COOKIE['mode'] == 'full' ? $stax : ''); ?></td>
                                                                                        <td class="line-hover"
                                                                                            onclick="editSublet(<?php echo $srow['SubLetID']; ?>,<?php echo $srow['complaintid']; ?>)">
                                                                                            &nbsp;
                                                                                        </td>
                                                                                        <?php if ($_COOKIE['mode'] == 'full') { ?>
                                                                                            <td class="line-hover sublettotal"
                                                                                                onclick="editSublet(<?php echo $srow['SubLetID']; ?>,<?php echo $srow['complaintid']; ?>)">
                                                                                                $<?php echo number_format(round($srow['SubletPrice'], 2), 2); ?></td>
                                                                                            <td data-mdb-toggle="tooltip"
                                                                                                data-mdb-placement="right"
                                                                                                title="Click to delete this sublet permanently from the repair order">
                                                                                                <?php
                                                                                                if ($candelete == "YES" && $Status != 'CLOSED') {
                                                                                                    ?>
                                                                                                    <i onclick="deleteSublet(<?php echo $srow['SubLetID']; ?>,<?php echo $srow['complaintid']; ?>)"
                                                                                                       class="fas fa-times-circle"></i>
                                                                                                    <?php
                                                                                                }
                                                                                                ?>
                                                                                            </td>
                                                                                        <?php } ?>
                                                                                    </tr>
                                                                                    <?php
                                                                                }
                                                                            }

                                                                        }


                                                                        $totaljob = $tsublet + $tlabor + $tparts;
                                                                        $comgp = 0;
                                                                        $rungp = $laborgp + $partsgp + $subgp;
                                                                        if ($totaljob > 0) {
                                                                            $comgp = $rungp / $totaljob;
                                                                        } else {
                                                                            $comgp = 0;
                                                                        }

                                                                        $gptext = '';

                                                                        if (strtolower($showgponro) != 'yes' || strtolower($showgpinro) != 'yes')
                                                                            $comgp = '';
                                                                        elseif ($comgp > 0) {
                                                                            // Initialize compgp if not set
                                                                            $compgp = isset($compgp) ? $compgp : 0;

                                                                            if ($compgp >= 0 && $comgp <= .5) $gptext = 'red';
                                                                            elseif ($compgp > .5 && $comgp < .6) $gptext = 'yellow';
                                                                            elseif ($compgp >= .6) $gptext = 'green';
                                                                            $comgp = round($comgp * 100, 0) . '%';
                                                                        } else {
                                                                            $comgp = "0%";
                                                                        }

                                                                        $comsfornewcanned .= $currcommid . "|";

                                                                        $pif = '';

                                                                        if ($haspph == "yes" && $num_labor_rows > 0) {
                                                                            $pif1 = $partsgp + $subgp + $tlabor + $pphfees - $pphdisc;
                                                                            $pif = $pif1 / $tlaborhrs;
                                                                        }


                                                                        ?>

                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <?php if ($_COOKIE['mode'] == 'full') { ?>

                                                        <div class="row pe-2 ps-2 mt-3">
                                                            <div class="col-12 col-lg-2 text-center">Total this Concern:
                                                                <br><?php if ($comgp != '') { ?><a
                                                                    href="javascript:void(null)" style="color: var(--<?= $gptext ?>) !important;" class="pe-2"
                                                                    onclick="showTotalIssue(<?php echo $row['complaintid']; ?>)">
                                                                    GP: <?= $comgp ?></a><?php }
                                                                if ($pif != '') { ?> <a href="javascript:void(null)"
                                                                                        style="color: var(--<?= $pif >= $target ? 'customgreen' : 'red' ?>) !important;"
                                                                                        onclick="showPPFIssue(<?php echo $row['complaintid']; ?>)">
                                                                    <br>PPH: <?= asdollars($pif) ?></a><?php } ?></div>
                                                            <div class="col-6 col-lg text-center"><?php if (strtolower($showpartscostonro) == "yes") { ?>Parts Cost: <?php echo "$" . number_format($tpartscost, 2); ?><?php } else { ?>Parts: <?php echo "$" . number_format($tparts, 2); ?><?php } ?></div>
                                                            <?php if (strtolower($showpartscostonro) == "yes") { ?>
                                                                <div class="col-6 col-lg text-center">Parts
                                                                Price: <?php echo "$" . number_format($tparts, 2); ?></div><?php } ?>
                                                            <div class="col-6 col-lg text-center">Labor
                                                                Hrs: <?php echo $tlaborhrs; ?></div>
                                                            <?php if (strtolower($showtimeclockoncomplaint) == "yes"){ ?>
                                                            <div class="col-6 col-lg text-center">Timeclock: <span id="tchrs-<?= $row['complaintid']; ?>"></span></div>
                                                            <?php } ?>
                                                            <div class="col-6 col-lg text-center">
                                                                Labor: <?php echo "$" . number_format($tlabor, 2); ?></div>
                                                            <div class="col-6 col-lg text-center">
                                                                Sublet: <?php echo "$" . number_format($tsublet, 2); ?></div>
                                                            <div class="col-6 col-lg text-center">
                                                                Total: <?php echo "$" . number_format($totaljob, 2); ?></div>
                                                        </div>
                                                    <?php } ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>


                                    <?php

                                    $tlabor = 0;
                                    $tlaborhrs = 0;
                                    $tparts = 0;
                                    $tpartscost = 0;
                                    $tsublet = 0;
                                    $icntr++;
                                    $runicntr .= $icntr . ",";
                                }
                                }


                                if (strpos($lents, "0") === false) {
                                    echo "<input type='hidden' id='lents' value='no'>";
                                } else {
                                    echo "<input type='hidden' id='lents' value='yes'>";
                                }
                                echo '<input type="hidden" id="comsfornewcanned" value="' . $comsfornewcanned . '">';
                                echo "<input type='hidden' id='icntrs' value='" . substr($runicntr, 0, strlen($runicntr) - 1) . "'>";

                                if (($icntr - 1) == 0) {
                                    echo "<br><br><div class='text-center text-primary fs-6'>You have no vehicle issues or customer concerns.  Please add them first, then add your parts and labor.  Click the Vehicle Issues button on the left</div><br><br><br><br>";

                                } elseif ($_COOKIE['mode'] == 'full') {
                                    ?>
                                    <div class="d-flex border-top ps-0 pe-0 pt-4 justify-content-center mt-4">
                                        <div class="ps-4 pe-4 text-center"><h4>Total Parts
                                                Cost: <?= asdollars($gtpcost) ?></h4></div>
                                        <div class="ps-4 pe-4 text-center"><h4>Total Labor Hrs: <?= $gtlaborhrs ?></h4>
                                        </div>
                                    </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php if ($_COOKIE['mode'] == 'full') { ?>

            <div class="col-xxl-3 col-xl-12">
                <div class="row mb-3 fees">
                    <!-- Fees -->
                    <div class="col">
                        <div class="d-flex flex-column">
                            <div class="dropdown row w-100 ms-2 me-1">
                                <button class="col d-flex justify-content-center p-2 dropdown-toggle ps-3" type="button"
                                        id="revisionsButton" data-mdb-toggle="dropdown" aria-expanded="false">
                                    Revisions / Payments / Fees / More
                                    <div class="col d-flex justify-content-end">
                                        <span class="select-arrow"></span>
                                    </div>
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="revisionsButton">
                                    <li><a class="dropdown-item" href="javascript:void(null)" onclick="showExtras('revs')">Revisions</a>
                                    </li>
                                    <li><a class="dropdown-item" href="javascript:void(null)" onclick="showExtras('fees')">Fees/Taxes</a>
                                    </li>
                                    <li><a class="dropdown-item" href="javascript:void(null)" onclick="showExtras('warr')">Warranties</a>
                                    </li>
                                    <li><a class="dropdown-item" href="javascript:void(null)" onclick="showExtras('pmts')">Payments</a>
                                    </li>
                                    <li><a class="dropdown-item" href="javascript:void(null)"
                                           onclick="showExtras('commlog')">Communication Log</a></li>
                                    <?php if ($Status != 'CLOSED') { ?>
                                        <hr/>
                                        <li><a class="dropdown-item" href="javascript:void(null)" data-mdb-toggle="modal"
                                               data-mdb-target="#commmodal">Add Communication</a></li>
                                        <li><a class="dropdown-item" href="javascript:void(null)" data-mdb-toggle="modal"
                                               data-mdb-target="#pmtmodal">Receive a Payment</a></li>
                                        <?php
                                        if ($merchantaccount == "authorize.net") {
                                            ?>
                                            <li><a class="dropdown-item" href="javascript:void(null)"
                                                   onclick="showProcessCC('ccmodal')" class="text text-primary">Process
                                                    Credit Card</a></li>
                                            <?php
                                        } elseif ($merchantaccount == "cardknox") {
                                            ?>
                                            <li><a class="dropdown-item" href="javascript:void(null)"
                                                   onclick="showProcessCC('cardknoxmodal')" class="text text-primary">Process
                                                    Credit Card</a></li>
                                            <?php
                                        } elseif ($merchantaccount == "360") {
                                            ?>
                                            <li><a class="dropdown-item" href="javascript:void(null)"
                                                   onclick="showProcessCC('threesixtymodal')" class="text text-primary">Process
                                                    Credit Card</a></li>
                                            <?php
                                        } elseif ($merchantaccount == "tnp") {
                                            ?>
                                            <li><a class="dropdown-item" href="javascript:void(null)"
                                                   onclick="showProcessCC('tnpmodal')" class="text text-primary">Process
                                                    Credit Card</a></li>
                                            <?php
                                        } elseif ($merchantaccount == "stax") {
                                            ?>
                                            <li><a class="dropdown-item" href="javascript:void(null)"
                                                   onclick="showProcessCC('staxmodal')" class="text text-primary">Process
                                                    Credit Card</a></li>
                                            <?php
                                        }
                                    }
                                    ?>
                                </ul>
                            </div>

                            <div id="fees" class="extras  no-hover-effect"
                                 style="display: <?= $defaultrochild != 'fees' ? 'none' : '' ?>;"><!-- Fees starts -->

                                <div class="card-body pt-4">
                                    <div class="d-flex justify-content-between flex-row align-items-center text-center">
                                        <div class="card-title w-100">Fees, Taxes, and Discounts</div>
                                        <?php if ($Status != 'CLOSED') { ?>
                                            <div><a href="javascript:void(null)" onclick="$('#feesmodal').modal('show')" title="Edit"><i
                                                            class="fas fa-edit"></i></a></div><?php } ?>
                                    </div>
                                    <div class="d-flex justify-content-start"></div>
                                    <div class="d-flex justify-content-end"></div>
                                </div>
                                <div class="card-text">
                                    <table class="table table-borderless table-sm">
                                        <tbody>
                                        <tr class="feerow">
                                            <th class="feelabel"><?php if ($shopid == '3073') {
                                                    echo "Hazardous Waste/Shop Materials";
                                                } else {
                                                    echo "Hazardous Waste";
                                                } ?></th>
                                            <td class="text-end feeamount"><?php echo sbpround(sbpround($HazardousWaste, 2), 2); ?></td>
                                        </tr>
                                        <tr class="feerow">
                                            <th class="feelabel">Storage Fee</th>
                                            <td class="text-end feeamount"><?php echo sbpround(sbpround($storagefee, 2), 2); ?></td>
                                        </tr>
                                        <?php
                                        if (strlen($userfee1label) > 0) {
                                            ?>
                                            <tr class="feerow">
                                                <th class="feelabel"><?= ucwords(strtolower($userfee1label)) ?></th>
                                                <td class="text-end feeamount"><?= sbpround(sbpround($UserFee1, 2), 2) ?></td>
                                            </tr>
                                        <?php } ?>
                                        <?php
                                        if (strlen($userfee2label) > 0) {
                                            ?>
                                            <tr class="feerow">
                                                <th class="feelabel"><?= ucwords(strtolower($userfee2label)) ?></th>
                                                <td class="text-end feeamount"><?= sbpround(sbpround($UserFee2, 2), 2) ?></td>
                                            </tr>
                                        <?php } ?><?php
                                        if (strlen($userfee3label) > 0) {
                                            ?>
                                            <tr class="feerow">
                                                <th class="feelabel"><?= ucwords(strtolower($userfee3label)) ?></th>
                                                <td class="text-end feeamount"><?= sbpround(sbpround($UserFee3, 2), 2) ?></td>
                                            </tr>
                                        <?php } ?>

                                        <?php
                                        $stmt = "select feename,coalesce(sum(feeamount),0) as feeamount from rofees where shopid = ? and roid = ? group by feename";
                                        if ($query = $conn->prepare($stmt)) {
                                            $query->bind_param("si", $shopid, $roid);
                                            $query->execute();
                                            $result = $query->get_result();
                                            $query->store_result();
                                            while ($row = $result->fetch_assoc()) { ?>
                                                <tr class="feerow">
                                                    <th class="feelabel"><?= ucwords(strtolower($row['feename'])) . ' (Part Fee)' ?></th>
                                                    <td class="text-end feeamount"><?= sbpround(sbpround($row['feeamount'], 2), 2) ?></td>
                                                </tr>
                                            <?php }
                                        } ?>
                                        <tr>
                                            <th>Parts Tax Rate</th>
                                            <td class="text-end"><?php echo $TaxRate; ?>%</td>
                                        </tr>
                                        <tr>
                                            <th>Labor Tax Rate</th>
                                            <td class="text-end"><?php echo $LaborTaxRate; ?>%</td>
                                        </tr>
                                        <tr>
                                            <th>Sublet Tax Rate</th>
                                            <td class="text-end"><?php echo $SubletTaxRate; ?>%</td>
                                        </tr>
                                        <tr>
                                            <th>Parts Discount</th>
                                            <td class="text-end"><?php if ($partsdiscount < 0) {
                                                    echo "<b>&lt; " . number_format($partsdiscount * -1, 2) . " &gt;</b>";
                                                } else {
                                                    echo "0.00";
                                                } ?></td>
                                        </tr>
                                        <tr>
                                            <th>Labor Discount</th>
                                            <td class="text-end"><?php if ($labordiscount < 0) {
                                                    echo "<b>&lt; " . number_format($labordiscount * -1, 2) . " &gt;</b>";
                                                } else {
                                                    echo "0.00";
                                                } ?></td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div> <!-- Fees ends -->

                            <div id="pmts" class="extras  no-hover-effect"
                                 style="display: <?= $defaultrochild != 'pmts' ? 'none' : '' ?>;"><!-- Payments starts -->

                                <div class="card-body pt-4">
                                    <div class="d-flex justify-content-between flex-row align-items-center text-center">
                                        <div class="card-title w-100">Payments Received</div>
                                    </div>
                                    <div class="d-flex justify-content-start"></div>
                                    <div class="d-flex justify-content-end"></div>
                                </div>
                                <div class="card-text">
                                    <table class="table table-borderless table-sm" id="paymentstable">
                                        <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Amount</th>
                                            <th>Type</th>
                                            <th>Ref</td>
                                            <th>Action</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <?php
                                        $stmt = "select id,amt,ptype,pdate,pnumber,token,terminalid,tstat,transactionid,surcharge from accountpayments where shopid = ? and roid = ?";
                                        if ($query = $conn->prepare($stmt)) {
                                            $query->bind_param("si", $shopid, $roid);
                                            $query->execute();
                                            $result = $query->get_result();
                                            $query->store_result();
                                            while ($row = $result->fetch_assoc()) {
                                                $pdate = date_create($row['pdate']);
                                                $pdate = $pdate->format("m/d/Y");
                                                $tdate = date("m/d/Y");
                                                $ref = "";
                                                $tnpid = $row['id'];
                                                $tnpamt = $row['amt'];
                                                $voidbtn = "";
                                                $termid = $row['terminalid'];
                                                if ($pdate == $tdate) {
                                                    if (strpos($row['pnumber'], "TNP~") !== false) {
                                                        $voidbtn = "<i class='fas fa-trash' title='Void' onclick='voidTNP($tnpid)'></i>";
                                                        $var = explode("~", $row['pnumber']);
                                                        $ref = $var[2];
                                                    } else {
                                                        $voidbtn = "";
                                                        $ref = $row['pnumber'];
                                                    }
                                                } else {
                                                    if (strpos($row['pnumber'], "TNP~") !== false) {
                                                        $voidbtn = "<i class='fas fa-refresh' title='Refund' onclick='refundTNP($tnpamt,$tnpid)'></i>";
                                                        $var = explode("~", $row['pnumber']);
                                                        $ref = $var[2];
                                                    } else {
                                                        $voidbtn = "";
                                                        $ref = $row['pnumber'];
                                                    }
                                                }

                                                if ($row['tstat'] == "refunded") {
                                                    $voidbtn = "";
                                                } else {
                                                    if (strpos($row['pnumber'], "360~") !== false && $tnpamt > 0) {
                                                        if (strtolower($row['ptype']) != 'paypal') {
                                                            if ($row['terminalid'] != "no")
                                                                $voidbtn = "<i class='fas fa-trash' title='Void' onclick='void360($tnpid)'></i>";
                                                            else
                                                                $voidbtn = "<i class='fas fa-trash' title='Void' onclick='void360remote($tnpid)'></i>";
                                                        }

                                                        $token = $row['token'];
                                                        $refundamt = $row['amt'];

                                                        if (strlen($row['token']) >= 5 && $row['terminalid'] != "no")
                                                            $voidbtn .= " <i class='fas fa-refresh' title='Refund' onclick='refund360($tnpid,\"$token\",\"$refundamt\",\"$termid\")'></i>";
                                                        elseif ($row['token'] == '360' && $row['terminalid'] == "no")
                                                            $voidbtn .= " <i class='fas fa-refresh' title='Refund' onclick='refund360remote($tnpid,\"$refundamt\",\"\")'></i>";

                                                        $var = explode("~", $row['pnumber']);
                                                        $ref = $var[1];
                                                    } elseif (strpos($row['pnumber'], "stax~") !== false && $tnpamt > 0) {
                                                        if (strtolower($row['ptype']) != 'paypal') {
                                                            if ($row['terminalid'] != "no")
                                                                $voidbtn = "<i class='fas fa-trash' title='Void' onclick='voidstax($tnpid)'></i>";
                                                            else
                                                                $voidbtn = "<i class='fas fa-trash' title='Void' onclick='voidstaxremote($tnpid)'></i>";
                                                        }

                                                        $refundamt = $row['amt'];

                                                        if ($row['terminalid'] != "no")
                                                            $voidbtn .= " <i class='fas fa-refresh' title='Refund' onclick='refundstax($tnpid,\"$refundamt\",\"$termid\")'></i>";
                                                        else
                                                            $voidbtn .= " <i class='fas fa-refresh' title='Refund' onclick='refundstaxremote($tnpid,\"$refundamt\")'></i>";

                                                        $var = explode("~", $row['pnumber']);
                                                        $ref = end($var);
                                                    } elseif (strpos($row['pnumber'], "360SYNC~") !== false || strpos($row['pnumber'], "360AFF~") !== false || strpos($row['pnumber'], "360SUNB~") !== false) {
                                                        $rtypearr = explode('~', $row['pnumber']);
                                                        $rtype = str_replace('360', '', $rtypearr[0]);
                                                        $voidbtn = " <i class='fas fa-refresh' title='Refund' onclick='refund360remote($tnpid,\"$refundamt\",\"$rtype\")'></i>";
                                                        $ref = $row['pnumber'];
                                                    } elseif (strpos($row['pnumber'], "360EASY~") !== false || strpos($row['pnumber'], "360WISE~") !== false || strpos($row['pnumber'], "360AFF~") !== false) {
                                                        $voidbtn = " ";
                                                        $ref = $row['pnumber'];
                                                    } else {
                                                        $voidbtn = "";
                                                        if (strpos($row['pnumber'], "~")) {
                                                            $var = explode("~", $row['pnumber']);
                                                            $ref = $var[1];
                                                        } else {
                                                            $ref = $row['pnumber'];
                                                        }
                                                    }
                                                }

                                                ?>
                                                <tr>
                                                    <td onclick="editPayment(<?php echo $row['id']; ?>)"><?php echo $pdate; ?></td>
                                                    <td onclick="editPayment(<?php echo $row['id']; ?>)"
                                                        class="text-right"><?= asdollars($row['amt'] + $row['surcharge']) ?></td>
                                                    <td onclick="editPayment(<?php echo $row['id']; ?>)"
                                                        class="text-left"><?= $row['ptype'] ?></td>
                                                    <td onclick="editPayment(<?php echo $row['id']; ?>)" title="<?= $ref ?>"
                                                        class="text-left"><?= strlen($ref) > 5 ? '...' . substr($ref, -5) : $ref ?></td>

                                                    <?php
                                                    if (strtoupper($deletepaymentsreceived) == "YES" && $voidbtn == "" && $row['transactionid'] == 'no' && $Status != 'CLOSED') {
                                                        ?>
                                                        <td class="text-right"><i class="fas fa-circle-minus"
                                                                                  onclick="delPayment(<?php echo $row['id']; ?>)"></i>
                                                        </td>
                                                        <?php
                                                    } elseif (strtoupper($deletepaymentsreceived) == "YES" && $voidbtn != "" && $tnpamt > 0 && $Status != 'CLOSED') {
                                                        echo '<td class="text-left">' . $voidbtn . "</td>";
                                                    } else {
                                                        echo '<td class="text-right"></td>';
                                                    }
                                                    ?>

                                                </tr>
                                                <?php
                                            }
                                        }
                                        ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php if ($Status != 'CLOSED') { ?>
                                    <div class="text-center">
                                        <a href="javascript:void(null)" data-mdb-target="#pmtmodal" data-mdb-toggle="modal"
                                           class="text text-primary me-3"><i class="fas fa-circle-plus"></i> Receive a
                                            Payment</a>
                                        <?php
                                        if ($merchantaccount == "authorize.net") {
                                            ?>
                                            <a href="javascript:void(null)" onclick="showProcessCC('ccmodal')"
                                               class="text text-primary"><i class="fas fa-circle-plus"></i> Process CC</a>
                                            <?php
                                        } elseif ($merchantaccount == "cardknox") {
                                            ?>
                                            <a href="javascript:void(null)" onclick="showProcessCC('cardknoxmodal')"
                                               class="text text-primary"><i class="fas fa-circle-plus"></i> Process CC</a>
                                            <?php
                                        } elseif ($merchantaccount == "360") {
                                            ?>
                                            <a href="javascript:void(null)" onclick="showProcessCC('threesixtymodal')"
                                               class="text text-primary"><i class="fas fa-circle-plus"></i> Process CC</a>
                                            <?php
                                        } elseif ($merchantaccount == "stax") {
                                            ?>
                                            <a href="javascript:void(null)" onclick="showProcessCC('staxmodal')"
                                               class="text text-primary"><i class="fas fa-circle-plus"></i> Process CC</a>
                                            <?php
                                        } elseif ($merchantaccount == "tnp") {
                                            ?>
                                            <a href="javascript:void(null)" onclick="showProcessCC('tnpmodal')"
                                               class="text text-primary"><i class="fas fa-circle-plus"></i> Process CC</a>
                                            <?php
                                        }
                                        ?>
                                    </div>
                                <?php } ?>
                            </div> <!-- Payments ends -->

                            <div id="revs" class="extras  no-hover-effect"
                                 style="display: <?= $defaultrochild != 'revs' ? 'none' : '' ?>;"><!-- Revisions starts -->
                                <div class="card-body pt-4 ">
                                    <div class="d-flex justify-content-between flex-row align-items-center text-center">
                                        <div class="card-title w-100">Revisions</div>
                                    </div>
                                    <div class="d-flex justify-content-start"></div>
                                    <div class="d-flex justify-content-end"></div>
                                </div>
                                <div class="card-text">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-outline mb-4">
                                                <input type="text" class="form-control" id="origro" name="origro"
                                                       onblur="saveOrigRO()"
                                                       value="<?php echo number_format($OrigRO, 2); ?>">
                                                <label class="form-label" for="origro">Original Estimate</label>
                                            </div>
                                            <div id="revisiondiv" class="row">
                                                <?php
                                                $stmt = "select revdate,revtime,revamt,revphone,revby from revisions where shopid = ? and roid = ?";
                                                if ($query = $conn->prepare($stmt)) {
                                                    $query->bind_param("si", $shopid, $roid);
                                                    $query->execute();
                                                    $result = $query->get_result();
                                                    $query->store_result();
                                                    while ($row = $result->fetch_assoc()) {
                                                        ?>

                                                        <div style="font-size:10pt;border-bottom:1px silver inset;"
                                                             class="col-md-10"><?php echo date('m/d/Y', strtotime($row['revdate'])) . ' ' . $row['revtime'] . '<br>' . strtoupper($row['revby']) . ' @ ' . strtoupper($row['revphone']); ?></div>
                                                        <div style="border-bottom:1px silver inset;font-size:10pt;text-align:right"
                                                             class="col-md-2"><?php echo $row['revamt']; ?><br>&nbsp;
                                                        </div>

                                                        <?php
                                                    }
                                                }
                                                ?>
                                            </div>
                                        </div>
                                        <?php if ($Status != 'CLOSED') { ?>
                                            <div class="row mt-4">
                                                <div class="col-md-12">
                                                    <a href="javascript:void(null)" data-mdb-target="#revmodal"
                                                       data-mdb-toggle="modal" class="text text-primary"><i
                                                                class="fas fa-circle-plus"></i> Add a Revision</a>
                                                </div>
                                            </div>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div><!-- Revisions ends -->

                            <div id="warr" class="extras  no-hover-effect"
                                 style="display: <?= $defaultrochild != 'warr' ? 'none' : '' ?>;"><!-- Warranty starts -->
                                <div class="card-body pt-4">
                                    <div class="d-flex justify-content-between flex-row align-items-center text-center">
                                        <div class="card-title w-100">Warranties and Disclosures</div>
                                    </div>
                                    <div class="d-flex justify-content-start"></div>
                                    <div class="d-flex justify-content-end"></div>
                                </div>
                                <div class="card-text">
                                    <div class="row">
                                        <table class="table table-borderless table-sm">
                                            <tbody>
                                            <tr>
                                                <th>Warranty Months</th>
                                                <td id="warrmos"><?php echo $WarrMos; ?></td>
                                            </tr>
                                            <tr>
                                                <th>Warranty Miles</th>
                                                <td id="warrmiles"><?php echo $WarrMiles; ?></td>
                                            </tr>
                                            </tbody>
                                        </table>
                                        <?php if ($Status != 'CLOSED') { ?>
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <a href="javascript:void(null)" onclick="getDisclosures()"
                                                       class="text text-primary me-4"><i class="fas fa-eye"></i> Disclosures</a>
                                                    <a href="javascript:void(null)" data-mdb-target="#warrmodal"
                                                       data-mdb-toggle="modal" class="text text-primary"><i
                                                                class="fas fa-edit"></i> Warranty</a>
                                                </div>
                                            </div>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div><!-- Warranty ends -->

                            <div id="commlog" class="extras"
                                 style="display: <?= $defaultrochild != 'commlog' ? 'none' : '' ?>;">
                                <!-- Comm Log starts -->
                                <div class="card-body pt-4 no-hover-effect">
                                    <div class="d-flex justify-content-between flex-row align-items-center text-center">
                                        <div class="card-title w-100">Communication Log</div>
                                    </div>
                                    <div class="d-flex justify-content-start"></div>
                                    <div class="d-flex justify-content-end"></div>
                                </div>
                                <div class="card-text">
                                    <div id="commlogdiv">
                                        <?php
                                        if (strtolower($commoldtonew) == 'yes') $orderby = 'asc'; else $orderby = 'desc';
                                        $stmt = "select `datetime`,`by`,comm from repairordercommhistory where shopid = ? and roid = ? ORDER BY `datetime` {$orderby}";
                                        if ($query = $conn->prepare($stmt)) {
                                            $query->bind_param("si", $shopid, $roid);
                                            $query->execute();
                                            $result = $query->get_result();
                                            $query->store_result();
                                            while ($row = $result->fetch_assoc()) {
                                                $datetime = date('m/d/Y h:i:s A', strtotime($row['datetime']));
                                                ?>
                                                <div class="row" style="font-size:9pt">
                                                    <div class="col-md-5"><?php echo $datetime . ' - ' . $row['by']; ?></div>
                                                    <div class="col-md-7"><?php echo $row['comm']; ?></div>
                                                </div>
                                                <?php
                                            }
                                        }
                                        ?>
                                    </div>
                                    <?php if ($Status != 'CLOSED') { ?>
                                        <div class="row mt-2">
                                            <div class="col-md-12">
                                                <a href="javascript:void(null)" data-mdb-target="#commmodal"
                                                   data-mdb-toggle="modal" class="text text-primary"><i
                                                            class="fas fa-circle-plus"></i> Add New Communication</a>
                                            </div>
                                        </div>
                                    <?php } ?>
                                </div>
                            </div><!-- Comm Log ends -->

                            <hr/>

                            <div class="d-flex flex-column text-center" style="display:none;cursor: pointer;"
                                 onclick="showCFP()" id="cfpalert"></div>

                            <?php
                            if (strtolower($scrolltotalswindow) == "yes") {
                                $scrollstr = "position:fixed;width:30%";
                            } elseif (strtolower($scrolltotalswindow) == "no") {
                                $scrollstr = "";
                            }
                            ?>

                            <div id="totalsbox" class=" no-hover-effect" style="<?php //echo $scrollstr; ?>">

                                <div class="d-flex justify-content-between flex-row align-items-center text-center">
                                    <div class="card-title w-100">Totals</div>
                                </div>
                                <div class="card-text">
                                    <table class="table table-borderless table-sm">
                                        <tbody>
                                        <tr>
                                            <th onclick="showParts()">Parts</th>
                                            <td class="text-end">$<?php echo sbpround($totalparts, 2); ?></td>
                                        </tr>
                                        <tr>
                                            <th onclick="showLabor()">Labor</th>
                                            <td class="text-end">$<?php echo sbpround($totallabor, 2); ?></td>
                                        </tr>
                                        <tr>
                                            <th onclick="showSublets()">Sublet</th>
                                            <td class="text-end">$<?php echo sbpround($totalsublet, 2); ?></td>
                                        </tr>
                                        <tr>
                                            <th onclick="showFees()">Fees</th>
                                            <td class="text-end">$<?php echo sbpround($totalfees, 2); ?></td>
                                        </tr>
                                        <tr>
                                            <th>Subtotal</th>
                                            <td class="text-end">$<?php echo sbpround(($subtotal), 2); ?></td>
                                        </tr>
                                        <tr>
                                            <th><?= $taxexemptstr ?><?php echo ucwords(strtolower($cantaxstr)); ?></th>
                                            <td class="text-end">$<?php echo sbpround($totaltax, 2); ?></td>
                                        </tr>
                                        <tr class="text-primary" onclick="totalROBreakdown()">
                                            <th><strong>Total RO</strong></th>
                                            <td class="text-end"><strong>$<?php echo number_format($totalro, 2); ?></strong>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th onclick="$('html, body').animate({ scrollTop: 0 });showExtras('pmts');">Payments</th>
                                            <td class="text-end">$<?php if ($totalpayments > 0) {
                                                    echo "< ";
                                                };
                                                echo sbpround($totalpayments, 2);
                                                if ($totalpayments > 0) {
                                                    echo " >";
                                                } ?><?= (!empty($totalsurcharge) ? ' (+ $' . $totalsurcharge . ' surcharge)' : '') ?></td>
                                        </tr>
                                        <tr>
                                            <th>Balance</th>
                                            <td class="text-end">$<?php echo sbpround($balancero, 2); ?></td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php } ?>
    </div>


    <input id="activecomid" type="hidden">
    <input type="hidden" id="reload" value="no">
    <input type="hidden" id="invpath" value="">
    <input type="hidden" id="joblist" name="joblist">
    <input type="hidden" id="restorelist" name="restorelist">
    <input type="hidden" id="currstat" name="currstat">
    <input type="hidden" id="signame" name="signame">
    <input type="hidden" id="printit" name="printit" value="no">
    <input type="hidden" id="currentsource" name="currentsource" value="<?php echo ucwords(strtolower($Source)); ?>">

</main>

<?php
include getFunctionsGlobal($component);
include getModalsGlobal($component);
include getScriptsGlobal($component);
include getFooterGlobal($component);
?>
