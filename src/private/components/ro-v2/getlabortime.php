<?php
require CONN;
$shopid = $_COOKIE['shopid'];
$roid = $_REQUEST['roid'];
$complaintid = $_REQUEST['complaintid'] ?? "";
$json_out = array();
$tsnow = localTimeStamp($shopid);

$stmt = "SELECT l.complaintid, SUM(l.LaborHours) as labor_hours, ABS(SUM(TIME_TO_SEC(timediff(COALESCE(lt.enddatetime, '$tsnow'),lt.startdatetime)) / 3600)) timeclock_hours, count(lt.LaborID) num_lines FROM labor l LEFT JOIN labortimeclock lt ON lt.laborid = l.LaborID AND l.shopid = lt.shopid AND l.ROID = lt.roid
WHERE l.shopid = ? AND l.roid = ? GROUP BY l.complaintid;";

if ($query = $conn->prepare($stmt)){
    $query->bind_param("si", $shopid, $roid);
    $query->execute();
    $results = $query->get_result();
    while($rs = $results->fetch_assoc()){
        $json_out[] = $rs;
    }
    $query->close();
}

echo json_encode($json_out);