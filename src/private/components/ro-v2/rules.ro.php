<?php
require_once(CONN);

$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
$roid = filter_var($_GET['roid'], FILTER_SANITIZE_STRING);
$DiscountAmt=(isset($_GET['DiscountAmt'])?$_GET['DiscountAmt']:'');
$jshopid = (isset($_GET['jshopid'])?filter_var($_GET['jshopid'], FILTER_SANITIZE_STRING):'');

if(!empty($jshopid))
{
    $stmt = "select id from joinedshops where shopid = ? and joinedshopid = ?";
    if ($query = $conn->prepare($stmt))
    {
        $query->bind_param("ss",$shopid,$jshopid);
        $query->execute();
        $query->store_result();
        $numrows = $query->num_rows();
        if ($numrows > 0)
            $shopid = $jshopid;
    }
}
// checking variables

$vin = isset($vin) ? $vin : "";
$DateIn = isset($DateIn) ? $DateIn : "";
$customerlast = isset($customerlast) ? $customerlast : "";
$techname = '';

$stmt = "select showcfp,commoldtonew,pph,requirerevapp,sourcerequired,showgponro,requireinspectiontofinal,motor,collapseissues,showtimeclockoncomplaint from settings where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($showcfp,$commoldtonew,$haspph,$requirerevapp,$sourcerequired,$showgponro,$requireinspectiontofinal,$motor,$collapseissues,$showtimeclockoncomplaint);
    $query->fetch();
    $query->close();
}

// check for a calendar item matching
$schdate = "";
$sstmt = "select schdate,schtime from schedule USE INDEX(sid) where shopid = ? and roid = ? and deleted = 'no' order by schdate desc";
if ($squery = $conn->prepare($sstmt)){
    $squery->bind_param("si",$shopid,$roid);
    $squery->execute();
    $squery->bind_result($scheduledate,$scheduletime);
    $squery->fetch();
    $squery->close();
    $schdate = $scheduledate." ".$scheduletime;
}

$pdusername = $pdtype = '';
$fees = isset($_GET['fees'])?$_GET['fees']:0;

$stmt = "select username,apikey from apilogin where shopid = '$shopid' and companyname='prodemand'";
if ($query = $conn->prepare($stmt)){
    $query->execute();
    $query->bind_result($pdusername,$pdtype);
    $query->fetch();
    $query->close();
}

function seconds2human($ss) {

    $time=$ss; //whatever
    $seconds = $time%60;
    $mins = floor($time/60)%60;
    $hours = floor($time/60/60)%24;
    $days = floor($time/60/60/24);

    $mins = substr("0".$mins,-2);
    $hours = substr("0".$hours,-2);

    if ($days > 0){
        $days = substr("0".$days,-2);
        return "$days:$hours:$mins";
    }else{
        return "00:$hours:$mins";
    }
}

$betafeatures = getBetaFeatures();
if(is_array($betafeatures) && in_array('2', $betafeatures))
$oilchangestickers = 'yes';
else
$oilchangestickers = 'no';

if(is_array($betafeatures) && in_array('1', $betafeatures))
$aitool = 'yes';
else
$aitool = 'no';

if(is_array($betafeatures) && in_array('4', $betafeatures))
$motordriven = true;
else
$motordriven = false;

$empid = (isset($_COOKIE['empid'])?$_COOKIE['empid']:'');
$changerotypes = "NO";
$showgpinro = "YES";
$edittechpaidlog = "NO";
$editcommentsinro = "NO";
$changerostatus = "YES";
$deletepaymentsreceived = "NO";
$sendupdates="NO";
$changerodate = "no";
$reopenro = "no";
$partsordering = "YES";
$onlytechissues = "NO";
$editparts = "YES";
$editlabor = "YES";
$editsublet = "YES";
$showsublet = "YES";
$editcannedjobs = "YES";
$changevistatus = "YES";
$showcustomerinfo = "YES";
$editmilesin = "YES";
$editmilesout = "YES";
$vieweditcomments = "YES";
$viewhistory = "YES";
$techshowpartscostonro = "YES";
$showlaborhoursonro = "YES";
$pphaccess = "YES";

$stmt = "select employeelast,employeefirst,ChangeRepairOrderTypes,showgpinro,edittechpaidlog,editcommentsinro,changerostatus,upper(candelete),upper(InventoryLookup),deletepaymentsreceived,upper(sendupdates),changerodate,reopenro,partsordering,pphaccess from employees where shopid = '$shopid' and id = $empid";
if ($query = $conn->prepare($stmt)){
    $query->execute();
    $query->bind_result($employeelast,$employeefirst,$changerotypes,$showgpinro,$edittechpaidlog,$editcommentsinro,$changerostatus,$candelete,$InventoryLookup,$deletepaymentsreceived,$sendupdates,$changerodate,$reopenro,$partsordering,$pphaccess);
    $query->fetch();
    $query->close();
}

if($_COOKIE['mode'] == 'tech2' && $empid!='Admin')
{
    $stmt = "select orderparts,changerostatus,onlytechissues,upper(editparts),upper(editlabor),upper(editcannedjobs),upper(editsublet),upper(changevistatus),upper(showsublet),upper(showcustomerinfo),upper(editmilesin),upper(editmilesout),upper(vieweditcomments),upper(viewhistory),upper(showpartscostonro),upper(showlaborhoursonro) from techpermissions where shopid = '$shopid' and empid = $empid";
    if ($query = $conn->prepare($stmt))
    {
       $query->execute();
       $query->bind_result($partsordering,$changerostatus,$onlytechissues,$editparts,$editlabor,$editcannedjobs,$editsublet,$changevistatus,$showsublet,$showcustomerinfo,$editmilesin,$editmilesout,$vieweditcomments,$viewhistory,$techshowpartscostonro,$showlaborhoursonro);
       $query->fetch();
       $query->close();
    }

    $techname = strtoupper($employeelast.", ".$employeefirst);
}

if ($empid == "Admin"){
    $changerotypes = "yes";
    $showgpinro = "yes";
    $edittechpaidlog = "YES";
    $editcommentsinro = "YES";
    $changerostatus = "YES";
    $candelete = "YES";
    $InventoryLookup = "YES";
    $deletepaymentsreceived = "yes";
    $sendupdates="YES";
    $changerodate = "yes";
    $reopenro = 'yes';
}
$changerotypes = strtoupper($changerotypes);
$showgpinro = strtoupper($showgpinro);
$edittechpaidlog = strtoupper($edittechpaidlog);
$editcommentsinro = strtoupper($editcommentsinro);
$changerostatus =  strtoupper($changerostatus);
$deletepaymentsreceived = strtoupper($deletepaymentsreceived);
$partsordering = strtoupper($partsordering);

// check to see if shop registered for CIMS
$locationid = "";
$dealerid = "";
$useridpassword = "";

$stmt = "select locationid,dealerid,useridpassword from cims where shopid = '$shopid'";
//echo $stmt;
if ($query = $conn->prepare($stmt)){
    //$query->bind_param("s",$shopid);
    $query->execute();
    $query->store_result();
    $numrows = $query->num_rows();
    if ($numrows > 0){
        $query->bind_result($locationid,$dealerid,$useridpassword);
        $query->fetch();
    }
}else{
    echo "Company Prepare Failed: (" . $conn->errno . ") " . $conn->error;
}


$commlist = "";
$stmt = "select complaintid from complaints where shopid = ? and roid = ? and cstatus = 'no'";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("si",$shopid,$roid);
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_array()){
        $commlist .= $rs['complaintid'].",";
    }
}

if (substr($commlist,-1) == ","){
    $commlist = substr($commlist,0,strlen($commlist)-1);
}


$stmt = "delete from parts where shopid = ? and roid = ? and complaintid not in ($commlist) and "
    . "supplier != 'discount' and partdesc != 'discount' and partnumber != 'discount' and partcategory"
    . " != 'discount'";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("si",$shopid,$roid);
    $query->execute();
    $conn->commit();
    $query->close();
}

$stmt = "delete from labor where shopid = ? and roid = ? and complaintid not in ($commlist) and "
    . "labor != 'discount' and tech != 'discount, discount'";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("si",$shopid,$roid);
    $query->execute();
    $conn->commit();
    $query->close();
}



if (isset($_COOKIE['plan'])){
    $plan = $_COOKIE['plan'];
}else{
    $plan = "none";
}

$discountcount = 0;
$stmt = "select count(*) c from discountreasons where shopid = '$shopid'";
if ($query = $conn->prepare($stmt)){
    $query->execute();
    $query->bind_result($discountcount);
    $query->fetch();
    $query->close();
}


$stmt = "select yearlabel,makelabel,modellabel,enginelabel,cylinderlabel,translabel,licenselabel,statelabel,fleetlabel,currmileagelabel,colorlabel,drivelabel,vinlabel"
    . " from vehiclelabels where shopid = '$shopid'";
if ($query = $conn->prepare($stmt)){
    $query->execute();
    $query->bind_result($yearlabel,$makelabel,$modellabel,$enginelabel,$cylinderlabel,$translabel,$licenselabel,$statelabel,$fleetlabel,$currmileagelabel,$colorlabel,$drivelabel,$vinlabel);
    $query->fetch();
    $query->close();
}


$stmt = "SELECT IFNULL((SELECT mycarfax FROM carfaxfeatures WHERE shopid = '$shopid'),'eula') a;";
if ($query = $conn->prepare($stmt)){
    $query->execute();
    $query->bind_result($mycarfax);
    $query->fetch();
    $query->close();
}

setcookie("quoteid","",time()-3600);

// check for a quoteid
$picktechs = "no";
$issuecount = 0;
$pickissues = "no";
$quoteid = 0;

if (isset($_GET['quoteid']) && $Status != 'CLOSED'){

    $quoteid = filter_var($_GET['quoteid'], FILTER_SANITIZE_STRING);

    if(isset($_GET['newquote']))
    {

        $stmt = "select id from quotelabor where shopid = '$shopid' and quoteid = $quoteid and tech=''";
        if ($query = $conn->prepare($stmt))
        {
            $query->execute();
            $labresult = $query->get_result();
            $labrows = $labresult->num_rows;
        }

        if ($labrows>0)$picktechs = "yes";
    }
    else
    {
        // count the vehicle issues.  if there is only 1, then add all parts and labor to it.  if there are more than one, prompt user to select the vehicle issue
        $stmt = "select count(*) from complaints where shopid = '$shopid' and roid = '$roid' and cstatus = 'no'";
        if ($query = $conn->prepare($stmt)){
            $query->execute();
            $query->store_result();
            $query->bind_result($issuecount);
            $query->fetch();
            $query->close();
        }

        $stmt = "select id from quotelabor where shopid = '$shopid' and quoteid = $quoteid";
        if ($query = $conn->prepare($stmt))
        {
            $query->execute();
            $labresult = $query->get_result();
            $labrows = $labresult->num_rows;
        }

        if ($issuecount == 1 && $labrows==0)
        {

            $stmt = "select complaintid from complaints where shopid = '$shopid' and roid = '$roid'";
            if ($query = $conn->prepare($stmt)){
                $query->execute();
                $query->store_result();
                $query->bind_result($tempcomid);
                $query->fetch();
                $query->close();
            }else{
                echo "2Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }

            // get the parts and labor from the quote
            $tempdate = date("Y-m-d");
            $stmt = "insert into parts (shopid,PartNumber,PartDesc,PartPrice,Quantity,ROID,Supplier,Cost,PartInvoiceNumber,PartCode,LineTTLPrice,LineTTLCost,Date,PartCategory,complaintid,discount"
                . ",net,bin,tax,overridematrix,ponumber,pstatus,deleted,cannedjobsid) select shopid,partnumber,part,price,qty,$roid,supplier,partcost,'',partcode,extprice,partcost*qty,'$tempdate',matrixcat,$tempcomid,discount,net,bin,taxable,'no'"
                . ",'','','no',cannedjobsid from quoteparts where shopid = '$shopid' and quoteid = $quoteid";
            //echo $stmt;
            if ($query = $conn->prepare($stmt)){
                if ($query->execute()){
                    $conn->commit();
                }else{
                    echo $conn->errno;
                }
            }else{
                echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }

            if(strtoupper($updateinvonadd)=='YES')
            {

                $stmt = "select qty,partnumber from quoteparts where shopid = ? and quoteid = ?";
                if ($query = $conn->prepare($stmt)){
                    $query->bind_param("ss",$shopid,$quoteid);
                    $query->execute();
                    $r = $query->get_result();
                    while ($rs = $r->fetch_array()){
                        $pq = $rs['qty'];
                        $stmt = "update partsinventory set onhand = onhand - $pq, netonhand = netonhand - $pq where shopid = ? and partnumber = ?";
                        if ($query = $conn->prepare($stmt)){
                            $query->bind_param("ss",$shopid,$rs['partnumber']);
                            $query->execute();
                            $conn->commit();
                            $query->close();
                        }

                        $stmt = "select NetOnHand,ReOrderLevel from partsinventory where shopid = ? and partnumber = ?";
                        if ($query = $conn->prepare($stmt))
                        {
                            $query->bind_param("ss",$shopid,$rs['partnumber']);
                            $query->execute();
                            $query->bind_result($netonhand,$reorderlevel);
                            $query->fetch();
                            $query->close();
                        }

                        if ($netonhand<=$reorderlevel && $netonhand!='' && $reorderlevel!='')
                        {
                            $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid = ? AND s.notification_type='166'";
                            if ($query = $conn->prepare($stmt))
                            {
                                $query->bind_param("s", $shopid);
                                $query->execute();
                                $query->store_result();
                                $num_rows = $query->num_rows;
                                if($num_rows>0)
                                {
                                    $query->bind_result($textcontent, $emailcontent, $popupcontent);
                                    $query->fetch();

                                    $popupcontent = str_replace("*|PARTNUMBER|*", $rs['partnumber'], $popupcontent);
                                    $emailcontent = str_replace("*|PARTNUMBER|*", $rs['partnumber'], $emailcontent);
                                    $textcontent = str_replace("*|PARTNUMBER|*", $rs['partnumber'], $textcontent);
                                    $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'166',?,?,?)";
                                    if ($squery = $conn->prepare($stmt))
                                    {
                                        $squery->bind_param("ssss", $shopid, $popupcontent, $textcontent, $emailcontent);
                                        $squery->execute();
                                        $conn->commit();
                                        $squery->close();
                                    }
                                }
                            }
                        }

                    }
                }
            }

            $stmt = "delete from quoteparts where shopid = '$shopid' and quoteid = $quoteid";
            if ($query = $conn->prepare($stmt)){
                if ($query->execute()){
                    $conn->commit();
                }else{
                    echo $conn->errno;
                }
            }else{
                echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }

            // Get Sublet for transfer

            // sublet 5/9/19

            $tempdate = date("Y-m-d");

            $stmt = "select subletid from sublet where shopid = '$shopid' order by subletid desc limit 1";
            if ($query = $conn->prepare($stmt)){
                $query->execute();
                $query->store_result();
                $query->bind_result($newsubletid);
                $query->fetch();
                $query->close();
            }else{
                echo "3Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }


            $stmt = " SELECT shopid,$roid,`description`,price,cost,invnum,`supplier`,$tempcomid from quotesublet "
                . " where shopid = '$shopid' and quoteid = $quoteid ";

            if ($query = $conn->prepare($stmt)){
                $query->execute();
                $result = $query->get_result();
                while ($row = $result->fetch_array()){
                    $newsubletid = $newsubletid + 1;
                    $desc = $row["description"];
                    $subprice = $row["price"];
                    $subcost = $row["cost"];
                    $subinvnum = $row["invnum"];
                    $subsupplier = $row["supplier"];

                    $stmt = "insert into sublet (shopid,SubletID,ROID,SubletDesc,SubletPrice,SubletCost,SubletInvoiceNo,SubletSupplier,complaintid)"
                        . "values ('$shopid',$newsubletid,$roid,'$desc',$subprice,$subcost,'$subinvnum','$subsupplier',$tempcomid)";


                    if ($query = $conn->prepare($stmt)){
                        if ($query->execute()){
                            $conn->commit();
                        }else{
                            echo $conn->error;
                        }
                    }

                }

            }

            $stmt = "delete from quotesublet where shopid = '$shopid' and quoteid = $quoteid";
            if ($query = $conn->prepare($stmt)){
                if ($query->execute()){
                    $conn->commit();
                }else{
                    echo $conn->errno;
                }
            }else{
                echo "Sublet Delete failed: (" . $conn->errno . ") " . $conn->error;
            }

            $stmt = "delete from quotes where shopid = '$shopid' and id = $quoteid";
            if ($query = $conn->prepare($stmt)){
                if ($query->execute()){
                    $conn->commit();
                }else{
                    echo $conn->errno;
                }
            }else{
                echo "QuoteDelete failed: (" . $conn->errno . ") " . $conn->error;
            }

            setcookie("quoteid","",time()-3600);

        }else{

            $pickissues = "yes";
        }

    }
}


$stmt = "select `ROID`,`CustomerID`,`Writer`,`DateIn`,`TimeIn`,`TaxRate`,`PurchaseOrderNumber`,`VehID`,`Customer`,`VehInfo`,`vehyear`,`vehmake`,`vehmodel`,`vehlicense`,`vehstate`,`VehLicNum`,`WrittenBy`,`Status`,`StatusDate`,`TotalLbrHrs`,`TotalLbr`,`TotalPrts`,`TotalSublet`,`TotalRO`,`CustomerAddress`,`CustomerCSZ`,`CustomerPhone`,`customercity`,`customerstate`,`customerzip`,`VehicleMiles`,`MilesOut`,`Vin`,`CustomerWork`,`MajorComplaint`,`DatePromised`,`Comments`,`DiscountAmt`,`DiscountPercent`,`WarrMos`,`WarrMiles`,`SalesTax`,`PartsCost`,`ROType`,`VehEngine`,`Cyl`,`VehTrans`,`HowPaid`,`AmtPaid1`,`CheckNum1`,`HowPaid2`,`AmtPaid2`,`CheckNum2`,`EstimateAmt`,`NoFollow`,`AccountPaid`,`HazardousWaste`,`Source`,`Rev1Amt`,`Rev1Date`,`Rev1Phone`,`Rev1Time`,`Rev1By`,`Rev2Amt`,"
    ."`Rev2Date`,`Rev2Phone`,`Rev2Time`,`Rev2By`,`CellPhone`,coalesce(`storagefee`,0),`cellprovider`,`UserFee1`,`UserFee2`,`UserFee3`,`userfee1label`,`userfee2label`,`userfee3label`,`UserFee1amount`,`UserFee2amount`,`UserFee3amount`,`userfee1percent`,`userfee2percent`,`userfee3percent`,`userfee1type`,`userfee2type`,`userfee3type`,`LastFirst`,`customerfirst`,`customerlast`,`DriveType`,`TotalFees`,`Fax`,`Subtotal`,`CB`,`DateInspection`,`DateAuthorization`,`DateParts`,`DateWork`,`DateInProcess`,`DateHold`,`DateFinal`,`DateDelivered`,`DateClosed`,`OrigRO`,`OrigTech`,`PartsOrdered`,`FinalDate`,`Exported`,`LaborTaxRate`,`SubletTaxRate`,`complainttable`,`gp`,`contact`,`balance`,`fleetno`,`email`,`recommendedrepairs`,`customvehicle1`,`customvehicle2`,`customvehicle3`,`customvehicle4`,`customvehicle5`,`customvehicle6`,"
    ."`customvehicle7`,`customvehicle8`,`customvehicle1label`,`customvehicle2label`,`customvehicle3label`,`customvehicle4label`,`customvehicle5label`,`customvehicle6label`,`customvehicle7label`,`customvehicle8label`,`tagnumber`,`qb`,`datetimepromised`,`spousename`,`spousework`,`spousecell`,`overridestatusdate`,`tirepressureinlf`,`tirepressureinrf`,`tirepressureinlr`,`tirepressureinrr`,`tirepressureoutlf`,`tirepressureoutrf`,`tirepressureoutlr`,`tirepressureoutrr`,`treaddepthlf`,`treaddepthrf`,`treaddepthlr`,`treaddepthrr`,`discounttaxable`,`ponumber`,`origshopid`,`inspectioncomplete`,`invoiceemailed`,`estimateemailed`,`inspectionemailed`,`updatesent`,`coupon`,`taxpartsprice`,`ts`,`waiter`,DefaultTech,canadiantax,recall"
    ." from repairorders where shopid = ? and roid = ?";


if ($query = $conn->prepare($stmt)){

    $query->bind_param("si",$shopid,$roid);
    $query->execute();
    $query->store_result();
    $num_roid_rows = $query->num_rows;
    if ($num_roid_rows > 0){
        $query->bind_result($ROID,$CustomerID,$Writer,$DateIn,$TimeIn,$TaxRate,$PurchaseOrderNumber,$VehID,$Customer,$VehInfo,$vehyear,$vehmake,$vehmodel,$vehlicense,$vehstate,$VehLicNum,$WrittenBy,$Status,$StatusDate,$TotalLbrHrs,$TotalLbr,$TotalPrts,$TotalSublet,$TotalRO,$CustomerAddress,$CustomerCSZ,$CustomerPhone,$customercity,$customerstate,$customerzip,$VehicleMiles,$MilesOut,$Vin,$CustomerWork,$MajorComplaint,$DatePromised,$Comments,$DiscountAmt,$DiscountPercent,$WarrMos,$WarrMiles,$SalesTax,$PartsCost,$ROType,$VehEngine,$Cyl,$VehTrans,$HowPaid,$AmtPaid1,$CheckNum1,$HowPaid2,$AmtPaid2,$CheckNum2,$EstimateAmt,$NoFollow,$AccountPaid,$HazardousWaste,$Source,$Rev1Amt,$Rev1Date,$Rev1Phone,$Rev1Time,$Rev1By,$Rev2Amt,$Rev2Date,$Rev2Phone,$Rev2Time,$Rev2By,$CellPhone,$storagefee,$cellprovider,$UserFee1,$UserFee2,$UserFee3,$userfee1label,$userfee2label,$userfee3label,$UserFee1amount,$UserFee2amount,$UserFee3amount,$userfee1percent,$userfee2percent,$userfee3percent,$userfee1type,$userfee2type,$userfee3type,$LastFirst,$customerfirst,$customerlast,$DriveType,$TotalFees,$Fax,$Subtotal,$discountreason,$DateInspection,$DateAuthorization,$DateParts,$DateWork,$DateInProcess,$DateHold,$DateFinal,$DateDelivered,$DateClosed,$OrigRO,$OrigTech,$PartsOrdered,$FinalDate,$Exported,$LaborTaxRate,$SubletTaxRate,$complainttable,$gp,$contact,$balance,$fleetno,$email,$recommendedrepairs,$customvehicle1,$customvehicle2,$customvehicle3,$customvehicle4,$customvehicle5,$customvehicle6,$customvehicle7,$customvehicle8,$customvehicle1label,$customvehicle2label,$customvehicle3label,$customvehicle4label,$customvehicle5label,$customvehicle6label,$customvehicle7label,$customvehicle8label,$tagnumber,$qb,$datetimepromised,$spousename,$spousework,$spousecell,$overridestatusdate,$tirepressureinlf,$tirepressureinrf,$tirepressureinlr,$tirepressureinrr,$tirepressureoutlf,$tirepressureoutrf,$tirepressureoutlr,$tirepressureoutrr,$treaddepthlf,$treaddepthrf,$treaddepthlr,$treaddepthrr,$discounttaxable,$ponumber,$origshopid,$inspectioncomplete,$invoiceemailed,$estimateemailed,$inspectionemailed,$updatesent,$coupon,$taxpartsprice,$ts,$waiter,$DefaultTech,$canadiantax,$recall);
        $query->fetch();
        $rofetch = "none";
    }else{
        $rofetch = $conn->error;$CustomerID='';
    }
    $query->close();


}else{
    echo "4Prepare failed: (" . $conn->errno . ") " . $conn->error;
    $CustomerID='';
}

if($_COOKIE['mode'] != 'full' && $Status=='CLOSED')
{
  header("location:/v2/wip/wip.php");
  exit;
}

$cszarray = array();
if(!empty($customercity))$cszarray[] = $customercity;
if(!empty($customerstate))$cszarray[] = $customerstate;
if(!empty($customerzip))$cszarray[] = $customerzip;

$Vin = strtoupper($Vin);
$startdatetime = strtotime($DateIn." ".$TimeIn);
$currdatetime = strtotime(localTimeStamp($shopid));
$numsecs = $currdatetime - $startdatetime;
$edisplaytime = seconds2human($numsecs);
$oldtotalro = $TotalRO;
if (empty($waiter))$waiter = "select";

if (strpos($Vin,"'") && $Status != 'CLOSED'){
    $Vin = str_replace("'","",$Vin);
    $stmt = "update repairorders set vin = '$Vin' where shopid = '$shopid' and roid = $roid";
    if ($query = $conn->prepare($stmt)){
        $query->execute();
        $conn->commit();
        $query->close();
    }

    $stmt = "update vehicles set vin = '$Vin' where shopid = '$shopid' and vehid = $VehID";
    //echo $stmt;
    if ($query = $conn->prepare($stmt)){
        $query->execute();
        $conn->commit();
        $query->close();
    }

}


$CellPhone = str_replace("\r\n","",$CellPhone);

$stmt = "select discount,coalesce(cast( `column 27` as decimal(8,2)),0) as customerdiscountamt, customertype, extension from customer where shopid = '$shopid' and customerid = $CustomerID";
if ($query = $conn->prepare($stmt)){
    $query->execute();
    $query->bind_result($discounttype,$customerdiscountamount,$customertype,$ext);
    $query->fetch();
    $query->close();
}

if (strlen($customertype) == 0){
    $customertype = "CASH";
}

$customertype = strtoupper($customertype);

if (strlen($origshopid) == 0){
    $origshopid = $shopid;
}

if ($origshopid != $shopid){
    $stmt = "select color from vehicles where shopid = '$origshopid' and vehid = $VehID";
}else{
    $stmt = "select color from vehicles where shopid = '$shopid' and vehid = $VehID";
}
//echo "stmt:".$stmt;
$color = "";
if ($query = $conn->prepare($stmt)){
    $query->execute();
    $query->bind_result($color);
    $query->fetch();
    $query->close();
    $colorfetch = "none";
}else{
    $colorfetch = $conn->error;
}

/*if (strtolower($Status) == "closed"){
    if(!empty($jshopid))
    header("Location:roclosed.php?jshopid=".$jshopid."&roid=$roid");
    else
    header("Location:roclosed.php?roid=$roid");
    exit;
}*/

$Status = strtoupper($Status);

if (is_numeric(substr($Status,0,1))){
    $status = substr($Status,1);
}else{
    $status = $Status;
}

if (strlen($Source) == 0){
    $Source = "NONE";
}

$statusflag = "";

if ($origshopid != $shopid){
    $stmt = "select color from vehicles where shopid = '$origshopid' and vehid = $VehID";
}else{
    $stmt = "select color from vehicles where shopid = '$shopid' and vehid = $VehID";
}
//echo "stmt:".$stmt;
$color = "";
if ($query = $conn->prepare($stmt)){
    $query->execute();
    $query->bind_result($color);
    $query->fetch();
    $query->close();
    $colorfetch = "none";
}else{
    $colorfetch = $conn->error;
}

// get company information
$cstmt = "select readonly,showinstockparts,definvmsgemail,companyname,companyphone,chargeshopfeeson,userfee1,userfee2,userfee3,userfee1amount,userfee2amount,userfee3amount,userfee1max"
    .",userfee2max,userfee3max,userfee1taxable,userfee2taxable,userfee3taxable,userfee1applyon,userfee2applyon,userfee3applyon,requirebalancero,requirepayments,requiresource,requireoutmileage,rodisclosure,rowarrdisclosure,alldatausername"
    .",alldatapassword,estguide,milesinlabel,milesoutlabel,replacerowithtag,merchantaccount,merchantid,merchantpassword,inspectionaspopup,alldatarepair,requiretechclockout,showgp,hazwastetaxable,storagetaxable"
    .",fullscreenissues,estimatetitle,invoicetitle,companyemail,scrolltotalswindow,hst,pst,gst,qst,chargehst,chargepst,chargegst,chargeqst,hstapplyon,pstapplyon,gstapplyon,qstapplyon,authnetclientkey,defaultrochild,updateinvonadd,newpackagetype,cfpid,showwaiting,HourlyRate,hourlyrate2,hourlyrate3,hourlyrate4,hourlyrate5,hourlyrate6,hourlyrate7,hourlyrate8,hourlyrate9,hourlyrate10,hourlyrate1label,hourlyrate2label,hourlyrate3label,hourlyrate4label,hourlyrate5label,hourlyrate6label,hourlyrate7label,hourlyrate8label,hourlyrate9label,hourlyrate10label,profitboost,companyzip,matco,worldpac,datestarted,requiremileagetoprintro,lineitemcomplete,sortvi,showpartscostonro,showtechoverhours,companystate from company where shopid = ?";

if ($cquery = $conn->prepare($cstmt)){
    $cquery->bind_param("s",$shopid);
    $cquery->execute();
    $cquery->store_result();
    $cquery->bind_result($readonly,$showinstockparts,$definvmsgemail,$shopname,$shopphone,$chargeshopfeeson,$userfee1,$userfee2,$userfee3,$userfee1amount,$userfee2amount,$userfee3amount,$userfee1max,$userfee2max,$userfee3max,$userfee1taxable,$userfee2taxable,$userfee3taxable,$userfee1applyon,$userfee2applyon,$userfee3applyon,$requirebalancero,$requirepayments,$requiresource,$requireoutmileage,$rodisclosure,$rowarrdisclosure,$alldatausername,$alldatapassword,$estguide,$milesinlabel,$milesoutlabel,$replacerowithtag,$merchantaccount,$merchantid,$merchantpassword,$inspectionaspopup,$alldatarepair,$requiretechclockout,$showgp,$hazwastetaxable,$storagetaxable,$fullscreenissues,$estimatetitle,$invoicetitle,$companyemail,$scrolltotalswindow,$hst,$pst,$gst,$qst,$chargehst,$chargepst,$chargegst,$chargeqst,$hstapplyon,$pstapplyon,$gstapplyon,$qstapplyon,$authnetclientkey,$defaultrochild,$updateinvonadd,$newpackagetype,$cfpid,$showwaiting,$hrate1,$hrate2,$hrate3,$hrate4,$hrate5,$hrate6,$hrate7,$hrate8,$hrate9,$hrate10,$hlabel1,$hlabel2,$hlabel3,$hlabel4,$hlabel5,$hlabel6,$hlabel7,$hlabel8,$hlabel9,$hlabel10,$isprofitboost,$companyzip,$matco,$worldpacyes,$datestarted,$requiremileagetoprintro,$lineitemcomplete,$sortvi,$showpartscostonro,$showtechoverhours,$company_state);
    $cquery->fetch();
    $companyfetch = "none";
}else{
    $companyfetch = $conn->error;
}

$newpackagetype = strtolower($newpackagetype);
if ($definvmsgemail == ""){
    $shopphone = formatPhone($shopphone);
    $definvmsgemail = "Your repair order #$roid from $shopname is attached.  Please look it over and let us know if you have any questions. $shopphone";
}

$definvmsgemail = str_replace("repair order from","repair order #$roid from",$definvmsgemail);

$matco_records = array();

if($matco=='yes')
{
    $stmt = "select diagnose_record_id,report_type from matco_records where shopid = ? and roid = ?";
    if($query = $conn->prepare($stmt))
    {
        $query->bind_param("si",$shopid,$roid);
        $query->execute();
        $result = $query->get_result();
        while($row = $result->fetch_array())
            $matco_records[] = array('type'=>$row['report_type'],'id'=>$row['diagnose_record_id']);
    }
}


$stmt = "SELECT emailinvoice,emailinvoicesubject,emailestimate,emailestimatesubject,emailesigrequest,emailesigrequestsubject from companymessages WHERE shopid = '$shopid'";
if ($query = $conn->prepare($stmt)){
    $query->execute();
    $query->store_result();
    $nummsgrows = $query->num_rows;
    if ($nummsgrows > 0){
        $query->free_result();
        $query->execute();
        $query->bind_result($emailinvoice,$emailinvoicesubject,$emailestimate,$emailestimatesubject,$emailesigrequest,$emailesigrequestsubject);
        $query->fetch();
    }else{
        $emailinvoice = str_replace("[shopname]",$_COOKIE['shopname'],$definvmsgemail);
        $emailinvoicesubject = 'Your repair invoice #'.$roid.' from '.$_COOKIE['shopname'];
        $emailestimate = str_replace("[shopname]",$_COOKIE['shopname'],$definvmsgemail);
        $emailestimatesubject = 'Your repair estimate #'.$roid.' from '.$_COOKIE['shopname'];
        $emailesigrequest = $_COOKIE['shopname'].' is requesting an E-Signature on a document. Please click the link below to E-Sign the document. Thank you for your business!';
        $emailesigrequestsubject = 'A Request for an E-Signature from '.$_COOKIE['shopname'];
    }
    $query->close();
}

// check for an mvr account
if ($origshopid != $shopid){
    $stmt = "select lastname,firstname,email,password from mvr.owner where sbpshopid = '$origshopid' and sbpcustomerid = $CustomerID";
}else{
    $stmt = "select lastname,firstname,email,password from mvr.owner where sbpshopid = '$shopid' and sbpcustomerid = $CustomerID";
}

$mvrlast = '';
$mvrfirst = '';
$mvremail = '';
$mvrpass = '';
if ($query = $conn->prepare($stmt)){

    $query->execute();
    $query->bind_result($mvrlast,$mvrfirst,$mvremail,$mvrpass);
    $query->fetch();
    $query->close();

}

//echo "merch:".$merchantaccount;
// canadian tax values:  $hst,$pst,$gst,$qst,$chargehst,$chargepst,$chargegst,$chargeqst
// values to change based on canadian tax values:  $userfee1taxable,$userfee2taxable,$userfee3taxable,$hazwastetaxable
// us values to overwrite:  $TaxRate,$LaborTaxRate,$SubletTaxRate

/*

1. if chargehst == "yes" or chargegst == "yes" or chargepst == "yes" or chargeqst == "yes"
2. get the rate that corresponds to the charges
3. overwrite the $TaxRate, $LaborTaxRate, $SubletTaxRate with the totals of the canadian amounts
4. set the $userfee1taxable,$userfee2taxable,$userfee3taxable,$hazwastetaxable to yes

*/

if(!is_numeric($companyzip) && !empty($canadiantax))
{
    $ctaxarr = explode(',',$canadiantax);
    $hst = $ctaxarr[0]??0;
    $pst = $ctaxarr[1]??0;
    $gst = $ctaxarr[2]??0;
    $qst = $ctaxarr[3]??0;
}

$canptaxrate = $canltaxrate = $canstaxrate = 0;
$cantaxstr = "";


if (!is_numeric($companyzip) && $chargehst == "yes" && $hst > 0){
    if(stripos($hstapplyon,'A')!==false || stripos($hstapplyon,'P')!==false) $canptaxrate += $hst;
    if(stripos($hstapplyon,'A')!==false || stripos($hstapplyon,'L')!==false) $canltaxrate += $hst;
    if(stripos($hstapplyon,'A')!==false || stripos($hstapplyon,'S')!==false) $canstaxrate += $hst;
    $hststr = "HST";
}else{
    $hst = 0;
    $hststr = "";
}
if (!is_numeric($companyzip) && $chargegst == "yes" && $gst > 0){
    if(stripos($gstapplyon,'A')!==false || stripos($gstapplyon,'P')!==false) $canptaxrate += $gst;
    if(stripos($gstapplyon,'A')!==false || stripos($gstapplyon,'L')!==false) $canltaxrate += $gst;
    if(stripos($gstapplyon,'A')!==false || stripos($gstapplyon,'S')!==false) $canstaxrate += $gst;
    $gststr = "GST";
}else{
    $gst = 0;
    $gststr = "";
}

if (!is_numeric($companyzip) && $chargepst == "yes" && $pst > 0){
    if(stripos($pstapplyon,'A')!==false || stripos($pstapplyon,'P')!==false) $canptaxrate += $pst;
    if(stripos($pstapplyon,'A')!==false || stripos($pstapplyon,'L')!==false) $canltaxrate += $pst;
    if(stripos($pstapplyon,'A')!==false || stripos($pstapplyon,'S')!==false) $canstaxrate += $pst;
    $pststr = "PST";
}else{
    $pst = 0;
    $pststr = "";
}

if (!is_numeric($companyzip) && $chargeqst == "yes" && $qst > 0){
    if(stripos($qstapplyon,'A')!==false || stripos($qstapplyon,'P')!==false) $canptaxrate += $qst;
    if(stripos($qstapplyon,'A')!==false || stripos($qstapplyon,'L')!==false) $canltaxrate += $qst;
    if(stripos($qstapplyon,'A')!==false || stripos($qstapplyon,'S')!==false) $canstaxrate += $qst;
    $qststr = "QST";
}else{
    $qst = 0;
    $qststr = "";
}

if (strlen($hststr) > 0){$cantaxstr .= $hststr;}

if (strlen($gststr) > 0){
    if (strlen($cantaxstr) > 0){
        $cantaxstr .= "+".$gststr;
    }else{
        $cantaxstr .= $gststr;
    }
}else{
    $cantaxstr .= $gststr;
}

if (strlen($pststr) > 0){
    if (strlen($cantaxstr) > 0){
        $cantaxstr .= "+".$pststr;
    }else{
        $cantaxstr .= $pststr;
    }
}else{
    $cantaxstr .= $pststr;
}

if (strlen($qststr) > 0){
    if (strlen($cantaxstr) > 0){
        $cantaxstr .= "+".$qststr;
    }else{
        $cantaxstr .= $qststr;
    }
}else{
    $cantaxstr .= $qststr;
}

if(!is_numeric($companyzip) && !empty($canadiantax)){
    $TaxRate = $canptaxrate;
    $LaborTaxRate = $canltaxrate;
    $SubletTaxRate = $canstaxrate;
}

$flagstr = "";
if (strlen($cantaxstr) > 0){
    $cantaxstr = "<b>(".$cantaxstr.")</b>";
    $flagstr = "<img style='max-width:50px' src='". IMAGE."/canadianflag.png'> ";
}

if ($TaxRate == 0 && $LaborTaxRate == 0 && $SubletTaxRate == 0)
    $taxexemptstr = "<span class='text-primary fw-bold'>Tax Exempt</span>";
else
    $taxexemptstr = "Sales Tax";

//$merchantaccount = "authorize.net";

$nexpartpwd = "";
$nexpartuser = "";
$nexstmt = "select password,username from nexpart where shopid = ?";
if ($nexquery = $conn->prepare($nexstmt)){
    $nexquery->bind_param("s",$shopid);
    $nexquery->execute();
    $nexquery->store_result();
    $nexquery->bind_result($nexpartpwd,$nexpartuser);
    $nexquery->fetch();
    $nexpartfetch = "none";
}else{
    $nexpartfetch = $conn->error;
}

// get worldpac
$worldpac = "no";
$wpstmt = "select 'yes' as wp from companyadds where shopid = '$shopid' and name = 'WorldPac Integrated Parts Ordering'";
if ($wpquery = $conn->prepare($wpstmt)){
    //$wpquery->bind_param("s",$shopid);
    $wpquery->execute();
    $wpquery->store_result();
    $wpquery->bind_result($worldpactest);
    $wpquery->fetch();
    $wpfetch = "none";
}else{
    $wpfetch = $conn->error;
}

if (strlen($worldpactest) > 0){
    $worldpac = "yes";
}

if ($plan == "gold" || $plan == "platinum" || $plan == "premier" || $plan == "premier plus"){
    $worldpac = "yes";
}

if ($Status == "CLOSED" || $Status == "FINAL"){
    $showtitle = $invoicetitle;
}else{
    $showtitle = $estimatetitle;
}

// get all the totals for the bottom
// get all the parts
$totalparts = 0; $totallabor = 0; $totalsublet = 0; $partsdiscount = 0; $labordiscount = 0;
$partsdiscountstmt = "select coalesce(sum(linettlprice),0) partsdiscount from parts where shopid = ? and roid = ? and partnumber = 'DISCOUNT'";
if ($pdquery = $conn->prepare($partsdiscountstmt)){
    $pdquery->bind_param("si",$shopid,$roid);
    $pdquery->execute();
    $pdquery->store_result();
    $pdquery->bind_result($partsdiscount);
    $pdquery->fetch();
    $partsdiscountfetch = "none";
}else{
    $partsdiscountfetch = $conn->error;
}
$labordiscountstmt = "select coalesce(sum(linetotal),0) labordiscount from labor where shopid = ? and roid = ? and tech = 'DISCOUNT, DISCOUNT'";
if ($ldquery = $conn->prepare($labordiscountstmt)){
    $ldquery->bind_param("si",$shopid,$roid);
    $ldquery->execute();
    $ldquery->store_result();
    $ldquery->bind_result($labordiscount);
    $ldquery->fetch();
    $labordiscountfetch = "none";
}else{
    $labordiscountfetch = $conn->error;
}
$labordiscount = round($labordiscount,2);
$laborstmt = "select coalesce(round(sum(linetotal),2),0) tlabor, coalesce(sum(laborhours),0) tlaborhours from labor where shopid = '$shopid' and roid = $roid and deleted = 'no'";
//echo $laborstmt."\r\n";
if ($lquery = $conn->prepare($laborstmt)){
    //$lquery->bind_param("si",$shopid,$roid);
    $lquery->execute();
    $lquery->store_result();
    $lquery->bind_result($totallabor,$totallaborhours);
    $lquery->fetch();
    $laborfetch = "none";
}else{
    $laborfetch = $conn->error;
}
//echo $totallabor."\r\n";
$partstmt = "select coalesce(round(sum(linettlprice),2),0) tprts, coalesce(sum(linettlcost),0) tprtscost from parts where shopid = '$shopid' and roid = $roid and deleted = 'no'";
//echo $partstmt."\r\n";
if ($pquery = $conn->prepare($partstmt)){
    $pquery->execute();
    $pquery->store_result();
    $pquery->bind_result($totalparts,$totalpartscost);
    $pquery->fetch();
    $allpartsfetch = "none";
}else{
    $allpartsfetch = $conn->error;
}
$totalparts = round($totalparts,2);
//echo $totalpartscost."\r\n";


$taxablesublet = 0;
$substmt = "select subletprice,taxable from sublet where shopid = ? and roid = ? and deleted = 'no'";
if ($squery = $conn->prepare($substmt)){
    $squery->bind_param("si",$shopid,$roid);
    $squery->execute();
    $sresult = $squery->get_result();
    $squery->store_result();
    while($srow = $sresult->fetch_assoc())
    {
        $totalsublet+= $srow['subletprice'];
        if($srow['taxable']=='yes')
            $taxablesublet+= $srow['subletprice'];
    }
    $subletfetch = "none";
}else{
    $subletfetch = $conn->error;
}

$totalpayments = 0;
$pmtstmt = "select coalesce(round(sum(amt),2),0) tamt,coalesce(round(sum(surcharge),2),0) from accountpayments where shopid = ? and roid = ?";
if ($squery = $conn->prepare($pmtstmt)){
    $squery->bind_param("si",$shopid,$roid);
    $squery->execute();
    $squery->store_result();
    $squery->bind_result($totalpayments,$totalsurcharge);
    $squery->fetch();
    $pmtfetch = "none";
}else{
    $pmtfetch = $conn->error;
}

if($totalpayments=='-0')$totalpayments = 0;

$rawlabor = $totallabor;
$rawparts = $totalparts;
$rawsublet = $totalsublet;

$totallabor = sbpround($totallabor,2);
$totalparts = sbpround($totalparts,2);
$totalsublet = sbpround($totalsublet,2);

$chargeshopfeeson = strtolower($chargeshopfeeson);

if($Status != 'CLOSED')
{
    if ($userfee1type == '%' && $userfee1percent > 0){
        if ($userfee1applyon == "all"){
            $UserFee1 = ($totallabor+$totalparts+$totalsublet) * ($userfee1amount/100);
        } else
        {
            $UserFee1 = 0;
            if (stripos($userfee1applyon,"labor") !== false){
                $UserFee1 += ($totallabor) * ($userfee1amount/100);
            }
            if (stripos($userfee1applyon, "parts") !== false){
                $UserFee1 += ($totalparts) * ($userfee1amount/100);
            }
            if (stripos($userfee1applyon, "sublet") !== false){
                $UserFee1 += ($totalsublet) * ($userfee1amount/100);
            }
        }
    }
//echo $userfee2type."|".$userfee2percent."\r\n";
    if ($userfee2type == '%' && $userfee2percent > 0){
        //echo $shopid."|".$UserFee2."|".$userfee2amount."\r\n";
        if ($userfee2applyon == "all"){
            $UserFee2 = ($totallabor+$totalparts+$totalsublet) * ($userfee2amount/100);
        } else {
            $UserFee2 = 0;
            if (stripos($userfee2applyon,"labor") !== false) {
                $UserFee2 += ($totallabor) * ($userfee2amount / 100);
            }
            if (stripos($userfee2applyon,"parts") !== false) {
                $UserFee2 += ($totalparts) * ($userfee2amount / 100);
            }
            if (stripos($userfee2applyon,"sublet") !== false) {
                $UserFee2 += ($totalsublet) * ($userfee2amount / 100);
            }
        }
    }
    if ($userfee3type == '%' && $userfee3percent > 0){
        if ($userfee3applyon == "all"){
            $UserFee3 = ($totallabor+$totalparts+$totalsublet) * ($userfee3amount/100);
        } else {
            $UserFee3 = 0;
            if (stripos($userfee3applyon,"labor") !== false) {
                $UserFee3 += ($totallabor) * ($userfee3amount / 100);
            }
            if (stripos($userfee3applyon, "parts") !== false) {
                $UserFee3 += ($totalparts) * ($userfee3amount / 100);
            }
            if (stripos($userfee3applyon,"sublet") !== false) {
                $UserFee3 += ($totalsublet) * ($userfee3amount / 100);
            }
        }
    }

    if ($userfee1max < $UserFee1 && $userfee1max > 0){$UserFee1 = $userfee1max;}
    if ($userfee2max < $UserFee2 && $userfee2max > 0){$UserFee2 = $userfee2max;}
    if ($userfee3max < $UserFee3 && $userfee3max > 0){$UserFee3 = $userfee3max;}
}


// calculate the sales tax
$userfee1tax = 0;
$userfee2tax = 0;
$userfee3tax = 0;
$userfee1taxamount = 0;
$userfee2taxamount = 0;
$userfee3taxamount = 0;
$hazwastetaxamount = 0;
$storagetaxamount =0;
$rawuserfee1tax = 0;
$rawuserfee2tax = 0;
$rawuserfee3tax = 0;
$rawhazwastetax = 0;

if ($UserFee1 > 0 && strtolower($userfee1taxable) == "taxable"){
    $userfee1tax = sbpround(($TaxRate/100) * sbpround($UserFee1,2));
    $rawuserfee1tax = ($TaxRate/100) * $UserFee1;
    $userfee1taxamount = $UserFee1;
}
if ($UserFee2 > 0 && strtolower($userfee2taxable) == "taxable"){
    $userfee2tax = sbpround(($TaxRate/100) * sbpround($UserFee2,2));
    $rawuserfee2tax = ($TaxRate/100) * $UserFee2;
    $userfee2taxamount = $UserFee2;
}
if ($UserFee3 > 0 && strtolower($userfee3taxable) == "taxable"){
    $userfee3tax = sbpround(($TaxRate/100) * sbpround($UserFee3,2));
    $rawuserfee3tax = ($TaxRate/100) * $UserFee3;
    $userfee3taxamount = $UserFee3;
}

if (strtolower($hazwastetaxable) == "yes"){
    $hazwastetax = sbpround(($TaxRate/100) * $HazardousWaste,2);
    $rawhazwastetax = ($TaxRate/100) * $HazardousWaste;
    $hazwastetaxamount = $HazardousWaste;
}else{
    $hazwastetax = 0.00;
}

if (strtolower($storagetaxable) == "yes")
    $storagetaxamount = $storagefee;

$totaltax = 0;
$labortax = 0;
$partstax = 0;
$sublettax = 0;
$totalro = 0;
$totaltaxableamount = 0;
$hstamount = 0; $gstamount = 0; $pstamount = 0; $qstamount = 0;

$labortax = ($totallabor * ($LaborTaxRate/100));

$nontaxlabor = 0;

// now check for labor items to non tax

if ($LaborTaxRate > 0){

    $stmt = "select coalesce(sum(linetotal),0) linetotal from labor where shopid = '$shopid' and roid = $roid and schedulelength = 'no' and deleted = 'no'";
    if ($query = $conn->prepare($stmt)){
        //$query->bind_param("si",$shopid,$roid);
        $query->execute();
        $query->bind_result($nontaxlabor);
        $query->fetch();
        $query->close();
    }
    $taxonnontaxlabor = round($nontaxlabor * ($LaborTaxRate/100),2);
    $labortax = $labortax - $taxonnontaxlabor;
}

if ($labortax < 0){
    $labortax = 0;
}


if(!is_numeric($companyzip) && !empty($canadiantax))
{
    if($taxpartsprice=='yes')
        $partstmt = "select linettlprice ttlp,cantaxes from parts where tax = 'yes' and shopid = '$shopid' and roid = $roid and deleted = 'no'";
    else
        $partstmt = "select linettlcost ttlp,cantaxes from parts where tax = 'yes' and shopid = '$shopid' and roid = $roid and deleted = 'no'";

    if ($pquery = $conn->prepare($partstmt))
    {
        $pquery->execute();
        $presult = $pquery->get_result();
        $pquery->store_result();
        while($prow = $presult->fetch_assoc())
        {
            if(!empty($prow['cantaxes']))
            {
                $intaxrate = 0;

                if($hst > 0 && stripos($prow['cantaxes'],'H')!==false) {$intaxrate += $hst;$hstamount += ($prow['ttlp'] * ($hst/100));}
                if($gst > 0 && stripos($prow['cantaxes'],'G')!==false) {$intaxrate += $gst;$gstamount += ($prow['ttlp'] * ($gst/100));}
                if($pst > 0 && stripos($prow['cantaxes'],'P')!==false) {$intaxrate += $pst;$pstamount += ($prow['ttlp'] * ($pst/100));}
                if($qst > 0 && stripos($prow['cantaxes'],'Q')!==false) {$intaxrate += $qst;$qstamount += ($prow['ttlp'] * ($qst/100));}

                $partstax += (round(round($prow['ttlp'],2) *  ($intaxrate/100),2));
            }
            else
            {
                $partstax += (round(round($prow['ttlp'],2) *  ($TaxRate/100),2));
                if(stripos($hstapplyon,'A')!==false || stripos($hstapplyon,'P')!==false) $hstamount += ($prow['ttlp'] * ($hst/100));
                if(stripos($gstapplyon,'A')!==false || stripos($gstapplyon,'P')!==false) $gstamount += ($prow['ttlp'] * ($gst/100));
                if(stripos($pstapplyon,'A')!==false || stripos($pstapplyon,'P')!==false) $pstamount += ($prow['ttlp'] * ($pst/100));
                if(stripos($qstapplyon,'A')!==false || stripos($qstapplyon,'P')!==false) $qstamount += ($prow['ttlp'] * ($qst/100));
            }

        }
        $taxpartsfetch = "none";
    }

    $totaltaxableamount = $userfee1taxamount + $userfee2taxamount + $userfee3taxamount + $hazwastetaxamount + $storagetaxamount;
}
else
{
    if($taxpartsprice=='yes')
        $partstmt = "select coalesce(sum(linettlprice),0) tprts from parts where tax = 'yes' and shopid = '$shopid' and roid = $roid and deleted = 'no'";
    else
        $partstmt = "select coalesce(sum(linettlcost),0) tprts from parts where tax = 'yes' and shopid = '$shopid' and roid = $roid and deleted = 'no'";
    if ($pquery = $conn->prepare($partstmt)){
        $pquery->execute();
        $pquery->store_result();
        $pquery->bind_result($taxableparts);
        $pquery->fetch();
        $taxpartsfetch = "none";
    }

    $totaltaxableamount = $taxableparts + $userfee1taxamount + $userfee2taxamount + $userfee3taxamount + $hazwastetaxamount + $storagetaxamount;

    //if(strtolower($discounttaxable)=='yes')
    //$totaltaxableamount = $totaltaxableamount - $partsdiscount - $labordiscount;

}

$stmt = "select coalesce(sum(feeamount),0) from rofees where shopid = '$shopid' and roid = $roid";
if ($query = $conn->prepare($stmt)){
    $query->execute();
    $query->bind_result($otherfees);
    $query->fetch();
    $query->close();
}

$partstax += round(round($totaltaxableamount,2) *  ($TaxRate/100),2);


$sublettax = sbpround(sbpround($taxablesublet) * ($SubletTaxRate/100));
if($Status == 'CLOSED')
    $totaltax = $SalesTax;
else
    $totaltax = round($labortax,2) + round($partstax,2) + round($sublettax,2);
$UserFee1 = round($UserFee1,2);$UserFee2 = round($UserFee2,2);$UserFee3 = round($UserFee3,2);$HazardousWaste = round($HazardousWaste,2);$storagefee = round($storagefee,2);
$totalfees = sbpround($UserFee1+$UserFee2+$UserFee3+$HazardousWaste+$storagefee+$otherfees,2);
$subtotal = sbpround($totallabor+$totalparts+$totalsublet+$totalfees,2);
$subtotalwofees = $subtotal - $totalfees;
$subtotalfordb = $subtotal - $totalfees;
$afterdiscount = sbpround($subtotal,2)-sbpround($DiscountAmt,2);
$totalpayments = sbpround($totalpayments,2);

$totalro = $subtotal + $totaltax - round($DiscountAmt,2);

$balancero = round($totalro - $totalpayments,2);

require(COMPONENTS_PRIVATE_PATH."/gp/gpcalc_dev.php");
require(COMPONENTS_PRIVATE_PATH."/pif/pifcalc.php");

if($Status != 'CLOSED' && strval($gp)!=strval($rogp))
{
    //notification
    $gpper=$rogp*100;
    $stmt = "SELECT s.customval,t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='23' and '$gpper'<s.customval";
    $query = $conn->prepare($stmt);
    $query->execute();
    $query->store_result();
    $numrows = $query->num_rows();
    if ($numrows > 0)
    {
        $query->bind_result($customval,$textcontent,$emailcontent,$popupcontent);
        $query->fetch();
        $emailcontent=str_replace("*|CUSTOMVAL|*",$customval,str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent)));
        $popupcontent=str_replace("*|CUSTOMVAL|*",$customval,str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent)));
        $textcontent=str_replace("*|CUSTOMVAL|*",$customval,str_replace("*|RO|*",$roid,$textcontent));
        $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'23',?,?,?)";
        if ($query = $conn->prepare($stmt))
        {
            $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
            $query->execute();
            $conn->commit();
            $query->close();
        }
    }

    $stmt = "SELECT s.customval,t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='26' and '$gpper'>s.customval";
    $query = $conn->prepare($stmt);
    $query->execute();
    $query->store_result();
    $numrows = $query->num_rows();
    if ($numrows > 0)
    {
        $query->bind_result($customval,$textcontent,$emailcontent,$popupcontent);
        $query->fetch();
        $emailcontent=str_replace("*|CUSTOMVAL|*",$customval,str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent)));
        $popupcontent=str_replace("*|CUSTOMVAL|*",$customval,str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent)));
        $textcontent=str_replace("*|CUSTOMVAL|*",$customval,str_replace("*|RO|*",$roid,$textcontent));
        $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'26',?,?,?)";
        if ($query = $conn->prepare($stmt))
        {
            $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
            $query->execute();
            $conn->commit();
            $query->close();
        }
    }

}

if($Status != 'CLOSED' && strval($oldtotalro)!=strval($totalro))
{
    //notification
    $stmt = "SELECT s.customval,t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='29' and '$totalro'>s.customval";
    $query = $conn->prepare($stmt);
    $query->execute();
    $query->store_result();
    $numrows = $query->num_rows();
    if ($numrows > 0)
    {
        $query->bind_result($customval,$textcontent,$emailcontent,$popupcontent);
        $query->fetch();
        $emailcontent=str_replace("*|CUSTOMVAL|*",$customval,str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent)));
        $popupcontent=str_replace("*|CUSTOMVAL|*",$customval,str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent)));
        $textcontent=str_replace("*|CUSTOMVAL|*",$customval,str_replace("*|RO|*",$roid,$textcontent));
        $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'29',?,?,?)";
        if ($query = $conn->prepare($stmt))
        {
            $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
            $query->execute();
            $conn->commit();
            $query->close();
        }
    }
}

if($Status != 'CLOSED')
{
    $roupdatestmt = "update repairorders set taxrate = $TaxRate, labortaxrate = $LaborTaxRate, sublettaxrate = $SubletTaxRate, gp = $rogp, pph = $pif, totallbrhrs = $totallaborhours,totallbr = $totallabor,totalprts = $totalparts,totalsublet = $totalsublet,"
        ."subtotal = $subtotalfordb,totalro = $totalro,totalfees = $totalfees,salestax = $totaltax,partscost = $totalpartscost,balance = $balancero, userfee1 = $UserFee1, userfee2 = $UserFee2, userfee3 = $UserFee3"
        ." where shopid = '$shopid' and roid = $roid";
    if ($query = $conn->prepare($roupdatestmt)){
        if ($query->execute()){
            $conn->commit();
            $roupdate = "none";
        }else{
            $roupdate = $conn->error;
        }

    }else{
        echo "Update RO Failed: (" . $conn->errno . ") " . $conn->error;
    }
}

if($Status != 'CLOSED' && !is_numeric($companyzip) && !empty($canadiantax)){

    if ($chargehst == "yes" && $hst > 0)
    {
        if(stripos($hstapplyon,'A')!==false || stripos($hstapplyon,'P')!==false) $hstamount += ($totaltaxableamount * ($hst/100));
        if(stripos($hstapplyon,'A')!==false || stripos($hstapplyon,'L')!==false) $hstamount += (($totallabor - $nontaxlabor) * ($hst/100));
        if(stripos($hstapplyon,'A')!==false || stripos($hstapplyon,'S')!==false) $hstamount += ($taxablesublet * ($hst/100));
    }
    if ($chargepst == "yes" && $pst > 0)
    {
        if(stripos($pstapplyon,'A')!==false || stripos($pstapplyon,'P')!==false) $pstamount += ($totaltaxableamount * ($pst/100));
        if(stripos($pstapplyon,'A')!==false || stripos($pstapplyon,'L')!==false) $pstamount += (($totallabor - $nontaxlabor) * ($pst/100));
        if(stripos($pstapplyon,'A')!==false || stripos($pstapplyon,'S')!==false) $pstamount += ($taxablesublet * ($pst/100));
    }
    if ($chargegst == "yes" && $gst > 0)
    {
        if(stripos($gstapplyon,'A')!==false || stripos($gstapplyon,'P')!==false) $gstamount += ($totaltaxableamount * ($gst/100));
        if(stripos($gstapplyon,'A')!==false || stripos($gstapplyon,'L')!==false) $gstamount += (($totallabor - $nontaxlabor) * ($gst/100));
        if(stripos($gstapplyon,'A')!==false || stripos($gstapplyon,'S')!==false) $gstamount += ($taxablesublet * ($gst/100));
    }
    if ($chargeqst == "yes" && $qst > 0)
    {
        if(stripos($qstapplyon,'A')!==false || stripos($qstapplyon,'P')!==false) $qstamount += ($totaltaxableamount * ($qst/100));
        if(stripos($qstapplyon,'A')!==false || stripos($qstapplyon,'L')!==false) $qstamount += (($totallabor - $nontaxlabor) * ($qst/100));
        if(stripos($qstapplyon,'A')!==false || stripos($qstapplyon,'S')!==false) $qstamount += ($taxablesublet * ($qst/100));
    }

    $hstamount = ($hstamount>0?sbpround($hstamount):'0');$gstamount = ($gstamount>0?sbpround($gstamount):'0');$pstamount = ($pstamount>0?sbpround($pstamount):'0');$qstamount = ($qstamount>0?sbpround($qstamount):'0');

    $castmt = "select id from canadiantaxcharged where shopid = ? and roid = ?";
    if ($caquery = $conn->prepare($castmt))
    {
        $caquery->bind_param("si",$shopid,$roid);
        $caquery->execute();
        $caquery->store_result();
        if($caquery->num_rows()>0)
        {
            $stmt = "update canadiantaxcharged set hstrate='$hst',hstamount='$hstamount',gstrate='$gst',gstamount='$gstamount',pstrate='$pst',pstamount='$pstamount',qstrate='$qst',qstamount='$qstamount' where shopid='$shopid' and roid='$roid'";
            if ($query = $conn->prepare($stmt))
            {
                $query->execute();
                $conn->commit();
            }
        }
        else
        {
            $stmt = "insert into canadiantaxcharged (hstrate,hstamount,gstrate,gstamount,pstrate,pstamount,qstrate,qstamount,shopid,roid) values ($hst,$hstamount,$gst,$gstamount,$pst,$pstamount,$qst,$qstamount,'$shopid',$roid)";
            if ($query = $conn->prepare($stmt))
            {
                $query->execute();
                $conn->commit();
            }
        }
    }
}

// Get Referral Source Name
$refname2 = '';
/*
$refnamestmt = "select source,origtech from repairorders where shopid = ? and roid = ? ";
if ($squery = $conn->prepare($refnamestmt)){
    $squery->bind_param("si",$shopid,$roid);
    $squery->execute();
    $squery->store_result();
    $squery->bind_result($refsource,$refname);
    $squery->fetch();
    $refnamefetch = "none";
}else{
    $refnamefetch = $conn->error;
}
*/

$refsource = $Source;
$refname = $OrigTech;

$refname = substr($refname,0,20);

$inspectionstarted = "no";

if(strtolower($requireinspectiontofinal)=='yes')
{
    $inspstmt = "select id from roinspection where shopid = ? and roid = ? UNION select id from autoserveinspections where shopid=? and roid=? UNION select id from dvi where shopid=? and roid=? limit 1";
    if ($insquery = $conn->prepare($inspstmt))
    {
        $insquery->bind_param("sisisi",$shopid,$roid,$shopid,$roid,$shopid,$roid);
        $insquery->execute();
        $insquery->store_result();
        if($insquery->num_rows()>0)$inspectionstarted="yes";
    }
}

$cfpphone = $cfpaltphone = '';

if($showcfp=='yes' && !empty($cfpid))
{
    $phones = array($CellPhone,$CustomerPhone,$spousecell,$spousework);
    foreach($phones as $phone)
    {
        if(!empty($phone))
        {
            if(empty($cfpphone))$cfpphone = $phone;
            elseif($phone!=$cfpphone && empty($cfpaltphone))$cfpaltphone = $phone;
        }
    }
}
$asid = "";
$stmt = "select asid from autoserveshop where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($asid);
    $query->fetch();
    $query->close();
}

$dvilite = 'yes';

if($newpackagetype == "silver" && strtotime($datestarted) > strtotime('2022-06-09'))
{
    $stmt = "select id from companyadds where shopid = '$shopid' and name = 'Dvi Lite'";
    if ($query = $conn->prepare($stmt))
    {
        $query->execute();
        $query->store_result();
        $numrows = $query->num_rows();
        if ($numrows < 1)$dvilite='no';
    }
}

if($_COOKIE['dvilite'] == 'no') $dvilite = "no";

if ($estguide == "yes"){
    $egbutton = "yes";
}else{
    $egbutton = "no";
}
if ($alldatausername == "motorfull"){
    $egbutton = "motorfull";
}elseif ($alldatausername == "motorest"){
    $egbutton = "motorest";
}else{
    $egbutton = "no";
}

if($haspph == "yes")
{
    $stmt = "SELECT count(DISTINCT complaintid) FROM labor where shopid = ? AND roid = ? and deleted='no' AND complaintid>0";

    if ($query = $conn->prepare($stmt))
    {
        $query->bind_param("si",$shopid,$roid);
        $query->execute();
        $query->bind_result($distinctcom);
        $query->fetch();
        $query->close();
    }

    $pphfees = ($distinctcom>0?round($totalfees/$distinctcom,2):0);
    $pphdisc = ($distinctcom>0?round(($partsdiscount+$labordiscount)/$distinctcom,2):0);

    $stmt = "select target from profitboost where shopid = ?";
    if ($query = $conn->prepare($stmt))
    {
        $query->bind_param("s",$shopid);
        $query->execute();
        $query->bind_result($target);
        $query->fetch();
        $query->close();
    }
}

$picscount=0;
$sstmt = "select picname from repairorderpics where shopid = ? and roid = ? and inspitemid=0";
if ($squery = $conn->prepare($sstmt)){
    $squery->bind_param("si",$shopid,$roid);
    $squery->execute();
    $squery->store_result();
    $picscount = $squery->num_rows();
}

$stmt = "select count(*) c from lyftrides where shopid = '$shopid' and roid = $roid";
if ($query = $conn->prepare($stmt)){
    $query->execute();
    $query->bind_result($lyftcount);
    $query->fetch();
    $query->close();
}

$rostatuses = array();
$stmt = "select colorcode,status,isdefault from rostatus where shopid = ? order by displayorder";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_array())
    {
      if($rs['isdefault']=='yes' && is_numeric(substr($rs['status'],0,1)))
      $displaystatus = substr($rs['status'],1);
      else
      $displaystatus = $rs['status'];

      $rostatuses[strtolower($rs['status'])] = array('color'=>$rs['colorcode'],'displaystatus'=>$displaystatus);
    }
    
}