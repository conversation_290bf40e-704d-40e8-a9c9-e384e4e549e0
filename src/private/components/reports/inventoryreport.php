<?php
$title = 'Inventory Report/Analysis';  // Report Title Goes Here
// Use this for Gen Pop Reports
require "includes/header_reports.php";
require(CONN);

$sd = date('m/d/Y');
//require("../php/functions.php");

// Global Variables
$shopid = $_COOKIE['shopid'];
$category = $_GET['category'];
$sortby = $_GET['sortby'];
$salesdata = $_GET['salesdata'];
// Page Variables
//$subtitle = 'my subtitle';  // Report SubTitle Goes Here - Hide if not needed
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyname,$conamereports);
    $query->fetch();
    $query->close();
}

if($conamereports == 'yes'){
    $title .= " - $companyname";
}
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport_multiple.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>

<title><?= $title;?></title>  <!-- Meta title for page. Change in variable above-->

<!DOCTYPE html>
<html>
<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>

<style>


.ltblue-header {
	background-color:#70b9eb;
	color:white;
	font-weight:bold;
	width:750px;
}
tbody {
    height:750px;
    overflow:auto;
}

thead, tbody tr {
    display:table;
    width:100%;
    table-layout:fixed;
}

.auto-style1 {
	background-color: #5c90d2;
	color:white;
	font-weight:bold
}

tbody{
	display:inline;
}

.report_table2 {
	page-break-before:avoid !important;
}


@media print{
	tbody{
		display:inline;
}
	tr{
	font-size:7.5px !important;
}

}

</style>

<?php

// Use this for Gen Pop Reports
require "includes/report_buttons.php";
?>
<table class=report_table>

<?php
if($category == "all")
$stmt = "select distinct partcategory as `category` from partsinventory where shopid = '$shopid' order by Category";
else
$stmt = "select distinct partcategory as `category` from partsinventory where partcategory = '$category' and shopid = '$shopid' order by Category";

$ttlcost = 0;

$alldata = array();

if($query = $conn->prepare($stmt))
{
 $query->execute();
 $result = $query->get_result();
 while($rs = $result->fetch_array())
 {
     $ttlcat = 0;
 	$pstmt = "select * from partsinventory where shopid = ? and PartCategory = ? order by $sortby";
 	if($pquery = $conn->prepare($pstmt))
 	{
		$pquery->bind_param("ss",$shopid,$rs['category']);
		$pquery->execute();
		$presult = $pquery->get_result();
	}

	if($presult->num_rows > 0)
	{
    $alldata[]=array("Category: ".$rs['category']);
	$alldata[]=array('TABLEHEAD','Part #','Bin','Description','Unit Cost','Total Cost','Alloc','OnHand','Reorder','Max','90_Days','12_Mos','Over');

 	?>
 	<tr>
    <td style="width:750px" class="ltblue-header" >Category: <?= $rs["category"]?></td>
    </tr>
	<tr class="table_header table_head">
	 <td class="auto-style1" width="12%">Part #</td>
	 <td class="auto-style1" width="8%">Bin</td>
	 <td class="auto-style1" width="30%">Description</td>
	 <td class="auto-style1" width="9%" style="text-align: right;">Unit Cost</td>
	 <td class="auto-style1" width="9%" style="text-align: right;" style="text-align: right;">Total Cost</td>
	 <td class="auto-style1" width="6%" style="text-align: right;">Alloc</td>
	 <td class="auto-style1" width="6%" style="text-align: right;">OnHand</td>
	 <td class="auto-style1" width="6%" style="text-align: right;">Reorder</td>
	 <td class="auto-style1" width="6%" style="text-align: right;" style="text-align: right;">Max</td>
	 <td class="auto-style1" width="6%" style="text-align: right;">90_Days</td>
	 <td class="auto-style1" width="6%" style="text-align: right;">12_Mos</td>
	 <td class="auto-style1" width="6%" style="text-align: right;">Over</td>
	</tr>
 	<?php
 	while($prs = $presult->fetch_array())
    {
    	$astmt = "SELECT COALESCE(SUM(p.quantity),0) FROM parts p, repairorders r WHERE p.shopid=r.shopid AND p.roid=r.roid AND r.shopid=? AND r.status != 'CLOSED' and r.ROType != 'No Approval' AND p.partnumber=?";
    	if ($aquery = $conn->prepare($astmt))
    	{
	     $aquery->bind_param("ss",$shopid,$prs['PartNumber']);
	     $aquery->execute();
	     $aquery->bind_result($allocated);
         $aquery->fetch();
	     $aquery->close();
        }

        $ninetydays = $twelvemos = '';

        if($salesdata=='yes')
        {
          $date = date('Y-m-d');
          $day90 = date('Y-m-d',strtotime('-90 days'));
          $mth12 = date('Y-m-d',strtotime('-12 months'));

          $astmt = "select count(*) from parts where deleted = 'no' and shopid = ? and `date` <= '$date' and `date` >= '$day90' and PartNumber = ?";
    	  if ($aquery = $conn->prepare($astmt))
    	  {
	       $aquery->bind_param("ss",$shopid,$prs['PartNumber']);
	       $aquery->execute();
	       $aquery->bind_result($ninetydays);
           $aquery->fetch();
	       $aquery->close();
          }

          $astmt = "select count(*) from parts where deleted = 'no' and shopid = ? and `date` <= '$date' and `date` >= '$mth12' and PartNumber = ?";
    	  if ($aquery = $conn->prepare($astmt))
    	  {
	       $aquery->bind_param("ss",$shopid,$prs['PartNumber']);
	       $aquery->execute();
	       $aquery->bind_result($twelvemos);
           $aquery->fetch();
	       $aquery->close();
          }

        }

        if($prs['NetOnHand'] > $prs['MaxOnHand'])
        $over = $prs['NetOnHand'] - $prs['MaxOnHand'];
        else
        $over = '';
        $cost = $prs['PartCost']*($prs['NetOnHand']+$allocated);
        ?>
    	<tr class="table_data">
    	 <td width="12%"><?= $prs['PartNumber'] ?></td>
    	 <td width="8%"><?= strtoupper($prs['Bin']) ?></td>
    	 <td width="30%"><?= strtoupper($prs['PartDesc']) ?></td>
    	 <td width="9%" style="text-align: right;"><?= asDollars($prs['PartCost'])?></td>
    	 <td width="9%" style="text-align: right;"><?= asDollars($cost)?></td>
    	 <td width="6%" style="text-align: right;"><?= ($salesdata=='yes'?strtoupper($allocated):'')?></td>
    	 <td width="6%" style="text-align: right;"><?= $prs['NetOnHand']?></td>
    	 <td width="6%" style="text-align: right;"><?= $prs['ReOrderLevel']?></td>
    	 <td width="6%" style="text-align: right;"><?= $prs['MaxOnHand']?></td>
    	 <td width="6%" style="text-align: right;"><?= $ninetydays ?></td>
    	 <td width="6%" style="text-align: right;"><?= $twelvemos ?></td>
    	 <td width="6%" style="text-align: right;"><?= $over?></td>
    	</tr>
    	<?php

    	$alldata[] = array($prs['PartNumber'],strtoupper($prs['Bin']),strtoupper($prs['PartDesc']),asDollars($prs['PartCost']),asDollars($prs['PartCost']*($prs['NetOnHand']+$allocated)),($salesdata=='yes'?$allocated:''),$prs['NetOnHand'],$prs['ReOrderLevel'],$prs['MaxOnHand'],$ninetydays,$twelvemos,$over);

        $ttlcost += $cost;
        $ttlcat += $cost;

    }

    }
    ?>
     <tr class="table_total">
         <td width="12%">Subtotal</td>
         <td width="8%"><?php //echo $rs['category'] ?></td>
         <td width="30%"></td>
         <td width="9%" style="text-align: right;"></td>
         <td width="9%" style="text-align: right;"><?= asDollars($ttlcat)?></td>
         <td width="6%" style="text-align: right;"></td>
         <td width="6%" style="text-align: right;"></td>
         <td width="6%" style="text-align: right;"></td>
         <td width="6%" style="text-align: right;"></td>
         <td width="6%" style="text-align: right;"></td>
         <td width="6%" style="text-align: right;"></td>
         <td width="6%" style="text-align: right;"></td>
     </tr>
     <tr style="background-color: transparent">
         <td colspan="12">&nbsp;</td>
     </tr>
         <?php
     $alldata[]=array('Subtotal',$rs['category'],'','',asDollars($ttlcat),'','','','','','');
     $alldata[]=array('','','','','','','','','','','');
 }
 ?>
 <tr class="table_data">
   <td width="12%"><b>Total</b></td>
   <td width="8%"></td>
   <td width="30%"></td>
   <td width="9%"></td>
   <td width="9%" style="text-align: right;"><b><?= asDollars($ttlcost)?></b></td>
   <td width="6%"></td>
   <td width="6%"></td>
   <td width="6%"></td>
   <td width="6%"></td>
   <td width="6%"></td>
   <td width="6%"></td>
   <td width="6%"></td>
 </tr>
 <?php
 $alldata[]=array('TOTALS','','','',asDollars($ttlcost),'','','','','','','');
}
?>

</table>

<?php

// Use this for Gen Pop Reports
require "includes/footer_reports.php";
require "includes/report_form_todaydate.php";

// Use this for Custom Reports
//include("../../../php/includes/reports/footer_reports.php");
//include("../../../php/includes/reports/report_form.php");
?>


</body>
</html>
<?php
if(isset($conn)){
	mysqli_close($conn);
}
?>
