<?php
require CONN;
$shopid = $_COOKIE['shopid'];

$target_path = "//fs.shopboss.aws/share/upload/".$shopid."/";
$target_dir = realpath($target_path);

if (!$target_dir){
	mkdir("//fs.shopboss.aws/share/upload/".$shopid);
}

$target_file = $target_path . basename($_FILES['file']['name']);
if(move_uploaded_file($_FILES['file']['tmp_name'], $target_file)) {
	
	$fname = basename($_FILES['file']['name']);
	$stmt = "update company set logo = '$fname' where shopid = '$shopid'";

	if ($query = $conn->prepare($stmt)){
	    if ($query->execute()){
	    	$conn->commit();
	    	echo "success";
	    }else{
	    	echo $conn->errno;
	    }
	    
	    $query->close();
	    
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

}else{

	echo "Error uploading";

}
/*
// Count # of uploaded files in array
$total = count($_FILES['file']['name']);
echo "file:".$_FILES['file']['name'];
//echo "total files:".$total;
$tmpFilePath = $_FILES['file']['tmp_name'][$i];
$newFilePath = "../../../sbp/upload/".$shopid."/" . $_FILES['file']['name'][$i];


	$fname = $shopid."_".$_FILES['file']['name'][$i];
	echo $fname."\r\n";
	//$resizeFilePath = "../../sbp/upload/".$shopid."/".$roid."/" . $fname;
	//smart_resize_image($newFilePath , null, 800, 800 , true , $resizeFilePath , true , false ,100 );
	
	$stmt = "update company set logo = '$fname' where shopid = '$shopid'";
	echo $stmt;
	
	if ($query = $conn->prepare($stmt)){
	    if ($query->execute()){
	    	$conn->commit();
	    	echo "success";
	    }else{
	    	echo $conn->errno;
	    }
	    
	    $query->close();
	    
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

}
*/
?>
<?php if(isset($conn)){mysqli_close($conn);} ?>