<?php
require CONN;

$shopid = $_POST['shopid'];
$t = $_POST['t'];

if ($t == "checkbox"){

	$id = $_POST['id'];
	$v = $_POST['val'];

	$stmt = "update company set ".$id." = '".$v."' where shopid = ?";
	echo $stmt;

	if ($query = $conn->prepare($stmt)){
		$query->bind_param("s",$shopid);
	    if ($query->execute()){
	    	echo $conn->errno;
	    }
	    $conn->commit();
	    $query->close();

		echo "success";

	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}


}

if ($t == "other"){

	$UserFee1 = $_POST['UserFee1'];
	$UserFee2 = $_POST['UserFee2'];
	$UserFee3 = $_POST['UserFee3'];
	$UserFee1amount = str_replace(',', '',$_POST['UserFee1amount']);
	$UserFee2amount = str_replace(',', '',$_POST['UserFee2amount']);
	$UserFee3amount = str_replace(',', '',$_POST['UserFee3amount']);
	$userfee1type = $_POST['userfee1type'];
	$userfee2type = $_POST['userfee2type'];
	$userfee3type = $_POST['userfee3type'];
	$userfee1taxable = $_POST['userfee1taxable'];
	$userfee2taxable = $_POST['userfee2taxable'];
	$userfee3taxable = $_POST['userfee3taxable'];
	$userfee1max = str_replace(',', '',$_POST['userfee1max']);
	$userfee2max = str_replace(',', '',$_POST['userfee2max']);
	$userfee3max = str_replace(',', '',$_POST['userfee3max']);
	$userfee1applyon = $_POST['userfee1applyon'];
	$userfee2applyon = $_POST['userfee2applyon'];
	$userfee3applyon = $_POST['userfee3applyon'];
	$hourlyrate1 = $_POST['hourlyrate'];
	$hourlyrate2 = $_POST['hourlyrate2'];
	$hourlyrate3 = $_POST['hourlyrate3'];
	$hourlyrate4 = $_POST['hourlyrate4'];
	$hourlyrate5 = $_POST['hourlyrate5'];
	$hourlyrate6 = $_POST['hourlyrate6'];
	$hourlyrate7 = $_POST['hourlyrate7'];
	$hourlyrate8 = $_POST['hourlyrate8'];
	$hourlyrate9 = $_POST['hourlyrate9'];
	$hourlyrate10 = $_POST['hourlyrate10'];
	$hourlyrate1label = $_POST['hourlyrate1label'];
	$hourlyrate2label = $_POST['hourlyrate2label'];
	$hourlyrate3label = $_POST['hourlyrate3label'];
	$hourlyrate4label = $_POST['hourlyrate4label'];
	$hourlyrate5label = $_POST['hourlyrate5label'];
	$hourlyrate6label = $_POST['hourlyrate6label'];
	$hourlyrate7label = $_POST['hourlyrate7label'];
	$hourlyrate8label = $_POST['hourlyrate8label'];
	$hourlyrate9label = $_POST['hourlyrate9label'];
	$hourlyrate10label = $_POST['hourlyrate10label'];
	$defaultrochild =  $_POST['defaultrochild'];

	$stmt = "update company set hourlyrate = ?,hourlyrate2 = ?,hourlyrate3 = ?,hourlyrate4 = ?,hourlyrate5 = ?,hourlyrate6 = ?,hourlyrate7 = ?,hourlyrate8 = ?,hourlyrate9 = ?,hourlyrate10 = ?,hourlyrate1label = ?,hourlyrate2label = ?,hourlyrate3label = ?,hourlyrate4label = ?,hourlyrate5label = ?,hourlyrate6label = ?,hourlyrate7label = ?,hourlyrate8label = ?,hourlyrate9label = ?,hourlyrate10label = ?,UserFee1 = ?,UserFee2 = ?,UserFee3 = ?,UserFee1amount = ?,UserFee2amount = ?,UserFee3amount = ?,userfee1type = ?,userfee2type = ?,userfee3type = ?,userfee1taxable = ?,userfee2taxable = ?,userfee3taxable = ?,userfee1max = ?,userfee2max = ?,userfee3max = ?,userfee1applyon = ?,userfee2applyon = ?,userfee3applyon = ?,defaultrochild = ? where shopid = ?";
	//printf (str_replace('?',"'%s'",$stmt),$UserFee1,$UserFee2,$UserFee3,$UserFee1amount,$UserFee2amount,$UserFee3amount,$userfee1type,$userfee2type,$userfee3type,$userfee1taxable,$userfee2taxable,$userfee3taxable,$userfee1max,$userfee2max,$userfee3max,$shopid);

	if ($query = $conn->prepare($stmt)){
		$query->bind_param("ddddddddddsssssssssssssdddssssssdddsssss",$hourlyrate1,$hourlyrate2,$hourlyrate3,$hourlyrate4,$hourlyrate5,$hourlyrate6,$hourlyrate7,$hourlyrate8,$hourlyrate9,$hourlyrate10,$hourlyrate1label,$hourlyrate2label,$hourlyrate3label,$hourlyrate4label,$hourlyrate5label,$hourlyrate6label,$hourlyrate7label,$hourlyrate8label,$hourlyrate9label,$hourlyrate10label,$UserFee1,$UserFee2,$UserFee3,$UserFee1amount,$UserFee2amount,$UserFee3amount,$userfee1type,$userfee2type,$userfee3type,$userfee1taxable,$userfee2taxable,$userfee3taxable,$userfee1max,$userfee2max,$userfee3max,$userfee1applyon,$userfee2applyon,$userfee3applyon,$defaultrochild,$shopid);
	    if ($query->execute()){
	    	$conn->commit();

	   //Notification
       $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='137'";
	   $query = $conn->prepare($stmt);
	   $query->execute();
	   $query->store_result();
	   $numrows = $query->num_rows();
	   if ($numrows > 0)
	   {
	   $query->bind_result($textcontent,$emailcontent,$popupcontent);
	   $query->fetch();
	   $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'137',?,?,?)";
	   if ($query = $conn->prepare($stmt))
	   {
	   $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
	   $query->execute();
	   $conn->commit();
	   $query->close();
	   }
      }

	    	echo "success";
	    }else{
	    	echo $conn->errno;
	    }

	    $query->close();

	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

}



mysqli_close($conn);

?>
