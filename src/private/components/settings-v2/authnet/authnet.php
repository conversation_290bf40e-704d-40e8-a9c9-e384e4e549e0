<?php

require CONN;

$mid = $_POST['mid'];
$ck = $_POST['ck'];
$tk = $_POST['tk'];
$shopid = $_POST['shopid'];

$stmt = "update company set merchantid = ?, authnetclientkey = ?, merchantpassword = ? where shopid = ?";
if ($query = $conn->prepare($stmt)){

	$query->bind_param("ssss",$mid,$ck,$tk,$shopid);
	$query->execute();
	$conn->commit();
	$query->close();

}

if (strlen($mid) > 5 && strlen($ck) > 10){
	
	$stmt = "update company set merchantaccount = 'authorize.net' where shopid = ?";
	if ($query = $conn->prepare($stmt)){
	
		$query->bind_param("s",$shopid);
		$query->execute();
		$conn->commit();
		$query->close();
		
		echo "success";
	
	}
	
}else{

	$stmt = "update company set merchantaccount = 'no' where shopid = ?";
	if ($query = $conn->prepare($stmt)){
	
		$query->bind_param("s",$shopid);
		$query->execute();
		$conn->commit();
		$query->close();
		
		echo "success";
	
	}


}






?>
