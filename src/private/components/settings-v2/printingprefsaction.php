<?php

require CONN;

$shopid = $_POST['shopid'];
$t = $_POST['t'];

if ($t == "checkbox") {
    $id = $_POST['id'];
    $v = $_POST['val'];

    $stmt = "update company set " . $id . " = '" . $v . "' where shopid = ?";
    //echo $stmt;

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        if ($query->execute()) {
            echo $conn->errno;
        }
        $conn->commit();
        $query->close();

        echo "success";

        //Notification
        $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='131'";
        $query = $conn->prepare($stmt);
        $query->execute();
        $query->store_result();
        $numrows = $query->num_rows();
        if ($numrows > 0) {
            $query->bind_result($textcontent, $emailcontent, $popupcontent);
            $query->fetch();
            $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'131',?,?,?)";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param('ssss', $shopid, $popupcontent, $textcontent, $emailcontent);
                $query->execute();
                $conn->commit();
                $query->close();
            }
        }

    } else {
        //check settings table

        $stmt = "update settings set " . $id . " = '" . $v . "' where shopid = ?";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("s", $shopid);
            if ($query->execute()) {
                echo $conn->errno;
            }
            $conn->commit();
            $query->close();

            echo "success";

            //Notification
            $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='131'";
            $query = $conn->prepare($stmt);
            $query->execute();
            $query->store_result();
            $numrows = $query->num_rows();
            if ($numrows > 0) {
                $query->bind_result($textcontent, $emailcontent, $popupcontent);
                $query->fetch();
                $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'131',?,?,?)";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param('ssss', $shopid, $popupcontent, $textcontent, $emailcontent);
                    $query->execute();
                    $conn->commit();
                    $query->close();
                }
            }

        } else {
            echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }
    }


}

if ($t == "miles") {

    $mil = $_POST['mil'];
    $mol = $_POST['mol'];
    $it = $_POST['it'];
    $et = $_POST['et'];
    $ql = $_POST['ql'];

    $stmt = "update company set milesinlabel = ?, milesoutlabel = ?, invoicetitle = ?, estimatetitle = ?, quotelabel = ? where shopid = ?";
    //printf (str_replace('?',"'%s'",$stmt),$mil,$mol,$it,$et,$shopid );

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("ssssss", $mil, $mol, $it, $et, $ql, $shopid);
        if ($query->execute()) {
            $conn->commit();
            echo "success";
        } else {
            echo $conn->errno;
        }

        $query->close();

        //Notification
        $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='131'";
        $query = $conn->prepare($stmt);
        $query->execute();
        $query->store_result();
        $numrows = $query->num_rows();
        if ($numrows > 0) {
            $query->bind_result($textcontent, $emailcontent, $popupcontent);
            $query->fetch();
            $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'131',?,?,?)";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param('ssss', $shopid, $popupcontent, $textcontent, $emailcontent);
                $query->execute();
                $conn->commit();
                $query->close();
            }
        }

    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

}


mysqli_close($conn);

?>