<?php
require CONN;

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $shopid = $_POST["shopid"];
    $theme = $_POST["theme"];
    
    $stmt = "UPDATE settings SET theme = ? WHERE shopid = ?";
    
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("ss", $theme, $shopid);
        if ($query->execute()) {
            $conn->commit();
            echo "Theme updated successfully to " . $theme;
            
            // Set the cookie with the theme
            setcookie("theme", $theme, time() + (86400 * 30), "/"); // <PERSON>ie will expire in 30 days
        } else {
            echo "Error executing query: " . $query->error;
        }
        $query->close();
    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }
}
mysqli_close($conn);
?>