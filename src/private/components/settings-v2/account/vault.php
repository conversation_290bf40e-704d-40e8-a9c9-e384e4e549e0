<?php
// vault.php
function guidv4($data){

    assert(strlen($data) == 16);

    $data[6] = chr(ord($data[6]) & 0x0f | 0x40); // set version to 0100
    $data[8] = chr(ord($data[8]) & 0x3f | 0x80); // set bits 6-7 to 10

    return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
}

$apikey360 = "7hzd42ZYhvD8HXMPGSR2Tvhb3T53EFEW";   // testing key
//$apikey360 = "2wN84PMY6ATF7J7w49ZB5hsYyMY59Q8Y";     // production key

require(CONNWOSHOPID);

$t = $_POST['t'];
$shopid = filter_var($_POST['shopid'],FILTER_SANITIZE_STRING);
$fn = strtoupper(filter_var($_POST['fn'],FILTER_SANITIZE_STRING));
$ln = strtoupper(filter_var($_POST['ln'],FILTER_SANITIZE_STRING));
$zip = filter_var($_POST['zp'],FILTER_SANITIZE_STRING);
$addr = strtoupper(filter_var($_POST['st'],FILTER_SANITIZE_STRING));
$payment_token = filter_var($_POST['payment_token'],FILTER_SANITIZE_STRING);

/*foreach ($_POST as $k => $v){
	echo $k."|".$v."\r\n";
}*/

if ($t == "addnew"){

	$uuid = guidv4(openssl_random_pseudo_bytes(16));
	
	$postdata = "payment_token=$payment_token&zip=$zip&last_name=$ln&first_name=$fn&customer_vault=add_customer&customer_vault_id=$uuid&security_key=$apikey360";
	//echo $postdata."<BR>";
	
	$url = "https://secure.networkmerchants.com/api/transact.php";
	$ch = curl_init();
	
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
	curl_setopt($ch, CURLOPT_POST, 1);
	curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);	
	
	$result = curl_exec($ch);
	//echo "result:".$result;
	
	if (curl_errno($ch)) {
	    $error_msg = curl_error($ch);
	    echo $error_msg."<BR>";
	}
	
	curl_close ($ch);
	
	parse_str($result,$ar);

	if ($ar['responsetext'] == "Customer Added"){
	
		$plan = strtolower($_POST['plan']);
		if ($plan == "silver"){ $flatprice = 109.00; }
		if ($plan == "gold"){ $flatprice = 175.00; }
		if ($plan == "platinum"){ $flatprice = 285.00; }
		$activatedate = date("Y-m-d");
	
		// add the id to the company table
		$sstmt = "update company set dateofacceptance = ?, cc = ?, package = 'paid', status = 'active', newpackagetype = ?, flatprice = ? where shopid = ?";
		//$sstmt = "update company set dateofacceptance = '$activatedate', cc = '$uuid', package = 'paid', status = 'active', newpackagetype = '$plan', flatprice = $flatprice where shopid = '$shopid'\r\n";
		if ($squery = $conn->prepare($sstmt)){
			$squery->bind_param("sssds",$activatedate,$uuid,$plan,$flatprice,$shopid);
			$squery->execute();
			$conn->commit();
			$squery->close();
		}
		echo "success";
	}else{
		echo "error";
	}


}elseif ($t == "update"){

	$uuid = filter_var($_POST['uuid'],FILTER_SANITIZE_STRING);
	
	$postdata = "payment_token=$payment_token&zip=$zip&last_name=$ln&first_name=$fn&customer_vault=update_customer&customer_vault_id=$uuid&security_key=$apikey360";
	//echo $postdata."<BR>";
	
	$url = "https://secure.networkmerchants.com/api/transact.php";
	$ch = curl_init();
	
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
	curl_setopt($ch, CURLOPT_POST, 1);
	curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);	
	
	$result = curl_exec($ch);
	//echo "result:".$result;
	
	if (curl_errno($ch)) {
	    $error_msg = curl_error($ch);
	    echo $error_msg."<BR>";
	}
	
	curl_close ($ch);
	
	parse_str($result,$ar);

	if ($ar['responsetext'] == "Customer Update Successful"){
		
		// if there is an amount variable we need to charge the customer for the amount
		if (isset($_POST['amt'])){
			//echo "amount entered";
			if ($_POST['amt'] > 0){
				
				$amt = $_POST['amt'];
				// charge the customer
				$postdata = "customer_vault_id=$uuid&type=sale&security_key=$apikey360&zip=$zip&amount=$amt&first_name=$fn&last_name=$ln";
				
				$url = "https://secure.networkmerchants.com/api/transact.php";
				$ch = curl_init();
				
				curl_setopt($ch, CURLOPT_URL, $url);
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
				curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
				curl_setopt($ch, CURLOPT_POST, 1);
				curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
				curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);	
				
				$result = curl_exec($ch);
				//echo "result:".$result;
				curl_close ($ch);
				
				parse_str($result,$ar);
				$ptype = $ar['cc_type'];
				$responsetext = $ar['responsetext'];
				$authcode = $ar['authcode'];
				$transactionid = $ar['transactionid'];
				
				if (strtoupper($responsetext) == "APPROVED" || strtoupper($responsetext) == "SUCCESS"){
				
					//post to the customer account and create the invoice
					$stmt = "insert into payments (paymentdate,shopid,amount,approvalcode,approvaldescription,tracenumber) values ('"
					. date("Y-m-d") . "', '" . $shopid . "', " . $amt . ", '" . $authcode
					. "', '" . $responsetext . "', '" . $transactionid . "')";
					if ($query = $conn->prepare($stmt)){
						$query->execute();
						$conn->commit();
						$query->close();
					}
					
					$stmt = "update company set status = 'active' where shopid = ?";
					if ($query = $conn->prepare($stmt)){
						$query->bind_param("s",$shopid);
						$query->execute();
						$conn->commit();
						$query->close();
					}
					
					echo "success";
					
				}else{
				
					echo $responsetext;
				
				}

		
				
			}
		}else{
			echo "success";
		}
		
		
		
	}else{
		echo "error";
	}



}


mysqli_close($conn);
