<?php

require CONN;
require(PRIVATE_PATH."/integrations/mandrill/src/Mandrill.php");

function getFlatPrice($package)
{
	if ($package == "silver"){
		return 109.00;
	}
	elseif ($package == "gold"){
		return 199.00;
	}
	elseif ($package == "platinum"){
		return 299.00;
	}
  elseif ($package == "premier"){
		return 399.00;
	}

	else return '';
}

if ($_GET['typ'] == "account1"){
	$shopid = $_GET['shopid'];
	$package = $_GET['subscription'];
	$oriplan = $_GET['hiddenplan'];
	$fromtrial = $_GET['fromtrial']??'';

	$flatprice = getFlatPrice(strtolower($package));

		$stmt = 'update company set newpackagetype = ?, flatprice = ? where shopid = ?';
		if ($query = $conn->prepare($stmt))
		{
			$query->bind_param("sss",$package,$flatprice,$shopid);
		  $query->execute();
		  $conn->commit();

		  if($package=='platinum' || $package=='premier')
		 {
		    $stmt = "delete from companyadds where shopid=? and name='Boss Board'";
       	if ($query = $conn->prepare($stmt))
	    {
	      $query->bind_param("s",$shopid);
	      $query->execute();
	      $conn->commit();
	    }
	   }

	    if($package!=$oriplan)
	    {
	    	$stmt = "insert into package_change_log (sbemp,currentpackage,shopid,oldpackage) values (?,?,?,?)";
	        if ($query = $conn->prepare($stmt))
	        {
	         $query->bind_param('ssss',$_COOKIE['username'],$package,$shopid,$oriplan);
	         $query->execute();
	         $conn->commit();
	         $query->close();
	        }

	        if($fromtrial!='yes')
	        {

		        $oldpackvalue = getFlatPrice(strtolower($oriplan));
		        $newpackvalue = getFlatPrice(strtolower($package));
		        $type='';

		        if($newpackvalue>$oldpackvalue)$type = 'upsell';elseif($newpackvalue<$oldpackvalue) $type = 'downgrade';

		        $tibcoid = "23655";

		        $data = array('shop_boss_shop_id'=>$shopid,'previous_package'=>$oriplan,'current_package'=>$package,'upsell_downgrade'=>$type);
		        $jsonEncodedData = json_encode($data);
		        $curl = curl_init();
		        $opts = array(
			    CURLOPT_URL             => 'https://endpoint.scribesoft.com/v1/orgs/47078/requests/'.$tibcoid.'?accesstoken=03fdbdf1-0c25-4fc8-84fb-472f7a90e4d6', //TIBCO
			    CURLOPT_RETURNTRANSFER  => true,
			    CURLOPT_CUSTOMREQUEST   => 'POST',
			    CURLOPT_POST            => 1,
			    CURLOPT_POSTFIELDS      => $jsonEncodedData,
			    CURLOPT_HTTPHEADER  => array('Content-Type: application/json','Content-Length: ' . strlen($jsonEncodedData))
		        );
		       curl_setopt_array($curl, $opts);
		       $result = curl_exec($curl);
		       curl_close($curl);
		    }
	    }

		    	echo "success";
		  }

	$dts = localTimeStamp($shopid);
	$temppackage = ucwords(strtolower($package));
	$usr = $_COOKIE['usr'];
	$cat = "Account Update";
	$ev = "Your Shop Boss Account was update to $temppackage";
	$stmt = "insert into `audit` (shopid,`category`,`event`,`useraccount`,`eventdatetime`) values ('$shopid','$cat','$ev','$usr','$dts')";
	if ($query = $conn->prepare($stmt)){
		if ($query->execute()){
			$conn->commit();
		}else{
			echo $conn->errno;
		}
	}else{
		echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

	$slackdata = "Shop ID: $shopid\nShop Name: ".strtoupper($_COOKIE['shopname'])."\nPackage: ".strtoupper($package)."\nDate Changed: ".date('m/d/Y');

    $data = array('text'=>$slackdata);
    $jsonEncodedData = json_encode($data);
    $curl = curl_init();
    $opts = array(
        CURLOPT_URL             => '*******************************************************************************',
        CURLOPT_RETURNTRANSFER  => true,
        CURLOPT_CUSTOMREQUEST   => 'POST',
        CURLOPT_POST            => 1,
        CURLOPT_POSTFIELDS      => $jsonEncodedData,
        CURLOPT_HTTPHEADER  => array('Content-Type: application/json','Content-Length: ' . strlen($jsonEncodedData))
    );
    curl_setopt_array($curl, $opts);
    $result = curl_exec($curl);
    curl_close($curl);


}

elseif ($_POST['typ'] == "bluesnapsave"){
	
	$username="API_16299867132592130162459";
    $password="Toby1dog";
    $bzip = $_POST['bzip'];
	$baddress = $_POST['baddress'];
	$bfirst = $_POST['bfirst'];
	$blast = $_POST['blast'];
	$token = $_POST['token'];
	$last4 = $_POST['last4'];
	$exparr = explode('/', $_POST['exp']);
	$exp = trim($exparr[0]).'/'.substr($exparr[1],-2);
	$cardtype = $_POST['cardtype'];
	$bluesnapid = $_POST['bluesnapid'];
	$doa = $_POST['doa'];
	$shopid = $_COOKIE['shopid'];
	$package = strtoupper($_POST['subscription']);

	if(!empty($bluesnapid))
	{
		$url = "https://ws.bluesnap.com/services/2/vaulted-shoppers/".$bluesnapid;
		$stmt = 'select cc from company where shopid = ?';
	  if ($query = $conn->prepare($stmt))
	  {
	    $query->bind_param("s",$shopid);
	    $query->execute();
	    $query->bind_result($cc);
	    $query->fetch();
	    $query->close();
	  }
	  $ccarr = explode(';',$cc);
	  $last4old = $ccarr[0];
	  $cardtypeold = $ccarr[1]??'';
		$data = array
   (
   "firstName" => $bfirst,
   "lastName" => $blast,
   "paymentSources" => array("creditCardInfo"=>array(0 => array("creditCard" => array("cardType" => $cardtypeold,"cardLastFourDigits" => $last4old),"status"=>"D")))
   );

		$jsonEncodedData = json_encode($data);
		$curl = curl_init();
		$opts = array(
		    CURLOPT_URL             => $url,
		    CURLOPT_RETURNTRANSFER  => true,
		    CURLOPT_CUSTOMREQUEST   => 'PUT',
		    CURLOPT_POST            => 1,
		    CURLOPT_POSTFIELDS      => $jsonEncodedData,
		    CURLOPT_SSL_VERIFYHOST  => 0,
		    CURLOPT_SSL_VERIFYPEER  => 0,
		    CURLOPT_HTTPHEADER  => array('Accept: application/json','Content-Type: application/json','Authorization: Basic '. base64_encode("$username:$password"),'Content-Length: ' . strlen($jsonEncodedData))
		);
		curl_setopt_array($curl, $opts);
		$res=curl_exec($curl);
	}
	else
	$url = "https://ws.bluesnap.com/services/2/vaulted-shoppers";

	$data = array
 (
   "firstName" => $bfirst,
   "lastName" => $blast,
   "zip" => $bzip,
   "merchantShopperId" => $shopid,
   "companyName" => strtoupper($_COOKIE['shopname']),
   "paymentSources" => array("creditCardInfo"=>[array("pfToken" => $token)])
 );

$jsonEncodedData = json_encode($data);
$curl = curl_init();
$opts = array(
    CURLOPT_URL             => $url,
    CURLOPT_RETURNTRANSFER  => true,
    CURLOPT_CUSTOMREQUEST   => (!empty($bluesnapid)?'PUT':'POST'),
    CURLOPT_POST            => 1,
    CURLOPT_POSTFIELDS      => $jsonEncodedData,
    CURLOPT_SSL_VERIFYHOST  => 0,
    CURLOPT_SSL_VERIFYPEER  => 0,
    CURLOPT_HTTPHEADER  => array('Accept: application/json','Content-Type: application/json','Authorization: Basic '. base64_encode("$username:$password"),'Content-Length: ' . strlen($jsonEncodedData))
);
curl_setopt_array($curl, $opts);
$res = curl_exec($curl);
$result = json_decode($res);

if(isset($result->message[0]))
{
	if($result->message[0]->code=='14040')echo("Token expired. Please refresh the page and try again.");
	else echo($result->message[0]->description);
}
elseif(isset($result->vaultedShopperId))
{
	$cc = $last4.';'.$cardtype;
	$bluesnapid = $result->vaultedShopperId;

	if ($doa == "0000-00-00")
	{
		$newdate = date('Y-m-d');
		$stmt = 'update company set blastname = ?,bfirstname = ?,billingaddress = ?, package = "Paid", howpaying = "cc", cc = ?, expdate = ?, dateofacceptance = ?, billingzip = ?, bluesnapid = ? where shopid = ?';
		if ($query = $conn->prepare($stmt))
		{
			$query->bind_param("sssssssss",$blast,$bfirst,$baddress,$cc,$exp,$newdate,$bzip,$bluesnapid,$shopid);
		  $query->execute();
		  $conn->commit();
		}

		  $stmt = 'select companyemail from company where shopid = ?';
		  if ($query = $conn->prepare($stmt))
		  {
		    $query->bind_param("s",$shopid);
		    $query->execute();
		    $query->bind_result($companyemail);
		    $query->fetch();
		    $query->close();
		  }

		//SF Update
		$data = array('ShopId'=>$shopid,'DateOfAcceptance'=>date('m/d/Y'),'email'=>$companyemail);
        $jsonEncodedData = json_encode($data);
        $curl = curl_init();
        $opts = array(
    CURLOPT_URL             => 'https://endpoint.scribesoft.com/v1/orgs/47078/requests/20228?accesstoken=03fdbdf1-0c25-4fc8-84fb-472f7a90e4d6',
    CURLOPT_RETURNTRANSFER  => true,
    CURLOPT_CUSTOMREQUEST   => 'POST',
    CURLOPT_POST            => 1,
    CURLOPT_POSTFIELDS      => $jsonEncodedData,
    CURLOPT_HTTPHEADER  => array('Content-Type: application/json','Content-Length: ' . strlen($jsonEncodedData))
        );
       curl_setopt_array($curl, $opts);
       $result = curl_exec($curl);
       curl_close($curl);

       $matco = $_COOKIE['matco'] ?? 'no';
       $protractor = $_COOKIE['protractor'] ?? 'no';

       if($matco=='yes')
       sendEmailMandrillTemplate($companyemail,"","","matco-new-customer");
       elseif($protractor=='yes')
       sendEmailMandrillTemplate($companyemail,"","","protractor-new-customer");
       else
       sendEmailMandrillTemplate($companyemail,"","","shopboss-new-customer");



	}
	else
	{
    $stmt = 'update company set blastname = ?,bfirstname = ?,billingaddress = ?, package = "Paid", howpaying = "cc", cc = ?, expdate = ?, billingzip = ?, bluesnapid = ? where shopid = ?';
		if ($query = $conn->prepare($stmt))
		{
			$query->bind_param("ssssssss",$blast,$bfirst,$baddress,$cc,$exp,$bzip,$bluesnapid,$shopid);
		  $query->execute();
		  $conn->commit();
		}
	}
		
		   //Notification
       $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='155'";
       $query = $conn->prepare($stmt);
       $query->execute();
       $query->store_result();
       $numrows = $query->num_rows();
       if ($numrows > 0)
      {
       $query->bind_result($textcontent,$emailcontent,$popupcontent);
		   $query->fetch();
		   $emailcontent=str_replace("*|CC|*",substr($cc,-4),$emailcontent);
		   $popupcontent=str_replace("*|CC|*",substr($cc,-4),$popupcontent);
		   $textcontent=str_replace("*|CC|*",substr($cc,-4),$textcontent);

		   $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'155',?,?,?)";
		   if ($query = $conn->prepare($stmt))
		   {
		   $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
		   $query->execute();
		   $conn->commit();
		   $query->close();
		   }
      }

      $ch = curl_init();
      curl_setopt($ch, CURLOPT_URL, "https://hooks.zapier.com/hooks/catch/2237757/okb08kt/?shopid=".$shopid);
      curl_setopt($ch, CURLOPT_HEADER, 0);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
      curl_exec($ch);
      curl_close($ch);

        $dts = localTimeStamp($shopid);
	    $usr = $_COOKIE['usr'];
	    $cat = "Account Update";
	    $ev = "Your Shop Boss Account Payment details updated";
		$stmt = "insert into `audit` (shopid,`category`,`event`,`useraccount`,`eventdatetime`) values ('$shopid','$cat','$ev','$usr','$dts')";
		if ($query = $conn->prepare($stmt))
		{
			$query->execute();
			$conn->commit();
		}

		$matco = $_COOKIE['matco'] ?? 'no';

	    $slackdata = "Shop ID: $shopid\nShop Name: ".strtoupper($_COOKIE['shopname'])."\nPackage: $package\nDate Added: ".date('m/d/Y')."\nBluesnap ID: $bluesnapid\nMatco: ".ucfirst($matco);

		$data = array('text'=>$slackdata);
		$jsonEncodedData = json_encode($data);
		$curl = curl_init();
		$opts = array(
		    CURLOPT_URL             => '*******************************************************************************',
		    CURLOPT_RETURNTRANSFER  => true,
		    CURLOPT_CUSTOMREQUEST   => 'POST',
		    CURLOPT_POST            => 1,
		    CURLOPT_POSTFIELDS      => $jsonEncodedData,
		    CURLOPT_HTTPHEADER  => array('Content-Type: application/json','Content-Length: ' . strlen($jsonEncodedData))
		);
		curl_setopt_array($curl, $opts);
		$result = curl_exec($curl);
		curl_close($curl);

      echo($bluesnapid);
}
else
echo("Failed");
curl_close($curl);
}

elseif ($_POST['typ'] == "achsave"){
	
	$username="API_16299867132592130162459";
    $password="Toby1dog";
    $zip = trim($_POST['azip']);
	$first = trim($_POST['afirst']);
	$last = trim($_POST['alast']);
	$company = trim($_POST['acomp']);
	$account = trim($_POST['account']);
	$routing = trim($_POST['routing']);
	$accounttype = $_POST['accounttype'];
	$bluesnapid = $_POST['bluesnapid'];
	$shopid = $_COOKIE['shopid'];
	$package = strtoupper($_POST['subscription']);

	if(!empty($bluesnapid))
	{
		$url = "https://ws.bluesnap.com/services/2/vaulted-shoppers/".$bluesnapid;
		$stmt = 'select bfirstname,blastname,billingzip,bankaccount,routing from company where shopid = ?';
	  if ($query = $conn->prepare($stmt))
	  {
	    $query->bind_param("s",$shopid);
	    $query->execute();
	    $query->bind_result($bfirst,$blast,$bzip,$oldaccount,$oldrouting);
	    $query->fetch();
	    $query->close();
	  }
	  if(!empty($oldaccount))
	  {
	  $rarr = explode('|',$oldrouting);
	  $oldr = substr($rarr[0],-5);
	  $oldt = $rarr[1];
		$data = array
   (
   "firstName" => $bfirst,
   "lastName" => $blast,
   "zip" => $bzip,
   "companyName" => $company,
   "paymentSources" => array("ecpDetails"=>array(0 => array("billingContactInfo" =>array("firstName" => $bfirst,"lastName" => $blast,"zip" => $bzip),"ecp" =>array("accountType"=>$oldt,"publicAccountNumber" => substr($oldaccount,-5),"publicRoutingNumber" => $oldr),"status"=>"D")))
   );

		$jsonEncodedData = json_encode($data);
		$curl = curl_init();
		$opts = array(
		    CURLOPT_URL             => $url,
		    CURLOPT_RETURNTRANSFER  => true,
		    CURLOPT_CUSTOMREQUEST   => 'PUT',
		    CURLOPT_POST            => 1,
		    CURLOPT_POSTFIELDS      => $jsonEncodedData,
		    CURLOPT_SSL_VERIFYHOST  => 0,
		    CURLOPT_SSL_VERIFYPEER  => 0,
		    CURLOPT_HTTPHEADER  => array('Accept: application/json','Content-Type: application/json','Authorization: Basic '. base64_encode("$username:$password"),'Content-Length: ' . strlen($jsonEncodedData))
		);
		curl_setopt_array($curl, $opts);
		$res=curl_exec($curl);
	  }
	}
	else
	$url = "https://ws.bluesnap.com/services/2/vaulted-shoppers";

	$data = array
 (
   "firstName" => $first,
   "lastName" => $last,
   "country" => "us",
   "shopperCurrency" => "USD",
   "merchantShopperId" => $shopid,
   "companyName" => strtoupper($_COOKIE['shopname']),
   "zip" => $zip,
   "companyName" => $company,
   "paymentSources" => array("ecpDetails"=>array(0 => array("ecp" => array("routingNumber" => $routing,"accountType"=>$accounttype,"accountNumber" => $account))))
 );

$jsonEncodedData = json_encode($data);
$curl = curl_init();
$opts = array(
    CURLOPT_URL             => $url,
    CURLOPT_RETURNTRANSFER  => true,
    CURLOPT_CUSTOMREQUEST   => (!empty($bluesnapid)?'PUT':'POST'),
    CURLOPT_POST            => 1,
    CURLOPT_POSTFIELDS      => $jsonEncodedData,
    CURLOPT_SSL_VERIFYHOST  => 0,
    CURLOPT_SSL_VERIFYPEER  => 0,
    CURLOPT_HTTPHEADER  => array('Accept: application/json','Content-Type: application/json','Authorization: Basic '. base64_encode("$username:$password"),'Content-Length: ' . strlen($jsonEncodedData))
);
curl_setopt_array($curl, $opts);
$res = curl_exec($curl);

$result = json_decode($res);

if(isset($result->vaultedShopperId))
{
	$routing = substr($routing,-5).'|'.$accounttype;
	$account = substr($account,-5);
	$bluesnapid = $result->vaultedShopperId;

	$stmt = 'update company set blastname = ?,bfirstname = ?, billingzip = ?, package = "Paid", howpaying = "ach", bankaccount = ?, routing = ?, bluesnapid = ? where shopid = ?';
	if ($query = $conn->prepare($stmt))
	{
	  $query->bind_param("sssssss",$last,$first,$zip,$account,$routing,$bluesnapid,$shopid);
	  $query->execute();
	  $conn->commit();
	}

    //Notification
       $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='155'";
       $query = $conn->prepare($stmt);
       $query->execute();
       $query->store_result();
       $numrows = $query->num_rows();
       if ($numrows > 0)
      {
       $query->bind_result($textcontent,$emailcontent,$popupcontent);
		   $query->fetch();
		   $emailcontent=str_replace("*|CC|*",substr($cc,-4),$emailcontent);
		   $popupcontent=str_replace("*|CC|*",substr($cc,-4),$popupcontent);
		   $textcontent=str_replace("*|CC|*",substr($cc,-4),$textcontent);

		   $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'155',?,?,?)";
		   if ($query = $conn->prepare($stmt))
		   {
		   $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
		   $query->execute();
		   $conn->commit();
		   $query->close();
		   }
      }

      $ch = curl_init();
      curl_setopt($ch, CURLOPT_URL, "https://hooks.zapier.com/hooks/catch/2237757/okb08kt/?shopid=".$shopid);
      curl_setopt($ch, CURLOPT_HEADER, 0);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
      curl_exec($ch);
      curl_close($ch);

      $dts = localTimeStamp($shopid);
      $usr = $_COOKIE['usr'];
      $cat = "Account Update";
      $ev = "Your Shop Boss Account Payment details updated";
	  $stmt = "insert into `audit` (shopid,`category`,`event`,`useraccount`,`eventdatetime`) values ('$shopid','$cat','$ev','$usr','$dts')";
	  if ($query = $conn->prepare($stmt))
	  {
		$query->execute();
		$conn->commit();
	  }

	  $matco = $_COOKIE['matco'] ?? 'no';

	  $slackdata = "Shop ID: $shopid\nShop Name: ".strtoupper($_COOKIE['shopname'])."\nPackage: $package\nDate Added: ".date('m/d/Y')."\nBluesnap ID: $bluesnapid\nMatco: ".ucfirst($matco);

		$data = array('text'=>$slackdata);
		$jsonEncodedData = json_encode($data);
		$curl = curl_init();
		$opts = array(
		    CURLOPT_URL             => '*******************************************************************************',
		    CURLOPT_RETURNTRANSFER  => true,
		    CURLOPT_CUSTOMREQUEST   => 'POST',
		    CURLOPT_POST            => 1,
		    CURLOPT_POSTFIELDS      => $jsonEncodedData,
		    CURLOPT_HTTPHEADER  => array('Content-Type: application/json','Content-Length: ' . strlen($jsonEncodedData))
		);
		curl_setopt_array($curl, $opts);
		$result = curl_exec($curl);
		curl_close($curl);

      echo($bluesnapid);
}
else
echo("Failed");
curl_close($curl);
}

?>
<?php if(isset($conn)){mysqli_close($conn);} ?>
