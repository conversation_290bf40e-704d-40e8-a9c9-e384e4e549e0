<?php

require(CONN);

$shopid = $_POST['shopid'];
$t = $_POST['t'];

if ($t == "definspval"){
	$id = $_POST['id'];
	$v = $_POST['val'];
	$stmt = "update company set defaultinspectionvalue = ? where shopid = ?";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("ss",$v,$shopid);
	    if ($query->execute()){
	    	$conn->commit();
	    }
	    $query->close();
		echo "success";
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
	
	
}

elseif ($t == "deletelogo"){

	$logo = $_POST['logo'];
	$stmt = "update company set logo = '' where shopid = ?";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("s",$shopid);
	    if ($query->execute()){
	    	$conn->commit();
	    }
	    $query->close();
		echo "success";
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
	/*$rpath = "../../sbp/upload/$shopid/$logo";
	echo $rpath."\r\n";
	$path = realpath($rpath);
	echo $path."\r\n";*/
	
}

elseif ($t == "checkbox"){
	$id = $_POST['id'];
	$v = $_POST['val'];
	$table = $_POST['table']??'company';
	
	if ($id != "remotecc"){
		$stmt = "update $table set ".$id." = '".$v."' where shopid = '$shopid'";
		
		if ($query = $conn->prepare($stmt)){
		    $query->execute();
		    $conn->commit();
		    $query->close();
		    
			echo "success";
		
		}else{
			echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}
	}elseif ($id == "remotecc"){
		if ($v == "yes"){
			$stmt = "insert into companysettings (shopid,setting,svalue) values ('$shopid','remotecc','yes')";
			if ($query = $conn->prepare($stmt)){
				$query->execute();
				$conn->commit();
				$query->close();
			}
		}else{
			$stmt = "delete from companysettings where shopid = '$shopid' and setting = 'remotecc'";
			if ($query = $conn->prepare($stmt)){
				$query->execute();
				$conn->commit();
				$query->close();
			}
		}
	}

}

elseif ($t == "tz"){

	$v = $_POST['val'];

	$stmt = "update company set timezone = '".$v."' where shopid = ?";
	//printf (str_replace('?',"'%s'",$stmt),$mil,$mol,$it,$et,$shopid );
	
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("s",$shopid);
	    if ($query->execute()){
	    	$conn->commit();
	    	echo "success";
	    }else{
	    	echo $conn->errno;
	    }
	    
	    $query->close();
	    
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

}

elseif ($t == "carfax"){

	$v = $_POST['val'];

	$stmt = "update company set showcarfaxonly = '".$v."' where shopid = ?";
	//printf (str_replace('?',"'%s'",$stmt),$mil,$mol,$it,$et,$shopid );
	
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("s",$shopid);
	    if ($query->execute()){
	    	$conn->commit();
	    	echo "success";
	    }else{
	    	echo $conn->errno;
	    }
	    
	    $query->close();
	    
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

}

elseif ($t == "calendar"){

	$v = $_POST['val'];

	$stmt = "update company set calendardefault = '".$v."' where shopid = ?";
	
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("s",$shopid);
	    if ($query->execute()){
	    	$conn->commit();
	    	echo "success";
	    }else{
	    	echo $conn->errno;
	    }
	    
	    $query->close();
	    
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

}

mysqli_close($conn);

?>