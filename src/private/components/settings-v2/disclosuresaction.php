<?php

require CONN;

$shopid = $_POST['shopid'];
$ro = $_POST['ro'];
$warr = $_POST['warr'];
$ps = $_POST['ps'];
$qd = $_POST['quotedisc'];

$stmt = "update company set quotedisclosure = ?,rowarrdisclosure = ?,rodisclosure = ?,psdisclosure = ? where shopid = ?";


if ($query = $conn->prepare($stmt)){
	$query->bind_param("sssss",$qd,$warr,$ro,$ps,$shopid);
    if ($query->execute()){
	    $conn->commit();    	
	    echo "success";	
    }else{
		echo $conn->errno;
	}
    $query->close();

       //Notification
       $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='134'";
	   $query = $conn->prepare($stmt);
	   $query->execute();
	   $query->store_result();
	   $numrows = $query->num_rows();
	   if ($numrows > 0)
	   {
	   $query->bind_result($textcontent,$emailcontent,$popupcontent);
	   $query->fetch();
	   $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'134',?,?,?)";
	   if ($query = $conn->prepare($stmt))
	   {
	   $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
	   $query->execute();
	   $conn->commit();
	   $query->close();
	   }
      }
    
	

}else{
	echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}


mysqli_close($conn);

?>