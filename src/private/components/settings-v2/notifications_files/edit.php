<?php

require CONN;


$shopid = $_POST['shopid'];
$t = $_POST['t'];

if($t=='bulkedit')
{
	$notifications=explode(',',$_POST['notifications']);
	$customs=explode(',',$_POST['customs']);
	$employees=$_POST['employees'];
	$alert_types=explode(',',$_POST['alert_types']);

	if(in_array('text',$alert_types))$text_notify=$employees;
	if(in_array('email',$alert_types))$email_notify=$employees;
	if(in_array('shop_reminder',$alert_types))$shop_reminder='1';else $shop_reminder='0';

	$c=0;

	foreach($notifications as $nid)
	{
	  if(in_array('none',$alert_types))
	  {
	  	$stmt = "delete from notification_settings where shopid = '$shopid' and notification_type = '$nid'";
     	if ($query = $conn->prepare($stmt))
     	{
     		$query->execute();
     	   	$conn->commit();
     	   	$query->close();
     	}
     	continue;
	  }
      $stmt = "select id from notification_settings where shopid = '$shopid' and notification_type='$nid'";
	  $query = mysqli_query($conn, $stmt);

	  $customval = $customs[$c];
	  $c++;

	  if(mysqli_num_rows($query) > 0) //record already present for this notification type
	  {
	  	$stmt = "update notification_settings set customval=?,popup_notify=?,text_notify=?,email_notify=? where shopid = ? and notification_type = ?";

     	if ($query = $conn->prepare($stmt))
     	{
		$query->bind_param("sissii",$customval,$shop_reminder,$text_notify,$email_notify,$shopid,$nid);
	    if ($query->execute())
	    	$conn->commit();
	    else
	    echo $conn->errno;

	    	$query->close();
	    }

	  }

	  else
	  {
	  	$stmt = "insert into notification_settings set shopid=?,notification_type = ?,customval = ?,popup_notify=?,text_notify=?,email_notify=?,added_by=?,added_on=now()";

     	if ($query = $conn->prepare($stmt))
     	{
		$query->bind_param("iisissi",$shopid,$nid,$customval,$shop_reminder,$text_notify,$email_notify,$_COOKIE['empid']);
	    if ($query->execute())
	    	$conn->commit();
	    else
	    echo $conn->errno;

	    	$query->close();
	    }
	  }


	}
}


elseif($t=='edit')
{
	$notification_type=$_POST['notification_type'];
	$text_notify=$_POST['emptext'];
	$email_notify=$_POST['empemail'];
	$shop_reminder=$_POST['shop_reminder'];
	$customval=$_POST['custom'];
	$chime=$_POST['chime'];

      $stmt = "select id from notification_settings where shopid = '$shopid' and notification_type='$notification_type'";
	  $query = mysqli_query($conn, $stmt);

	  if(mysqli_num_rows($query) > 0) //record already present for this notification type
	  {
	  	if(empty($shop_reminder) && empty($text_notify) && empty($email_notify))
	  	{
	  		$stmt = "delete from notification_settings where shopid = '$shopid' and notification_type = '$notification_type'";
     	    if ($query = $conn->prepare($stmt))
     	    {
     	    	$query->execute();
     	    	$conn->commit();
     	    	$query->close();
     	    }

	  	}
	  	else
	  	{
	  	$stmt = "update notification_settings set popup_notify=?,text_notify=?,email_notify=?,customval=?,chime=? where shopid = ? and notification_type = ?";
     	if ($query = $conn->prepare($stmt))
     	{
		$query->bind_param("isssssi",$shop_reminder,$text_notify,$email_notify,$customval,$chime,$shopid,$notification_type);
	    if ($query->execute())
	    $conn->commit();
	    else
	    echo $conn->errno;
	    $query->close();
	    }
	    }

	  }

	  elseif(!empty($shop_reminder) || !empty($text_notify) || !empty($email_notify))
	  {
	  	$stmt = "insert into notification_settings set shopid=?,notification_type = ?,popup_notify=?,text_notify=?,email_notify=?,customval=?,chime=?,added_by=?,added_on=now()";

     	if ($query = $conn->prepare($stmt))
     	{
		$query->bind_param("siissssi",$shopid,$notification_type,$shop_reminder,$text_notify,$email_notify,$customval,$chime,$_COOKIE['empid']);
	    if ($query->execute())
	    	$conn->commit();
	    else
	    echo $conn->errno;

	    	$query->close();
	    }
	  }

}

elseif($t=='getempinfo')
{
	$stmt = "select EmployeeLast,EmployeeFirst,EmployeePhone,EmployeeEmail from employees where id = ? and shopid = ?";
    if ($query = $conn->prepare($stmt))
    {
	$query->bind_param("is",$_POST['empid'],$shopid);
    $query->execute();
    $query->bind_result($EmployeeLast,$EmployeeFirst,$EmployeePhone,$EmployeeEmail);
    $query->fetch();
    $query->close();

    echo(json_encode(array('name'=>$EmployeeLast.', '.$EmployeeFirst,'phone'=>$EmployeePhone,'email'=>$EmployeeEmail)));
    }
}

elseif($t=='saveempinfo')
{
	$email = $_POST['email'];
	$phone = str_replace("-","",$_POST['phone']);
	$empid = $_POST['empid'];

	$stmt = "update employees set EmployeePhone = ?,EmployeeEmail = ? where id = ? and shopid = ?";
    if ($query = $conn->prepare($stmt))
    {
	 $query->bind_param("ssis",$phone,$email,$empid,$shopid);
	 if ($query->execute())
	 $conn->commit();
	 else
	 echo $conn->errno;
	 $query->close();
	}

	$stmt = "select EmployeePhone,EmployeeEmail from employees where id = ? and shopid = ?";
    if ($query = $conn->prepare($stmt))
    {
	$query->bind_param("is",$empid,$shopid);
    $query->execute();
    $query->bind_result($EmployeePhone,$EmployeeEmail);
    $query->fetch();
    $query->close();

    echo(json_encode(array('status'=>'success','phone'=>$EmployeePhone,'email'=>$EmployeeEmail)));
    }
}
