<?php

require CONN;

$shopid = $_POST['shopid'];
$enablelogin = $_POST['enablelogin']??'';
$openhours = $_POST['openhours'];
$closedhours = $_POST['closedhours'];

if(!empty($enablelogin) && !empty($openhours) && !empty($closedhours))$bufferhours = $openhours.','.$closedhours;
else $bufferhours = '';

$stmt = "update company set bufferhours = ? where shopid = ?";
if ($query = $conn->prepare($stmt)){

	$query->bind_param("ss",$bufferhours,$shopid);
	$query->execute();
	$conn->commit();
	$query->close();

}

echo "success";
?>