<?php
$component = "settings-v2";
$sub_component = "Canned Jobs";
include getRulesComponent($component);
include getHeadGlobal($component);

$shopid = $_COOKIE['shopid'];

?>

<body>
<?php
include getHeaderGlobal($component);
include getMenuGlobal($component);

?>
<main id="settings" class="min-vh-100">
    <div class="report">
        <div class="col-12">
            <div class="row">
                <div class="col-md-9 col-sm-6">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/settings/settings.php"
                           class="text-secondary">Settings</a>
                        <span
                                class="text-secondary d-print-none ps-3 pe-3">/</span>
                        <h2 class="">Canned Jobs</h2>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 justify-content-end">
                    <button onclick="showCreateCannedJob()" class="btn btn-primary btn-md float-end ms-2">Create New Canned Job</button>
                </div>
            </div>
            <hr/>
        </div>
    </div>
    <div class="d-flex">
        <section class="container-fluid">
            <table class="sbdatatable w-100" id="cj_table">
                <thead>
                <tr>
                    <th style="width: 60%">Canned Job</th>
                    <th>Calculated Price</th>
                    <th>Taxable</th>
                    <th class="text-center">Delete</th>
                </tr>
                </thead>
                <tbody>
                <?php
                $stmt = "select jobname,flatprice,taxable,id from cannedjobs where shopid = '" . $shopid . "' order by trim(jobname)";
                //echo $stmt;
                $result = $conn->query($stmt);
                //$conn->store_result();
                while ($row = $result->fetch_array()) {
                    if ($row['flatprice'] > 0) {
                        $fpm = "<br><span style='color:red;font-weight:bold'>If you edit this job it will be converted to a calculated price job instead of a flat price job</span>";
                        $fpm = "";
                        $totalcalcprice = $row['flatprice'];
                    } else {
                        $fpm = "";
                        $tlabor = 0;

                        $lstmt = "select SUM(IF(flatprice = '0.00', (rateforcalc * laborhours) , flatprice)) from cannedlabor where shopid = ? and cannedjobsid = ?";
                        if ($lquery = $conn->prepare($lstmt)) {
                            $lquery->bind_param("si",$shopid,$row['id']);
                            $lquery->execute();
                            $lquery->bind_result($tlabor);
                            $lquery->fetch();
                            $lquery->close();
                        }

                        $pstmt = "select coalesce(sum(partprice * qty),0) pfp from cannedparts where shopid = '$shopid' and cannedjobsid = " . $row['id'];
                        if ($pquery = $conn->prepare($pstmt)) {
                            $pquery->execute();
                            $pquery->bind_result($pfp);
                            $pquery->fetch();
                            $pquery->close();
                        }

                        $sstmt = "select coalesce(sum(subletprice),0) from cannedsublet where shopid = '$shopid' and cannedjobsid = " . $row['id'];
                        if ($squery = $conn->prepare($sstmt)) {
                            $squery->execute();
                            $squery->bind_result($tsublet);
                            $squery->fetch();
                            $squery->close();
                        }

                        $totalcalcprice = $tlabor + $pfp + $tsublet;
                        }
                    ?>
                    <tr>
                        <td onclick="editJob(<?php echo $row['id']; ?>,'<?php echo $row['flatprice']; ?>')"><?php echo $row['jobname']; ?></td>
                        <td onclick="editJob(<?php echo $row['id']; ?>,'<?php echo $row['flatprice']; ?>')">$<?php echo number_format($totalcalcprice, 2) . " " . $fpm; ?></td>
                        <td onclick="editJob(<?php echo $row['id']; ?>,'<?php echo $row['flatprice']; ?>')"><?php echo $row['taxable']; ?></td>
                        <td class="text-center"><i class="fa fa-trash" onclick="deleteJob('<?= $row['jobname'] ?>', '<?php echo $row['id']; ?>')"></i></td>
                    </tr>
                    <?php
                }
                ?>
                </tbody>
            </table>
        </section>
    </div>
</main>

<!-- Apps Modal -->
<div id="cannedmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header">
                <h5 class="modal-title">Create New Canned Job</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-outline mb-4">
                            <input class="form-control" tabindex="1" type="text" id="cjname" name="cjname">
                            <label class="form-label" for="cjname">Job Name</label>
                        </div>
                    </div>
                    <div class="form-row mb-4">
                        <select class="select" id="cjtax" name="cjtax">
                            <option value="yes">Yes</option>
                            <option value="no">No</option>
                        </select>
                        <label class="form-label select-label" for="cjtax">Taxable</label>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-center">
                <button class="btn btn-md btn-primary" type="button" onclick="createCannedJob()">Create Canned Job</button>
            </div>
        </div>
    </div>
</div>

<!-- Apps Modal -->
<div id="editcannedmodal" class="modal fade modal-wide" tabindex="-1" role="dialog" aria-hidden="true">
    <input type="hidden" id="cannedjobid" name="cannedjobid">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header">
                <h5 class="modal-title">Edit Canned Job</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-outline mb-4">
                            <input class="form-control" type="text" id="editcjname" name="editcjname">
                            <label class="form-label" id="editcjnamelabel" for="editcjname">Job Name</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control" readonly disabled type="text" id="editcjcalc" name="editcjcalc">
                            <label class="form-label" id="editcjcalclabel" for="editcjcalc">Calculated Total Price</label>
                        </div>
                        <div class="form-row mb-4">
                            <select class="select" id="editcjtax" name="editcjtax">
                                <option value="yes">Yes</option>
                                <option value="no">No</option>
                            </select>
                            <label class="form-label select-label" id="editcjtaxlabel" for="editcjtax">Taxable</label>
                        </div>
                        <div class="form-row mb-4">
                            <?php
                            $rquery = "select HourlyRate,hourlyrate2,hourlyrate3,hourlyrate4,hourlyrate5,hourlyrate6,hourlyrate7,hourlyrate8,hourlyrate9,hourlyrate10,hourlyrate1label,hourlyrate2label,hourlyrate3label,hourlyrate4label,hourlyrate5label,hourlyrate6label,hourlyrate7label,hourlyrate8label,hourlyrate9label,hourlyrate10label from company where shopid = ?";
                            if ($rstmt = $conn->prepare($rquery)) {
                                $rstmt->bind_param("s", $shopid);
                                $rstmt->execute();
                                $rresult = $rstmt->bind_result($hrate, $hrate2, $hrate3, $hrate4, $hrate5, $hrate6, $hrate7, $hrate8, $hrate9, $hrate10, $hlabel1, $hlabel2, $hlabel3, $hlabel4, $hlabel5, $hlabel6, $hlabel7, $hlabel8, $hlabel9, $hlabel10);
                                $rstmt->fetch();
                                $rstmt->close();
                            }
                            ?>
                            <select class="select" onchange="saveCannedJob('hr')" id="editcjhourlyrate" name="editcjhourlyrate">
                                <option value="none">None</option>
                                <?php if ($hlabel1 != '') { ?>
                                    <option lab="<?= ($hlabel1) ?>" value="<?php echo $hrate; ?>"><?php echo ($hlabel1) . "-" . number_format($hrate, 2); ?></option><?php } ?>
                                <?php if ($hlabel2 != '') { ?>
                                    <option lab="<?= ($hlabel2) ?>" value="<?php echo $hrate2; ?>"><?php echo ($hlabel2) . "-" . number_format($hrate2, 2); ?></option><?php } ?>
                                <?php if ($hlabel3 != '') { ?>
                                    <option lab="<?= ($hlabel3) ?>" value="<?php echo $hrate3; ?>"><?php echo ($hlabel3) . "-" . number_format($hrate3, 2); ?></option><?php } ?>
                                <?php if ($hlabel4 != '') { ?>
                                    <option lab="<?= ($hlabel4) ?>" value="<?php echo $hrate4; ?>"><?php echo ($hlabel4) . "-" . number_format($hrate4, 2); ?></option><?php } ?>
                                <?php if ($hlabel5 != '') { ?>
                                    <option lab="<?= ($hlabel5) ?>" value="<?php echo $hrate5; ?>"><?php echo ($hlabel5) . "-" . number_format($hrate5, 2); ?></option><?php } ?>
                                <?php if ($hlabel6 != '') { ?>
                                    <option lab="<?= ($hlabel6) ?>" value="<?php echo $hrate6; ?>"><?php echo ($hlabel6) . "-" . number_format($hrate6, 2); ?></option><?php } ?>
                                <?php if ($hlabel7 != '') { ?>
                                    <option lab="<?= ($hlabel7) ?>" value="<?php echo $hrate7; ?>"><?php echo ($hlabel7) . "-" . number_format($hrate7, 2); ?></option><?php } ?>
                                <?php if ($hlabel8 != '') { ?>
                                    <option lab="<?= ($hlabel8) ?>" value="<?php echo $hrate8; ?>"><?php echo ($hlabel8) . "-" . number_format($hrate8, 2); ?></option><?php } ?>
                                <?php if ($hlabel9 != '') { ?>
                                    <option lab="<?= ($hlabel9) ?>" value="<?php echo $hrate9; ?>"><?php echo ($hlabel9) . "-" . number_format($hrate9, 2); ?></option><?php } ?>
                                <?php if ($hlabel10 != '') { ?>
                                    <option lab="<?= ($hlabel10) ?>" value="<?php echo $hrate10; ?>"><?php echo ($hlabel10) . "-" . number_format($hrate10, 2); ?></option><?php } ?>
                            </select>
                            <label id="edithrlabel" class="form-label select-label" for="editcjhourlyrate">Default Labor Hourly Rate</label>
                        </div>
                        <div class="form-outline mb-4">
                            <textarea class="form-control" type="text" tabindex="1" id="techstory" name="techstory" ai-writing-tool></textarea>
                            <label class="form-label" id="techstorylabel" for="techstory">Tech Story</label>
                        </div>
                        <div id="canneddetails"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-center">
                <button class="btn btn-md btn-secondary" style="float:left" type="button" onclick="addLabor()">Add Labor</button>
                <button class="btn btn-md btn-secondary" style="float:left;margin-left:5px" type="button" onclick="addPart()">Add Part</button>
                <button class="btn btn-md btn-secondary" style="float:left;margin-left:5px" type="button" onclick="addSublet()">Add Sublet</button>

                <button class="btn btn-md btn-primary" type="button" onclick="saveCannedJob()">Save This Job</button>
            </div>
        </div>
    </div>
</div>

<!-- Apps Modal -->
<div id="addlabormodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <input type="hidden" id="cannedlaborid" value="0">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header">
                <h5 class="modal-title">Edit / Add Labor</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-outline mb-4">
                            <input class="form-control" type="text" id="addlaborname" name="addlaborname">
                            <label class="form-label" id="editlabornamelabel" for="addlaborname">Labor Description</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control" onblur="if (this.value.length == 0){this.value = 0}" type="text" id="addlabortime" name="addlabortime">
                            <label class="form-label" id="editlabortimelabel" for="addlabortime">Labor Time</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control" onblur="if (this.value.length == 0){this.value = 0}" type="text" id="addlaborflatprice" name="addlaborflatprice">
                            <label class="form-label" id="editlaborpricelabel" for="addlaborflatprice">Flat Price for Labor</label>
                        </div>
                        <div class="form-check mb-4">
                            <input type="checkbox" id="addlabornocalc" name="addlabornocalc" class="form-check-input">
                            <label class="form-check-label" id="editlabornocalc" for="addlabornocalc">Confirm Flat Price Rate Override</label>
                            <i class="fa fa-info-circle" data-mdb-toggle="tooltip" title="Checking this box will override the technician’s hourly rate with this flat rate when added to a RO."></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-center">
                <button class="btn btn-md btn-secondary" type="button" onclick="delLabor()">Delete</button>
                <button class="btn btn-md btn-primary" id="btn-savelabor" type="button" onclick="saveLabor()">Save</button>
            </div>
        </div>
    </div>
</div>

<div id="addsubletmodal" class="modal fade" style="z-index:9999" tabindex="-1" role="dialog" aria-hidden="true">
    <input type="hidden" id="cannedsubletid" value="0">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header">
                <h5 class="modal-title">Edit / Add Sublet</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-outline mb-4">
                            <input class="form-control" type="text" id="addsubletdesc" name="addsubletdesc">
                            <label class="form-label" id="addsubletdesclabel" for="addsubletdesc">Sublet Description</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control" type="text" id="addsubletcost" name="addsubletcost">
                            <label class="form-label" id="addsubletcostlabel" for="addsubletcost">Shop Cost</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control" type="text" id="addsubletprice" name="addsubletprice">
                            <label class="form-label" id="addsubletpricelabel" for="addsubletprice">Selling Price</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control" type="text" id="addsubletsupplier" name="addsubletsupplier">
                            <label class="form-label" id="addsubletsupplierlabel" for="addsubletsupplier">Supplier</label>
                        </div>
                        <div class="form-row mb-4">
                            <select class="select" id="addsublettax" name="addsublettax">
                                <option value="yes">Yes</option>
                                <option value="no">No</option>
                            </select>
                            <label class="form-label select-label" for="addsublettax">Taxable</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-center">
                <button class="btn btn-md btn-secondary" type="button" onclick="delSublet()">Delete</button>
                <button class="btn btn-md btn-primary" id="btn-savesublet" type="button" onclick="saveSublet()">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Apps Modal -->
<div id="addpartmodal" class="modal fade" style="z-index:9999;" tabindex="-1" role="dialog" aria-hidden="true">
    <input type="hidden" id="cannedpartid" value="0">
    <input type="hidden" id="cannedjobsid" value="0">
    <input type="hidden" id="cannedalloc">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header">
                <h5 class="modal-title">Edit / Add Part</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-outline mb-4" id="async">
                            <input class="form-control autocomplete" type="text" id="addpartnumber" name="addpartnumber">
                            <label class="form-label" for="addpartnumber">Part Number</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control" type="text" id="addpartdesc" name="addpartdesc">
                            <label class="form-label" id="addpartdesclabel" for="addpartdesc">Part Description</label>
                        </div>
                        <div class="form-row mb-4">
                            <select id="addsupplier" class="select">
                                <?php
                                $stmt = "select suppliername s from supplier where shopid = ? order by displayorder";
                                //printf ( str_replace('?',"'%s'",$stmt),$PartSupplier,$shopid);
                                $matrix = strtolower($_COOKIE['matrix']);
                                if ($query = $conn->prepare($stmt)) {
                                    $query->bind_param("s", $shopid);
                                    $query->execute();
                                    $result = $query->get_result();
                                    $query->store_result();
                                    $numrows = $result->num_rows;
                                    if ($numrows > 0) {
                                        while ($row = $result->fetch_array()) {

                                            echo "<option value=\"" . $row['s'] . "\">" . $row['s'] . "</option>";
                                        }
                                    } else {
                                        echo "<option value='none'>No Suppliers Entered</option>";
                                    }
                                }
                                ?>
                            </select>
                            <label class="form-label select-label" id="addsupplierlabel" for="addsupplier">Supplier</label>
                        </div>
                        <div class="form-row mb-4">
                            <select id="addcategory" onchange="calcPrices()" class="select">
                                <?php
                                $stmt = "select distinct category c from category where shopid = ? order by displayorder";
                                $matrix = strtolower($_COOKIE['matrix']);
                                if ($query = $conn->prepare($stmt)) {
                                    $query->bind_param("s", $shopid);
                                    $query->execute();
                                    $result = $query->get_result();
                                    $query->store_result();
                                    $numrows = $result->num_rows;
                                    if ($numrows > 0) {
                                        while ($row = $result->fetch_array()) {

                                            echo "<option value='" . $row['c'] . "'>" . $row['c'] . "</option>";
                                        }
                                    } else {
                                        echo "<option value='none'>No Suppliers Entered</option>";
                                    }
                                }
                                ?>
                            </select>
                            <label class="form-label select-label" id="addcategorylabel" for="addcategory">Category</label>
                        </div>
                        <div class="form-row mb-4">
                            <select id="addpartcode" class="select">
                                <?php
                                $stmt = "select codes from codes where shopid = ? order by codes asc";
                                if ($query = $conn->prepare($stmt)) {
                                    $query->bind_param("s", $shopid);
                                    $query->execute();
                                    $result = $query->get_result();
                                    $query->store_result();
                                    $numrows = $result->num_rows;
                                    if ($numrows > 0) {
                                        while ($row = $result->fetch_array()) {
                                            echo '<option value="' . $row['codes'] . '">' . $row['codes'] . '</option>';
                                        }
                                    } else {
                                        echo '<option value="New">New</option>';
                                    }
                                }
                                ?>
                            </select>
                            <label class="form-label select-label" id="addpartcodelabel" for="addpartcode">Part Code</label>
                        </div>
                        <div class="col-sm-12">
                            <div class="form-outline mb-4">
                                <input class="form-control" onblur="calcPrices()" type="text" id="addpartcost" name="addpartcost">
                                <label class="form-label" id="addpartcostlabel" for="addpartcost">Shop Cost</label>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="addpartprice" name="addpartprice">
                                <label class="form-label" id="addpartpricelabel" for="addpartprice">Selling Price</label>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="addquantity" name="addquantity">
                                <label class="form-label" id="addquantitylabel" for="addquantity">Quantity</label>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="addbin" name="addbin">
                                <label class="form-label" id="addbinlabel" for="addbin">Bin</label>
                            </div>
                            <div class="form-row mb-4">
                                <select class="select" type="text" id="addtaxable" name="addtaxable">
                                    <option value="yes">Yes</option>
                                    <option value="no">No</option>
                                </select>
                                <label class="form-label select-label" id="addtaxablelabel" for="addtaxable">Taxable</label>
                            </div>
                            <div class="form-row mb-4">
                                <select class="select" type="text" id="addmatrix" name="addmatrix">
                                    <option value="no">No</option>
                                    <option value="yes">Yes</option>
                                </select>
                                <label class="form-label select-label" id="addmatrixlabel" for="addmatrix">Override Matrix</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-center">
                    <button class="btn btn-md btn-secondary" type="button" onclick="delPart()">Delete</button>
                    <button class="btn btn-md btn-primary" id="btn-savepart" type="button" onclick="savePart()">Save</button>
                </div>
            </div>
        </div>
    </div>


    <?php
    $component = '';
    include getScriptsGlobal('');
    // include getFooterComponent($component);
    ?>

    <script>
        jQuery(function () {
            document.querySelectorAll('.form-outline').forEach((formOutline) => {
                new mdb.Input(formOutline).init();
            });

            $("#cj_table").dataTable({
                responsive: true,
                fixedHeader: {
                    headerOffset: 68
                },
                colReorder: true,
                select: true,
                scrollY: false,
                scrollX: false,
                scroller: false,
                paging: false,
                language: {
                    search: '',
                    searchPlaceholder: "Search..."
                },
            });

            $('#srch').keyup(function () {
                vals = $(this).val().toLowerCase()
                $('.srchbox').each(function () {
                    v = $(this).html().toLowerCase()
                    if (v.indexOf(vals) >= 0) {
                        $(this).parent().show()
                        console.log(vals + ":" + v)
                    } else {
                        $(this).parent().hide()
                        console.log(vals + ":" + v)
                    }
                })
            })

            <?php
            if (isset($_GET['id'])) {
                echo "sbalert('Canned Job has been saved');";
                echo 'history.pushState(null, "", location.href.split("?")[0]);';
            }
            if (isset($_GET['newid'])) {
                echo "editJob(" . $_GET['newid'] . ");";
                echo 'history.pushState(null, "", location.href.split("?")[0]);';
            }

            ?>

            const asyncAutocomplete = document.getElementById('async');
            const asyncFilter = async (query) => {

                const url = `cannedjobfiles/findpart.php?query=${encodeURI(query)}`;
                const response = await fetch(url);
                const data = await response.json();
                return Object.values(data).map(obj => ({
                    display: obj.value,
                    value: obj.orival
                }));
            };

            const autocomplete = new mdb.Autocomplete(asyncAutocomplete, {
                filter: asyncFilter,
                displayValue: (value) => value.display,
                setValue: (value) => value.value
            });

            asyncAutocomplete.addEventListener('itemSelect.mdb.autocomplete', (e) => {
                e.preventDefault()
                infolist = e.value.value
                infolist = infolist.replace("Part No:", "").replace("Desc:", "").replace("Price:", "").replace("Supplier:", "").replace("Category:", "").replace("Cost:", "").replace("ID:", "").replace("TAX:", "").replace("Matrix:", "").replace("Bin:", "")
                infoarray = infolist.split("|")
                console.log(infoarray);
                alloc = infoarray[0]
                pn = infoarray[1].trim()
                pd = infoarray[2].trim()
                pp = infoarray[3].trim()
                su = infoarray[4].trim()
                ca = infoarray[5].trim()
                co = infoarray[6].trim()
                tax = infoarray[8].trim()
                matrix = infoarray[9].trim()
                bin = infoarray[10].trim()
                console.log("taxable:" + tax)
                $('#addpartnumber').val(pn).removeClass("active").addClass("active")
                $('#addpartdesc').val(pd).removeClass("active").addClass("active")
                $('#addpartprice').val(pp).removeClass("active").addClass("active")
                $('#addsupplier').val(su).removeClass("active").addClass("active")
                $('#addcategory').val(ca).removeClass("active").addClass("active")
                $('#addpartcost').val(co).removeClass("active").addClass("active")
                $('#addtaxable').val(tax).removeClass("active").addClass("active")
                $('#addmatrix').val(matrix).removeClass("active").addClass("active")
                $('#addbin').val(bin).removeClass("active").addClass("active")
                $('#cannedalloc').val(alloc).removeClass("active").addClass("active")
                $('#addquantity').focus()
                autocomplete.close();

            })
            /*
            $("#addpartnumber").on("input", function () {
                if ($(this).val().length >= 2) {
                    var options = {};
                    options.url = "cannedjobfiles/findpart.php";
                    options.type = "GET";
                    options.data = {
                        "query": $("#addpartnumber").val()
                    };
                    options.dataType = "json";
                    options.success = function (data) {
                        //console.log(data)
                        $("#addpartdatalist").empty();
                        $.each(data, function (i, v) {
                            datastr = "<option value='" + data[i].partnumber + "'></option>"
                            $("#addpartdatalist").append(datastr).show();
                            //console.log(datastr)
                        });
                    };
                    $.ajax(options);
                    //calcPrices()
                }
                v = $(this).val()
                //console.log("input val:"+v)
                if (v.indexOf("|") > 0) {
                    //console.log("changing")
                    infolist = $(this).val()
                    // split on the " | " and complete the form
                    console.log("infolist:" + infolist)
                    infolist = infolist.replace("Part No:", "").replace("Desc:", "").replace("Price:", "").replace("Supplier:", "").replace("Category:", "").replace("Cost:", "").replace("ID:", "").replace("TAX:", "").replace("Matrix:", "").replace("Bin:", "")
                    infoarray = infolist.split(" | ")
                    alloc = infoarray[0]
                    pn = infoarray[1]
                    pd = infoarray[2]
                    pp = infoarray[3]
                    su = infoarray[4]
                    ca = infoarray[5]
                    co = infoarray[6]
                    tax = infoarray[8]
                    matrix = infoarray[9]
                    bin = infoarray[10]
                    console.log("taxable:" + tax)
                    $('#addpartnumber').val(pn)
                    $('#addpartdesc').val(pd)
                    $('#addpartprice').val(pp)
                    $('#addsupplier').val(su)
                    $('#addcategory').val(ca)
                    $('#addpartcost').val(co)
                    $('#addtaxable').val(tax)
                    $('#addmatrix').val(matrix)
                    $('#addbin').val(bin)
                    $('#cannedalloc').val(alloc)
                    $('#addquantity').focus()
                }
            });
    */
        });

        function calcPrices() {
            console.log("calcing")
            if ($('#addmatrix').val() == "no") {
                <?php
                $stmt = "select category,factor,start,end from category where shopid = ? order by Category, Start";

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("s", $shopid);
                    $query->execute();
                    $result = $query->get_result();
                    $rs = array();
                    while ($row = $result->fetch_assoc()) {
                        $rs[] = $row;
                    }
                    echo "var clist = " . json_encode($rs) . "\r\n";
                }
                ?>
                srch = $('#addcategory').val().toLowerCase()
                amt = $('#addpartcost').val()
                $.each(clist, function (i, v) {
                    //console.log(JSON.stringify(i)+":"+JSON.stringify(v))
                    if (v.category.toUpperCase() === srch.toUpperCase() && v.start <= amt && v.end >= amt) {
                        console.log(JSON.stringify(i) + ":" + JSON.stringify(v))
                        $('#addpartprice').val(Math.round((amt * v.factor) * 100) / 100)
                    }
                });


            }
        }

        function createCannedJob() {

            cjname = encodeURIComponent($('#cjname').val())
            cjtax = $('#cjtax').val()
            //console.log(cjname+":"+cjflat+":"+cjtax)///

            if (cjname.length == 0 || cjname == 'null') {
                sbalert("Job Name is Required");
                return;
            }

            $.ajax({
                data: "taxable=" + cjtax + "&shopid=<?php echo $shopid; ?>&type=newjob&cjname=" + cjname,
                url: "cannedjobfiles/cannedjob_new.php",
                success: function (r) {
                    console.log(r)
                    if (r.indexOf("|") > 0) {
                        rar = r.split("|")
                        newid = rar[1]
                        location.href = 'cannedjobs_new.php?newid=' + newid
                    } else {
                        console.log(r)
                    }
                }
            });

        }

        function saveCannedJob(p = '') {

            cannedid = $('#cannedjobid').val()
            editcjname = encodeURIComponent($('#editcjname').val())
            editcjtax = $('#editcjtax').val()
            if ($('#editcjhourlyrate').val() != 'none') {
                editcjratelabel = $('#editcjhourlyrate').find(':selected').attr('lab')
                editcjrate = $('#editcjhourlyrate').val()
            } else {
                editcjratelabel = ''
                editcjrate = ''
            }
            techstory = encodeURIComponent($('#techstory').val())
            showLoader();
            $.ajax({
                data: "taxable=" + editcjtax + "&id=" + cannedid + "&jobname=" + editcjname + "&rate=" + editcjrate + "&ratelabel=" + editcjratelabel + "&techstory=" + techstory + "&type=savejob&shopid=<?php echo $shopid; ?>",
                url: "cannedjobfiles/cannedjob_new.php",
                success: function (r) {
                    if (r == "success" && p == '') {
                        location.href = 'cannedjobs_new.php?id=' + cannedid
                    } else {
                        console.log(r);
                        hideLoader();
                    }
                }
            });

        }

        function deleteJob(cname, cannedid) {
            sbconfirm("Are you sure?", "Are you sure you want to delete Canned Job \""+cname+"\" ?", function () {
                showLoader();
                $.ajax({
                    data: "shopid=<?php echo $shopid; ?>&cannedid=" + cannedid + "&type=deletejob",
                    url: "cannedjobfiles/cannedjob_new.php",
                    success: function (r) {
                        if (r == "success") {
                            location.reload()
                        } else {
                            console.log(r)
                            hideLoader();
                        }
                    }
                });
            });

        }

        function savePart() {

            pn = encodeURIComponent($('#addpartnumber').val())
            pd = encodeURIComponent($('#addpartdesc').val())
            su = encodeURIComponent($('#addsupplier').val())
            cat = encodeURIComponent($('#addcategory').val())
            pc = encodeURIComponent($('#addpartcode').val())
            pcost = encodeURIComponent($('#addpartcost').val())
            pp = encodeURIComponent($('#addpartprice').val())
            qty = encodeURIComponent($('#addquantity').val())
            tax = encodeURIComponent($('#addtaxable').val())
            matrix = encodeURIComponent($('#addmatrix').val())
            bin = encodeURIComponent($('#addbin').val())
            alloc = $('#cannedalloc').val()
            id = $('#cannedpartid').val()

            console.log(cat == 'null')

            // check for required values
            if (pn.length == 0 || pn == 'null') {
                sbalert("Part Number is required");
                return;
            }
            if (pd.length == 0) {
                sbalert("Part Description is required");
                return
            }
            if (su.length == 0 || su == 'null' || su == '') {
                sbalert("Supplier is required");
                return
            }
            if (cat.length == 0 || cat == 'null' || cat == '') {
                sbalert("Part Category is required");
                return;
            }
            if (pc.length == 0 || pc == 'null' || pc == '') {
                sbalert("Part Code is required");
                return
            }
            if (pcost.length == 0 || !$.isNumeric(pcost)) {
                sbalert("Part Cost is required and must be a number");
                return
            }
            if (pp.length == 0 || !$.isNumeric(pp)) {
                sbalert("Selling Price is required and must be a number");
                return
            }
            if (qty.length == 0 || !$.isNumeric(qty)) {
                sbalert("Quantity is required and must be a number");
                return
            }
            cannedid = $('#cannedjobid').val()

            $('#btn-savepart').attr('disabled', 'disabled')
            showLoader();
            ds = "alloc=" + alloc + "&bin=" + bin + "&matrix=" + matrix + "&tax=" + tax + "&cannedid=" + cannedid + "&type=savepart&pn=" + pn + "&pd=" + pd + "&su=" + su + "&cat=" + cat + "&pc=" + pc + "&pcost=" + pcost + "&pp=" + pp + "&qty=" + qty + "&id=" + id + "&shopid=<?php echo $shopid; ?>"
            console.log(ds)
            $.ajax({
                data: ds,
                url: "cannedjobfiles/cannedjob_new.php",
                success: function (r) {
                    if (r == "success") {
                        $('#addpartmodal').modal('hide')
                        cannedid = $('#cannedjobsid').val()
                        getCannedJobDetails()
                        $('#cannedjobsid').val("0")
                        $('#cannedpartsid').val("0")
                        $('#cannedlaborid').val("0")
                    } else {
                        console.log(r)
                    }

                    $('#btn-savepart').attr('disabled', false)
                    hideLoader();
                }
            });


        }

        function addPart() {

            $('#addpartmodal').modal('show')
            $('#addpartnumber').val('').removeClass("active");
            $('#addpartdesc').val('').removeClass("active");
            $('#addpartcost').val('').removeClass("active");
            $('#addpartprice').val('').removeClass("active");
            $('#addquantity').val('').removeClass("active");
            $('#addmatrix').val('no').removeClass("active");
            $('#cannedpartid').val(0)
            setTimeout(function () {
                $('#addpartnumber').focus()
            }, 500);
        }

        function editPart(id, cannedjobsid) {

            $('#cannedjobsid').val(cannedjobsid)
            ds = "type=editpart&shopid=<?php echo $shopid; ?>&partid=" + id
            $.ajax({
                data: ds,
                url: "cannedjobfiles/cannedjob_new.php",
                success: function (r) {
                    console.log(r)
                    //$partnumber."|".$partdescription."|".$supplier."|".$partcost."|".$partprice."|".$qty."|".$tax."|".$partcategory."|".$partcode."|".$partprice
                    if (r.indexOf("|") > 0) {
                        rar = r.split("|")
                        partnumber = rar[0]
                        partdescription = rar[1]
                        supplier = rar[2]
                        partcost = rar[3]
                        partprice = rar[4]
                        qty = rar[5]
                        tax = rar[6]
                        partcategory = rar[7]
                        partcode = rar[8]
                        matrix = rar[9]
                        bin = rar[10]
                        alloc = rar[11]

                        $('#addpartnumber').val(partnumber).removeClass("active").addClass("active")
                        $('#addpartdesc').val(partdescription).removeClass("active").addClass("active")
                        $('#addsupplier').val(supplier).removeClass("active").addClass("active")
                        $('#addcategory').val(partcategory).removeClass("active").addClass("active")
                        $('#addpartcode').val(partcode).removeClass("active").addClass("active")
                        $('#addpartcost').val(partcost).removeClass("active").addClass("active")
                        $('#addpartprice').val(partprice).removeClass("active").addClass("active")
                        $('#addquantity').val(qty).removeClass("active").addClass("active")
                        $('#cannedpartid').val(id).removeClass("active").addClass("active")
                        $('#addtaxable').val(tax).removeClass("active").addClass("active")
                        $('#addmatrix').val(matrix).removeClass("active").addClass("active")
                        $('#addbin').val(bin).removeClass("active").addClass("active")
                        $('#cannedalloc').val(alloc).removeClass("active").addClass("active")


                    }
                }
            });

            $('#addpartmodal').modal('show')
            $('#editcannedmodal').css('opacity', '1.0')
            $('#addpartmodal').on("hidden.mdb.modal", function () {
                $('#editcannedmodal').css('opacity', '1.0')
            });
        }

        function editJob(id, fp) {
            $('#editcannedmodal').modal('hide')
            $('#canneddetails').html('')
            $('#cannedjobid').val(id)
            $('#editcjhourlyrate').val('none').removeClass("active")
            ds = "type=main&shopid=<?php echo $shopid; ?>&id=" + id + "&fp=" + fp
            console.log(ds)
            showLoader();
            $.ajax({

                data: ds,
                url: "cannedjobfiles/cannedjob_new.php",
                dataType: "json",
                success: function (r) {
                    console.log(r)
                    if (r.status == 'success') {
                        jobname = r.jobname
                        price = r.price
                        taxable = r.taxable
                        hrate = r.rate
                        hrlabel = r.ratelabel
                        tstory = r.techstory
                        $('#editcjname').val(jobname).addClass("active")
                        $('#editcjcalc').val(price).addClass("active")
                        $('#editcjtax').val(taxable.toLowerCase()).addClass("active")
                        $('#techstory').val(tstory).addClass("active")

                        if (hrlabel != '') {
                            $("#editcjhourlyrate > option").each(function () {
                                if (this.value != '' && Math.round(parseFloat(this.value)) == Math.round(parseFloat(hrate)) && $(this).attr('lab').toLowerCase() == hrlabel.toLowerCase()) {
                                    $(this).prop('selected', 'selected')
                                    $("#editcjhourlyrate").addClass("active")
                                }
                            });
                        }
                    }
                    $('#editcannedmodal').modal('show')
                    hideLoader();
                }

            });
            setTimeout('getCannedJobDetails()', 500)
            

        }

        function showCreateCannedJob() {

            $('#cannedmodal').modal('show')
            setTimeout(function () {
                $('#cjname').focus()
            }, 700)

        }

        function addLabor() {

            $('#editcannedmodal').css('opacity', '1.0')
            $('#addlabormodal').modal('show')
            console.log($('#cannedjobid').val())
            $('#addlaborname').val("").removeClass("active");
            $('#addlabortime').val("").removeClass("active");
            $('#addlaborflatprice').val("").removeClass("active");
            $('#cannedlaborid').val(0)
            $('#addlabornocalc').attr('checked', false).removeClass("active");

            setTimeout(function () {
                $('#addlaborname').focus()
            }, 500)
            $('#addlabormodal').on("hidden.mdb.modal", function () {
                $('#editcannedmodal').css('opacity', '1.0')
            });

        }

        function addSublet() {

            $('#editcannedmodal').css('opacity', '1.0')
            $('#addsubletmodal').modal('show')
            $('#addsubletmodal').on("hidden.mdb.modal", function () {
                $('#editcannedmodal').css('opacity', '1.0')
            });

        }

        function editLabor(id, labor, hours, price, nocalc) {

            $('#editcannedmodal').css('opacity', '1.0')
            $('#addlabormodal').modal('show')
            setTimeout(function () {
                $('#addlaborname').val(labor).addClass("active")
                $('#addlabortime').val(hours).addClass("active")
                $('#cannedlaborid').val(id).addClass("active")
                $('#addlaborflatprice').val(price).addClass("active")
                if (nocalc == '1') $('#addlabornocalc').attr('checked', true)
                else
                    $('#addlabornocalc').attr('checked', false)

                $("#addlabornocalc").addClass("active")

            }, 500)
            $('#addlabormodal').on("hidden.mdb.modal", function () {
                $('#editcannedmodal').css('opacity', '1.0')
            });


        }

        function editSublet(id, desc, cost, price, supplier, tax) {

            $('#editcannedmodal').css('opacity', '1.0')
            $('#addsubletmodal').modal('show')
            setTimeout(function () {
                $('#addsubletdesc').val(desc).addClass("active")
                $('#addsubletcost').val(cost).addClass("active")
                $('#addsubletprice').val(price).addClass("active")
                $('#cannedsubletid').val(id).addClass("active")
                $('#addsubletsupplier').val(supplier).addClass("active")
                $('#addsublettax').val(tax).addClass("active")

            }, 500)
            $('#addsubletmodal').on("hidden.mdb.modal", function () {
                $('#editcannedmodal').css('opacity', '1.0')
            });


        }

        function saveLabor() {

            labor = encodeURIComponent($('#addlaborname').val())
            hours = $('#addlabortime').val()
            id = $('#cannedlaborid').val()
            cannedid = $('#cannedjobid').val()
            addlaborflatprice = $('#addlaborflatprice').val()
            if ($('#addlabornocalc').is(':checked')) nocalc = '1';
            else nocalc = '0';
            if ($('#editcjhourlyrate').val() != 'none')
                addcjrate = $('#editcjhourlyrate').val()
            else
                addcjrate = '';

            if (labor.length == 0 || labor == 'null') {
                sbalert("Labor Description is required");
                return;
            }
            if (hours.length == 0 || !$.isNumeric(hours)) {
                sbalert("Labor Time is required and must be a number");
                return
            }

            $('#btn-savelabor').attr('disabled', 'disabled')
            showLoader();

            ds = "addlaborflatprice=" + addlaborflatprice + "&cannedjobid=" + cannedid + "&type=editlabor&labor=" + labor + "&hours=" + hours + "&laborid=" + id + "&nocalc=" + nocalc + "&shopid=<?php echo $shopid; ?>&addcjrate=" + addcjrate
            console.log("save labor:" + ds)
            $.ajax({
                data: ds,
                url: "cannedjobfiles/cannedjob_new.php",
                success: function (r) {
                    console.log(r)
                    if (r == "success") {
                        $('#addlabormodal').modal('hide')
                        $('#editcannedmodal').css('opacity', '1.0')
                        //getCannedJobDetails()
                        //editJob(cannedid)
                        location.href = 'cannedjobs_new.php?newid=' + cannedid
                        $('#cannedlaborid').val("0")

                    }
                    $('#btn-savelabor').attr('disabled', false)
                    hideLoader();
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                    hideLoader();
                }

            });


        }

        function saveSublet() {

            desc = encodeURIComponent($('#addsubletdesc').val())
            id = $('#cannedsubletid').val()
            cannedid = $('#cannedjobid').val()
            price = $('#addsubletprice').val()
            cost = $('#addsubletcost').val()
            supplier = $('#addsubletsupplier').val()
            taxable = $('#addsublettax').val()

            if (desc.length == 0 || desc == 'null') {
                sbalert("Sublet Description is required");
                return;
            }
            if (cost.length == 0 || !$.isNumeric(price)) {
                sbalert("Sublet cost is required and must be a number");
                return
            }
            if (price.length == 0 || !$.isNumeric(price)) {
                sbalert("Sublet price is required and must be a number");
                return
            }

            $('#btn-savesublet').attr('disabled', 'disabled')
            showLoader();

            ds = "cannedjobid=" + cannedid + "&type=editsublet&desc=" + desc + "&cost=" + cost + "&price=" + price + "&supplier=" + supplier + "&taxable=" + taxable + "&subletid=" + id + "&shopid=<?php echo $shopid; ?>"
            showLoader();
            $.ajax({
                data: ds,
                url: "cannedjobfiles/cannedjob_new.php",
                success: function (r) {
                    if (r == "success") {
                        $('#addsubletmodal').modal('hide')
                        $('#editcannedmodal').css('opacity', '1.0')
                        location.href = 'cannedjobs_new.php?newid=' + cannedid
                        $('#cannedsubletid').val("0")

                    }
                    $('#btn-savesublet').attr('disabled', false)
                    hideLoader();
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                    hideLoader();
                }

            });


        }

        function delLabor() {

            sbconfirm("Are you sure?", "Are you sure you want to delete this labor item", function () {
                showLoader();
                id = $('#cannedlaborid').val()
                cannedid = $('#cannedjobid').val()
                ds = "cannedjobid=" + cannedid + "&type=dellabor&laborid=" + id + "&shopid=<?php echo $shopid; ?>"
                console.log(ds)
                $.ajax({
                    data: ds,
                    url: "cannedjobfiles/cannedjob_new.php",
                    success: function (r) {
                        if (r == "success") {
                            $('#addlabormodal').modal('hide')
                            $('#editcannedmodal').css('opacity', '1.0')
                            //editJob(cannedid)
                            //getCannedJobDetails()
                            location.href = 'cannedjobs_new.php?newid=' + cannedid
                            $('#cannedlaborid').val("0")

                        }
                        hideLoader();
                    }
                });
            });


        }

        function delSublet() {

            sbconfirm("Are you sure?", "Are you sure you want to delete this sublet", function () {
                showLoader();
                id = $('#cannedsubletid').val()
                cannedid = $('#cannedjobid').val()
                ds = "cannedjobid=" + cannedid + "&type=delsublet&subletid=" + id + "&shopid=<?php echo $shopid; ?>"
                console.log(ds)

                $.ajax({
                    data: ds,
                    url: "cannedjobfiles/cannedjob_new.php",
                    success: function (r) {
                        if (r == "success") {
                            $('#addsubletmodal').modal('hide')
                            $('#editcannedmodal').css('opacity', '1.0')
                            location.href = 'cannedjobs_new.php?newid=' + cannedid
                            $('#cannedsubletid').val("0")

                        }
                        hideLoader();
                    }
                });
            });


        }

        function delPart() {

            sbconfirm("Are you sure?", "Are you sure you want to delete this part", function () {
                showLoader();
                id = $('#cannedpartid').val()
                cannedid = $('#cannedjobid').val()
                ds = "cannedjobid=" + cannedid + "&type=delpart&partid=" + id + "&shopid=<?php echo $shopid; ?>"
                console.log(ds)
                $.ajax({
                    data: ds,
                    url: "cannedjobfiles/cannedjob_new.php",
                    success: function (r) {
                        if (r == "success") {
                            $('#addpartmodal').modal('hide')
                            $('#editcannedmodal').css('opacity', '1.0')
                            getCannedJobDetails()
                            $('#cannedpartid').val("0")

                        }
                        hideLoader();
                    }
                });
            });


        }

        $('#addlabormodal').on('hidden.mdb.modal', function () {
            $('#editcannedmodal').modal('show')
        });

        function getCannedJobDetails() {
            showLoader();
            cannedid = $('#cannedjobid').val()
            console.log(cannedid)
            ds = "type=sub&shopid=<?php echo $shopid; ?>&id=" + cannedid
            //console.log("cannedjobfiles/cannedjob.php?type=sub&shopid=<?php echo $shopid; ?>&id="+cannedid)
            $.ajax({

                data: ds,
                url: "cannedjobfiles/cannedjob_new.php",
                success: function (r) {
                    //console.log(r)
                    $('#canneddetails').html(r)
                    hideLoader();
                }
            });

        }
    </script>

    <script type="text/javascript">
    </script>

</body>

</html>