<?php

require CONN;

$shopid = $_GET['shopid'];
$type = $_GET['type'];

if ($type == "newjob") {

    $jobname = filter_var($_GET['cjname'], FILTER_SANITIZE_STRING);
    $jobtax = filter_var($_GET['taxable'], FILTER_SANITIZE_STRING);

    $stmt = "select id from cannedjobs where shopid = '$shopid' order by id desc limit 1";
    if ($query = $conn->prepare($stmt)) {
        if ($query->execute()) {
            $query->bind_result($id);
            $query->fetch();
            $query->close();
        } else {
            echo $conn->errno;
        }
    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }
    $id = $id + 1;

    $stmt = "insert into cannedjobs (jobname,shopid,id,taxable) values ('$jobname','$shopid',$id,'$jobtax')";
    //echo $stmt;
    if ($query = $conn->prepare($stmt)) {
        if ($query->execute()) {
            $conn->commit();
            $query->close();
        } else {
            echo $conn->errno;
        }

    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

    echo "success|" . $id;

} elseif ($type == "dellabor") {
    $laborid = $_GET['laborid'];
    $stmt = "delete from cannedlabor where shopid = '$shopid' and id = $laborid";
    if ($query = $conn->prepare($stmt)) {

        $query->execute();
        $conn->commit();
        $query->close();
        echo "success";

    }


} elseif ($type == "delsublet") {
    $subletid = $_GET['subletid'];
    $stmt = "delete from cannedsublet where shopid = '$shopid' and id = $subletid";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $conn->commit();
        $query->close();
        echo "success";

    }
} elseif ($type == "delpart") {
    $partid = $_GET['partid'];
    $stmt = "delete from cannedparts where shopid = '$shopid' and id = $partid";
    if ($query = $conn->prepare($stmt)) {

        $query->execute();
        $conn->commit();
        $query->close();
        echo "success";

    }


} elseif ($type == "editlabor") {

    $laborid = $_GET['laborid'];
    $labor = filter_var($_GET['labor'], FILTER_SANITIZE_STRING);
    $laborhours = $_GET['hours'];
    $cannedid = $_GET['cannedjobid'];
    $nocalc = $_GET['nocalc'];
    $rateforcalc = (isset($_GET['addcjrate']) && !empty($_GET['addcjrate']) ? $_GET['addcjrate'] : 0);
    if (empty($_GET['addlaborflatprice'])) {
        $fp = 0.00;
    } else {
        $fp = $_GET['addlaborflatprice'];
        $rateforcalc = 0;
    }

    if ($laborid != 0) {
        $stmt = "update cannedlabor set labor = '$labor', laborhours = $laborhours, flatprice = $fp, nocalc = $nocalc, rateforcalc = $rateforcalc where shopid = '$shopid' and id = $laborid";
    } else {
        $stmt = "insert into cannedlabor (labor,laborhours,cannedjobsid,shopid,flatprice,nocalc,rateforcalc) values ('$labor',$laborhours,$cannedid,'$shopid',$fp,'$nocalc','$rateforcalc')";
    }

    //echo $stmt."\r\n";
    if ($query = $conn->prepare($stmt)) {
        if ($query->execute()) {
            $conn->commit();
            $query->close();
            echo "success";
        } else {
            echo $conn->errno;
        }

    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

} elseif ($type == "editsublet") {

    $subletid = $_GET['subletid'];
    $desc = filter_var($_GET['desc'], FILTER_SANITIZE_STRING);
    $cost = $_GET['cost'];
    $price = $_GET['price'];
    $supplier = $_GET['supplier'];
    $taxable = $_GET['taxable'];
    $cannedid = $_GET['cannedjobid'];


    if ($subletid != 0) {
        $stmt = "update cannedsublet set subletdesc = ?, subletprice = ?, subletcost = ?, subletsupplier = ?, taxable = ? where shopid = ? and id = ?";
    } else {
        $stmt = "insert into cannedsublet (subletdesc,subletprice,subletcost,subletsupplier,taxable,shopid,cannedjobsid) values (?,?,?,?,?,?,?)";
    }

    if ($query = $conn->prepare($stmt)) {
        if (!empty($subletid))
            $query->bind_param("sddsssi", $desc, $price, $cost, $supplier, $taxable, $shopid, $subletid);
        else
            $query->bind_param("sddsssi", $desc, $price, $cost, $supplier, $taxable, $shopid, $cannedid);
        if ($query->execute()) {
            $conn->commit();
            $query->close();
            echo "success";
        } else {
            echo $conn->errno;
        }

    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

} elseif ($type == "editpart") {

    $partid = $_GET['partid'];

    $stmt = "select partnumber,partdescription,supplier,partcost,partprice,qty,tax,partcategory,partcode,overridematrix,bin,allocated from cannedparts where shopid = '$shopid' and id = $partid";
    //echo $stmt;
    if ($query = $conn->prepare($stmt)) {

        if ($query->execute()) {
            $query->bind_result($partnumber, $partdescription, $supplier, $partcost, $partprice, $qty, $tax, $partcategory, $partcode, $matrix, $bin, $allocated);
            $query->fetch();
            echo $partnumber . "|" . $partdescription . "|" . $supplier . "|" . $partcost . "|" . $partprice . "|" . $qty . "|" . $tax . "|" . $partcategory . "|" . $partcode . "|" . strtolower($matrix) . "|" . $bin . "|" . $allocated;
            $query->close();

        } else {
            echo $conn->errno;
        }

    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }


} elseif ($type == "deletejob") {

    $cannedid = $_GET['cannedid'];
    $stmt = "delete from cannedjobs where shopid = '$shopid' and id = $cannedid";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $conn->commit();
        //echo "success";
        $query->close();
    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

    $stmt = "delete from cannedlabor where shopid = '$shopid' and cannedjobsid = $cannedid";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $conn->commit();
        $query->close();
    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

    $stmt = "delete from cannedparts where shopid = '$shopid' and cannedjobsid = $cannedid";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $conn->commit();
        $query->close();
    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

    $stmt = "delete from cannedsublet where shopid = '$shopid' and cannedjobsid = $cannedid";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $conn->commit();
        $query->close();
    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

    echo "success";
} elseif ($type == "savepart") {

    // ds = "type=savepart&pn="+pn+"&pd="+pd+"&su="+su+"&cat="+cat+"&pc="+pc+"&pcost="+pcost+"&pp="+pp+"&qty="+qty+"&id="+id+"&shopid="
    $pn = filter_var($_GET['pn'], FILTER_SANITIZE_STRING);
    $pd = filter_var($_GET['pd'], FILTER_SANITIZE_STRING);
    $su = filter_var($_GET['su'], FILTER_SANITIZE_STRING);
    $cat = filter_var($_GET['cat'], FILTER_SANITIZE_STRING);
    $pc = filter_var($_GET['pc'], FILTER_SANITIZE_STRING); // part code
    $pcost = filter_var($_GET['pcost'], FILTER_SANITIZE_STRING);
    $qty = filter_var($_GET['qty'], FILTER_SANITIZE_STRING);
    $id = filter_var($_GET['id'], FILTER_SANITIZE_STRING);
    $pp = filter_var($_GET['pp'], FILTER_SANITIZE_STRING);
    $tax = filter_var($_GET['tax'], FILTER_SANITIZE_STRING);
    $ovrm = filter_var($_GET['matrix'], FILTER_SANITIZE_STRING);
    $bin = filter_var($_GET['bin'], FILTER_SANITIZE_STRING);
    $alloc = filter_var($_GET['alloc'], FILTER_SANITIZE_STRING);
    $cannedid = $_GET['cannedid'];

    /*$ovrm = "no";
    $stmt = "select overridematrix from partsinventory where shopid = ? and partnumber = ?";
    if ($query = $conn->prepare($stmt)){
        $query->bind_param("ss",$shopid,$pn);
        $query->execute();
        $query->store_result();
        $numrows = $query->num_rows();
        if ($numrows > 0){
            $query->free_result();
            $query->execute();
            $query->bind_result($ovrm);
            $query->fetch();
        }else{
            $ovrm = "no";
        }
        $query->close();
    }
    */
    if ($id != 0) {
        $stmt = "update cannedparts set partnumber = ?, partdescription = ?, supplier = ?, partcost = ?, partprice = ?, qty = ?, partcategory = ?, partcode = ?"
            . ",overridematrix = ?, tax = ?, bin = ?, allocated = ? where id = ? and shopid = ?";
    } else {
        $stmt = "insert into cannedparts (partnumber,partdescription,supplier,partcost,partprice,qty,tax,partcategory,partcode,cannedjobsid,shopid,overridematrix,bin,allocated) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    }
    //printf(str_replace("?","%s",$stmt),$pn,$pd,$su,$pcost,$pp,$qty,$cat,$pc,$id,$shopid);

    if ($query = $conn->prepare($stmt)) {

        if ($id != 0) {
            $query->bind_param("sssdddssssssss", $pn, $pd, $su, $pcost, $pp, $qty, $cat, $pc, $ovrm, $tax, $bin, $alloc, $id, $shopid);
        } else {
            $query->bind_param("sssdddsssissss", $pn, $pd, $su, $pcost, $pp, $qty, $tax, $cat, $pc, $cannedid, $shopid, $ovrm, $bin, $alloc);
        }
        $query->execute();
        $conn->commit();
        echo "success";
        $query->close();

    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }


} elseif ($type == "savejob") {

    // "id="+cannedid+"&jobname="+editcjname+"&flatprice="+editcjflat+"&taxable="+editcjtax+"&type=savejob&shopid=
    $jobname = filter_var($_GET['jobname'], FILTER_SANITIZE_STRING);
    $jobntax = filter_var($_GET['taxable'], FILTER_SANITIZE_STRING);
    $jobrate = filter_var($_GET['rate'], FILTER_SANITIZE_STRING);
    $jobratelabel = filter_var($_GET['ratelabel'], FILTER_SANITIZE_STRING);
    $techstory = filter_var($_GET['techstory'], FILTER_SANITIZE_STRING);

    $cannedid = $_GET['id'];

    $stmt = "update cannedjobs set jobname = ?,taxable = ?, hourlyrate=?, ratelabel=? , techstory = ? where shopid =?  and id =? ";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("ssdsssi", $jobname, $jobntax, $jobrate, $jobratelabel, $techstory, $shopid, $cannedid);
        $query->execute();
        $conn->commit();
        echo "success";
        $query->close();
    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }


} elseif ($type == "main") {

    $id = $_GET['id'];
    if ($_GET['fp'] != "0") {
        $stmt = "update cannedjobs set flatprice = 0 where id = ? and shopid = ?";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("is", $id, $shopid);
            $query->execute();
            $conn->commit();
            $query->close();
        }

    }

    $stmt = "select jobname,flatprice,taxable,hourlyrate,ratelabel,techstory from cannedjobs where id = ? and shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("is", $id, $shopid);
        $query->execute();
        $query->bind_result($j, $f, $t, $hr, $rl, $ts);
        $query->fetch();
        $query->close();

    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

    // now get the total of parts
    $stmt = "select coalesce(sum(partprice*qty)) from cannedparts where shopid = ? and cannedjobsid = ?";
    /*$dstmt = "select coalesce(sum(partprice*qty)) from cannedparts where shopid = '$shopid' and cannedjobsid = $id";
    echo $dstmt;*/
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $id);
        $query->execute();
        $query->bind_result($tparts);
        $query->fetch();
        $query->close();
    }

    // now get the total of labor
    $stmt = "select labor,laborhours,rateforcalc,flatprice from cannedlabor where shopid = ? and cannedjobsid = ?";
    $tlabor = 0;
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $id);
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_array()) {

            if (empty($rs['flatprice'])) {
                $tlabor += ($rs['rateforcalc'] * $rs['laborhours']);
            } else {
                $tlabor += $rs['flatprice'];
            }

        }
        $query->close();
    }

    $stmt = "select coalesce(sum(subletprice),0) from cannedsublet where shopid = ? and cannedjobsid = ?";
	if($query = $conn->prepare($stmt)){
		$query->bind_param("si",$shopid,$id);
	    $query->execute();
	    $query->bind_result($tsublet);
	    $query->fetch();
	    $query->close();
	}

	$f = asDollars($tlabor + $tparts + $tsublet);


    echo(json_encode(array("status" => "success", "jobname" => $j, 'price' => $f, 'taxable' => $t, 'rate' => $hr, 'ratelabel' => $rl, 'techstory' => $ts)));
} elseif ($type == "sub") {
    $id = $_GET['id'];

    $stmt = "select upper(taxable) from cannedjobs where id = ? and shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("is", $id, $shopid);
        $query->execute();
        $query->bind_result($taxable);
        $query->fetch();
        $query->close();
    }

    ?>
    <p>
        <b>Parts and Labor on this Canned Job. Click a line to Edit / Delete it.</b>
    </p>
    <div class="">
        <table class="sbdatatable w-100">
            <thead>
            <tr>
                <th style="width:15%">Type</th>
                <th style="width:55%">Item</th>
                <th style="width:10%">Taxable</th>
                <th class="text-right" style="width:10%">Qty/Hrs</th>
                <th class="text-right" style="width:10%">Price</th>

            </tr>
            </thead>
            <tbody>
            <?php

            $stmt = "select labor, laborhours, id, flatprice, nocalc, rateforcalc  from cannedlabor where shopid = ? and cannedjobsid = ?";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("si", $shopid, $id);
                $query->execute();
                $result = $query->get_result();
                while ($row = $result->fetch_array()) {

                    if (empty($row['flatprice'])) {
                        $price = ($row['rateforcalc'] * $row['laborhours']);
                    } else {
                        $price = $row['flatprice'];
                    }
                    ?>
                    <tr onclick="editLabor(<?php echo $row['id']; ?>,'<?php echo str_replace("\r\n", "", str_replace(array("'", "&#39;"), "\'", $row['labor'])); ?>','<?php echo $row['laborhours']; ?>','<?php echo $row['flatprice']; ?>','<?= $row['nocalc'] ?>')">
                        <td>Labor</td>
                        <td><?php echo $row['labor']; ?></td>
                        <td><?= $taxable ?></td>
                        <td class="text-right"><?php echo $row['laborhours']; ?></td>
                        <td class="text-right"><?php echo asDollars($price); ?></td>
                    </tr>
                    <?php
                }
            } else {
                echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }

            $stmt = "select partnumber,partdescription,qty,round(qty*partprice,2) as partprice,id,cannedjobsid,tax from cannedparts where shopid = ? and cannedjobsid = ?";
            //echo $stmt;
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("si", $shopid, $id);
                $query->execute();
                $result = $query->get_result();
                while ($row = $result->fetch_array()) {
                    ?>
                    <tr onclick="editPart(<?php echo $row['id']; ?>,<?php echo $row['cannedjobsid']; ?>)">
                        <td>Part #<?php echo $row['partnumber']; ?></td>
                        <td><?php echo $row['partdescription']; ?></td>
                        <td><?php echo $row['tax']; ?></td>
                        <td class="text-right"><?php echo $row['qty']; ?></td>
                        <td class="text-right"><?php echo asDollars($row['partprice']); ?></td>
                    </tr>
                    <?php
                }
            } else {
                echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }

            $stmt = "select * from cannedsublet where shopid = ? and cannedjobsid = ?";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("si", $shopid, $id);
                $query->execute();
                $result = $query->get_result();
                while ($row = $result->fetch_array()) {
                    ?>
                    <tr onclick="editSublet(<?php echo $row['id']; ?>,'<?php echo str_replace("\r\n", "", str_replace(array("'", "&#39;"), "\'", $row['subletdesc'])); ?>','<?php echo $row['subletcost']; ?>','<?php echo $row['subletprice']; ?>','<?= addslashes($row['subletsupplier']) ?>','<?= $row['taxable'] ?>')">
                        <td>Sublet</td>
                        <td><?php echo $row['subletdesc']; ?></td>
                        <td><?php echo $row['taxable']; ?></td>
                        <td class="text-right"></td>
                        <td class="text-right"><?php echo asDollars($row['subletprice']); ?></td>
                    </tr>
                    <?php
                }
            } else {
                echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }
            ?>
            </tbody>
        </table>
    </div>
    <?php
}
?>
<?php if (isset($conn)) {
    mysqli_close($conn);
} ?>
