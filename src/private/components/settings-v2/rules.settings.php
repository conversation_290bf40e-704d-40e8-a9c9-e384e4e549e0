<?php

require_once CONN;
$shopid = $_COOKIE['shopid'];

$stmt = "select shopmgr,definvmsgemail,userpassword,companyname,companyaddress,companycity,companystate,companyzip,companyphone,companyfax,companyurl,companyemail,barno,epano,defaulttaxrate,defaultlabortaxrate,defaultsublettaxrate"
    . ",defaultwarrmos,defaultwarrmiles,storagefee,hazardouswaste,PSWarrDays,hazwastetaxable,storagetaxable,hst,gst,pst,qst,chargehst,chargepst,chargegst,chargeqst,hstapplyon,pstapplyon,gstapplyon,qstapplyon,SatHours,SunHours,DailyHours,DaysOpen,masterinterface,conamereports,matco,einno "
    . "from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->store_result();
    $num_roid_rows = $query->num_rows;
    if ($num_roid_rows > 0) {
        $query->bind_result($showelapsed, $definvmsgemail, $kioskpassword, $name, $address, $city, $state, $zip, $phone, $fax, $url, $email, $bar, $epa, $parttax, $labortax, $sublettax, $warrmos, $warrmiles, $storagefee, $hazwaste, $pswarr, $hazwastetaxable, $storagetaxable, $hst, $gst, $pst, $qst, $chargehst, $chargepst, $chargegst, $chargeqst, $hstapplyon, $pstapplyon, $gstapplyon, $qstapplyon, $sathrs, $sunhrs, $dailyhrs, $daysopen, $mstrinterface, $conamereports, $matco, $ein);
        $query->fetch();
    } else {
        $showelapsed = '';
        $definvmsgemail = '';
        $name = '';
        $address = '';
        $city = '';
        $state = '';
        $zip = '';
        $phone = '';
        $fax = '';
        $url = '';
        $email = '';
        $bar = '';
        $epa = '';
        $parttax = '';
        $labortax = '';
        $sublettax = '';
        $warrmos = '';
        $warrmiles = '';
        $storagefee = '';
        $hazwaste = '';
        $pswarr = '';
        $sathrs = '';
        $sunhrs = '';
        $dailyhrs = '';
        $daysopen = 0;
        $mstrinterface = '';
        $conamereports = '';
        $ein = '';
    }
    $query->close();
} else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$smsnum = "";
$smstmt = "SELECT smsnum FROM smsnumbers WHERE shopid = ?";
if ($smquery = $conn->prepare($smstmt)) {
    $smquery->bind_param("s", $shopid);
    $smquery->execute();
    $smquery->bind_result($smsnum);
    $smquery->fetch();
    $smquery->close();
}

if (strlen($kioskpassword) == 0) {

    $kioskpassword = generateRandomStr();
    $stmt = "update company set userpassword = '$kioskpassword' where shopid = '$shopid'";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $conn->commit();
        $query->close();
    }
}

$shophours = array();

$stmt = "select * from shophours where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_array()) {
        $shophours[$rs['day']] = array('start' => date('H:i', strtotime($rs['start'])), 'end' => date('H:i', strtotime($rs['end'])));
    }
}

if ($matco == 'yes') {
    $matcokey = '';
    $stmt = "select apikey from apilogin where shopid='$shopid' and companyname='matco'";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->bind_result($matcokey);
        $query->fetch();
        $query->close();
    }
}

if ($_COOKIE['empid'] == "Admin") {
    $reopenro = $ChangePartMatrix = $ChangeSources = $ChangeJobDescription = $ChangePartCodes = $editnotifications = $integrationaccess = "YES";
    $pphAccess = "yes";
    $jobdesc = 'owner';
} else {

    $stmt = "select upper(ReOpenRO),upper(ChangePartMatrix),upper(ChangeSources),upper(ChangeJobDescription),upper(ChangePartCodes),upper(editnotifications),upper(IntegrationAccess),lower(jobdesc), pphAccess from employees where id = ? and shopid = ?";

    if ($query = $conn->prepare($stmt)) {

        $query->bind_param("is", $_COOKIE['empid'], $shopid);
        $query->execute();
        $query->bind_result($reopenro, $ChangePartMatrix, $ChangeSources, $ChangeJobDescription, $ChangePartCodes, $editnotifications, $integrationaccess, $jobdesc, $pphAccess);
        $query->fetch();
        $query->close();

    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }
}

$stmt = "select lower(profitboost), merchantaccount, newpackagetype, datestarted from company where shopid = ?";

if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($isprofitboost, $merchant, $newpackagetype, $datestarted);
    $query->fetch();
    $query->close();
}

$stmt = "select lower(pph) from settings where shopid = ?";

if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($haspph);
    $query->fetch();
    $query->close();
}

$betafeatures = getBetaFeatures();
if(is_array($betafeatures) && in_array('2', $betafeatures))
$oilchangestickers = 'yes';
else
$oilchangestickers = 'no';


/*
 * Get Menu ITEM
 */
$curr_page = basename(parse_url($_SERVER["REQUEST_URI"], PHP_URL_PATH));

$settingsMenu = array(
    'employees' => array('employee-edit.php', 'employees.php'),
    'company' => array('settings.php'),
    'custom_settings' => array('goals.php', 'printingprefs.php', 'oil-change-sticker.php', 'customfields.php', 'disclosures.php', 'roprefs.php', 'misc.php', 'concerncats.php', 'discounts.php', 'auth.net.php'),
    'profitboost_settings' => array("accounttypes.php", "sources.php", "referralsource.php", "profitboost.php")
);

$flag = false;
foreach ($settingsMenu as $menuItem => $pages) {
    foreach ($pages as $page) {
        if ($page == $curr_page) {
            $flag = true;
            break;
        }
    }

    if ($flag) {
        break;
    } else {
        $menuItem = "";
    }
}

$companyImagesPath = UPLOAD_URL . "/" . $_COOKIE['shopid'] . "/";

$stmt = "SELECT showphotos FROM settings WHERE shopid = ?";
$canSeePhotos = 'yes';

if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $_COOKIE['shopid']);
    $query->execute();
    $query->bind_result($canSeePhotos);
    $query->fetch();
    $query->close();
}

if ($canSeePhotos === 'yes') {
    $stmt = "select logo from company where shopid = ? limit 1";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $_COOKIE['shopid']);
        $query->execute();
        $query->store_result();
        $query->bind_result($companyLogo);
        $query->fetch();
        $query->close();
    }

    $stmt = "select photo from employees where id = ? limit 1";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $_COOKIE['empid']);
        $query->execute();
        $query->store_result();
        $query->bind_result($employeePhoto);
        $query->fetch();
        $query->close();
    }
}

?>
