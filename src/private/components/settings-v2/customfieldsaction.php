<?php

require CONN;

$shopid = $_POST['shopid'];
$cust1 = $_POST['cust1'];
$cust2 = $_POST['cust2'];
$cust3 = $_POST['cust3'];
$veh1 = $_POST['veh1'];
$veh2 = $_POST['veh2'];
$veh3 = $_POST['veh3'];
$veh4 = $_POST['veh4'];
$veh5 = $_POST['veh5'];
$veh6 = $_POST['veh6'];
$veh7 = $_POST['veh7'];
$veh8 = $_POST['veh8'];
$year = $_POST['year'];
$make = $_POST['make'];
$model = $_POST['model'];
$engine = $_POST['engine'];
$cylinder = $_POST['cylinder'];
$trans = $_POST['trans'];
$drive = $_POST['drive'];
$vin = $_POST['vin'];
$color = $_POST['color'];
$license = $_POST['license'];
$state = $_POST['state'];
$fleet = $_POST['fleet'];
$currmileage = $_POST['currmileage'];

$stmt = "update company set customuserfield1 = ?, customuserfield2 = ?, customuserfield3 = ?, vehiclefield1label = ?, vehiclefield2label = ?, vehiclefield3label = ?, vehiclefield4label = ?, vehiclefield5label = ?, vehiclefield6label = ?, vehiclefield7label = ?, vehiclefield8label = ? where shopid = ?";


if ($query = $conn->prepare($stmt)){
	$query->bind_param("ssssssssssss",$cust1,$cust2,$cust3,$veh1,$veh2,$veh3,$veh4,$veh5,$veh6,$veh7,$veh8,$shopid);
    if ($query->execute()){
	    $conn->commit();    	
	    echo "success";	
    }else{
		echo $conn->errno;
	}
    $query->close();
    
	

}else{
	echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$stmt = "update vehiclelabels set cylinderlabel = ?, yearlabel = ?, makelabel = ?, modellabel = ?, enginelabel = ?, translabel = ?, licenselabel = ?, statelabel = ?, fleetlabel = ?, currmileagelabel = ?, colorlabel = ?, drivelabel = ?, vinlabel = ? where shopid = ?";


if ($query = $conn->prepare($stmt)){
	$query->bind_param("ssssssssssssss",$cylinder,$year,$make,$model,$engine,$trans,$license,$state,$fleet,$currmileage,$color,$drive,$vin,$shopid);
    if ($query->execute()){
    	$conn->commit();
    	echo "success";
    }else{
		echo $conn->errno;
	}
    $query->close();
    
	

}else{
	echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}




mysqli_close($conn);

?>