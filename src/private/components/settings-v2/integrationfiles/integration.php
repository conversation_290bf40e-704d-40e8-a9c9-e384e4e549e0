<?php

// get the info posted
require CONN;
require_once INTEGRATIONS_PATH. '/html2pdf/autoload.php';

use <PERSON><PERSON>pu\Html2Pdf\Html2Pdf;
use Spipu\Html2Pdf\Exception\Html2PdfException;
use Spipu\Html2Pdf\Exception\ExceptionFormatter;

function get_ip_address()
{
    foreach (array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR') as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip); // just to be safe
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
}

function isValidWebsite($url) {

	global $conn;

	if (filter_var($url, FILTER_VALIDATE_EMAIL)) {
        return false;
    }

    $response = makeCurlRequest($url,[],'GET');

    $httpCode = $response['http_code'];

	return ($httpCode >= 200 && $httpCode < 400);
}
		
if ($_GET['t'] == "rsu"){

	$shopid = $_GET['shopid'];
	$url = $_GET['url'];
	$stmt = "update company set repairstatusurl = ? where shopid = ?";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("ss",$url,$shopid);
		$query->execute();
		$conn->commit();
		$query->close();
		echo "success";
	}

}

if ($_GET['t'] == "epicor"){
	$s = $_GET['epicorsupplier'];
	$sar = explode("|",$s);
	$s = $sar[0];
	$e = $sar[1];
	$a = $_GET['address'];
	$c = $_GET['city'];
	$st = $_GET['state'];
	$z = $_GET['zip'];
	$ac = $_GET['account'];
	$shopid = $_GET['shopid'];

	// get shop info
	$stmt = "select companyname,companyaddress,companycity,companystate,companyzip,companyphone from company where shopid = ?";
	if ($query = $conn->prepare($stmt)){

		$query->bind_param("s",$shopid);
	    $query->execute();
	    $query->store_result();
	    $query->bind_result($name,$address,$city,$state,$zip,$phone);
	    $query->fetch();
	    $query->close();

	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}


	// now send the email

	$mb = "The following account is requesting electronic trading via IAP with their local " . $s . " Auto Parts supplier.  They will be using Shop Boss Pro. Here are the details for the account requesting setup:\r\n";
	$mb .= "Shop Info:\r\n\r\n";
	$mb .= "Shop ID (internal use): " . $shopid . "\r\n";
	$mb .= $name . "\r\n" . $address . "\r\n" . $city . ", " . $state . ". " . $zip . "\r\n" . $phone . "\r\n";
	$mb .= "Vendor Account #" . $ac . "\r\n\r\n";
	$mb .= "Supplier Information:" . "\r\n";
	$mb .= $s . "\r\n" . $a . "\r\n" . $c . ", " . $st . ". " . $z . "\r\n\r\n";
	$mb .= "Chris Boshaw\r\nPresident/CEOShop Boss Pro";
	$mb = strtoupper($mb);

	require(PRIVATE_PATH."/integrations/mandrill/src/Mandrill.php");

	$subject = 'Request Epicor Credentials';
	
	$res = sendEmailMandrill('<EMAIL>',$subject,$mb,'Shop Boss Support','<EMAIL>');

    if(empty($res)) {
	    echo 'Message could not be sent.';
	    echo 'Mailer Error: ' . $mail->ErrorInfo;
	} else {
	    echo 'success';
	}
}

if ($_GET['t'] == "nexpart"){

	$npu = $_GET['nexpartusername'];
	$npp = $_GET['nexpartpassword'];
	$nps = $_GET['nexpartsupplier'];
	$shopid = $_GET['shopid'];

	// get shop info
	$stmt = "insert into nexpart (`desc`, username, password, shopid) values (?,?,?,?)";
	if ($query = $conn->prepare($stmt)){

		$query->bind_param("ssss",$nps,$npu,$npp,$shopid);
	    $query->execute();
	    $insid = $conn->insert_id;
	    $query->store_result();
	    $conn->commit();
	    $query->close();
	    echo $insid;

	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

}

elseif ($_GET['t'] == "nexpartdelete"){

	$nexid = $_GET['nexid'];
	$shopid = $_GET['shopid'];

	$stmt = "delete from nexpart where id=? and shopid=?";
	if ($query = $conn->prepare($stmt)){

		$query->bind_param("ss",$nexid,$shopid);
	    $query->execute();
	    $conn->commit();
	    $query->close();
	    echo "success";

	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

}

if ($_GET['t'] == "worldpac"){

	$shopid = $_GET['shopid'];
	$name = "WorldPac Integrated Parts Ordering";
	$price = "9.95";

	$stmt = "insert into companyadds (`name`, price, shopid) values (?,?,?)";
	if ($query = $conn->prepare($stmt)){

		$query->bind_param("sss",$name,$price,$shopid);
	    $query->execute();
	    $query->store_result();
	    $conn->commit();
	    $query->close();
	    echo "success";

	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}


}

if ($_GET['t'] == "carfax"){

	// service history check registration
	//customerid=&carfaxcontact=contact+name&carfaxemail=contact+email&shopid=demo&t=carfax
	$shopid = $_GET['shopid'];
	$contact = $_GET['carfaxcontact'];
	$email = $_GET['carfaxemail'];

	// get the shop info
	$stmt = "select companyname,companyaddress,companycity,companystate,companyzip,companyphone,companyurl from company where shopid = ?";
	if ($query = $conn->prepare($stmt)){

		$query->bind_param("s",$shopid);
	    $query->execute();
	    $query->store_result();
	    $num_roid_rows = $query->num_rows;
	    if ($num_roid_rows > 0){
		    $query->bind_result($n,$a,$c,$s,$z,$p,$u);
		    $query->fetch();

			$xml = "<carfax-request>"  //  This is for QuickVIN
			. "<partner>"
			. "<management-system>Shop Boss Pro</management-system>"
			. "<product-data-id>99ADB006A9534BCD</product-data-id>"
			. "<comp-code>OG0Q8L3001</comp-code>"
			. "</partner>"
			. "<location>"
			. "<location-id>" . $shopid . "</location-id>"
			. "<name><![CDATA[" . $n . "]]></name>"
            . "<address><![CDATA[" . $a . "]]></address>"
			. "<city>" . $c . "</city>"
			. "<state>" . $s . "</state>"
			. "<zip>" . $z . "</zip>"
			. "<phone>" . $p . "</phone>"
			. "<url>" . $u . "</url>"
			. "<business-contact>" . $contact . "</business-contact>"
			. "<technical-contact>" . $contact . "</technical-contact>"
			. "<email>" . $email . "</email>"
			. "</location>"
			. "</carfax-request>";
			//echo $cfr;
			$url = "https://quickvin.carfax.com/authorizer/1/";

			$stream_options = array(
			    'http' => array(
			       'method'  => 'POST',
			       'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
			       'content' => $xml,
			    ),
			);

			$context  = stream_context_create($stream_options);
			$response = file_get_contents($url, null, $context);
			$arrResult = simplexml_load_string($response, 'SimpleXMLElement', LIBXML_NOCDATA);

			if (null !== $arrResult->xpath('//carfax-response/success')){
				$qvmsg = "success";
			}elseif (null !== $arrResult->xpath('//carfax-response/error/message')){
				$qvmsg = $arrResult->xpath('//carfax-response/error/message');
			}

			/*
			sample success response
			<carfax-response>
			  <success>Access granted. Please allow up to 3 hours for request to be processed.</success>
			</carfax-response>

			sample error code
			<carfax-response>
			  <error>
			    <code>308</code>
			    <message>Duplicate activation request.</message>
			  </error>
			</carfax-response>
			*/

			//echo $response;

			$cfr = "<carfax-request>"     //  Service History setup request
			. "<partner>"
			. "<management-system>Shop Boss Pro</management-system>"
			. "<product-data-id>520FEBC80AB9F9EA</product-data-id>"
			. "<comp-code>OG0Q8L3001</comp-code>"
			. "</partner>"
			. "<location>"
			. "<location-id>" . $shopid . "</location-id>"
			. "<name><![CDATA[" . $n . "]]></name>"
            . "<address><![CDATA[" . $a . "]]></address>"
			. "<city>" . $c . "</city>"
			. "<state>" . $s . "</state>"
			. "<zip>" . $z . "</zip>"
			. "<phone>" . $p . "</phone>"
			. "<url>" . $u . "</url>"
			. "<business-contact>" . $contact . "</business-contact>"
			. "<technical-contact>" . $contact . "</technical-contact>"
			. "<email>" . $email . "</email>"
			. "</location>"
			. "</carfax-request>";

			$url = "https://quickvin.carfax.com/authorizer/1/";

			$stream_options = array(
			    'http' => array(
			       'method'  => 'POST',
			       'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
			       'content' => $cfr,
			    ),
			);

			$context  = stream_context_create($stream_options);
			$response = file_get_contents($url, null, $context);
			$arrResult = simplexml_load_string($response, 'SimpleXMLElement', LIBXML_NOCDATA);

			if (null !== $arrResult->xpath('//carfax-response/success')){
				$shmsg = "success";
			}elseif (null !== $arrResult->xpath('//carfax-response/error/message')){
				$shmsg = $arrResult->xpath('//carfax-response/error/message');
			}

			echo $qvmsg."|".$shmsg;
			/*
			sample success response
			<carfax-response>
			  <success>Access granted. Please allow up to 3 hours for request to be processed.</success>
			</carfax-response>
			*/

			//update the carfaxlocation id number
			$ustmt = "update company set carfaxlocation = shopid where shopid = '$shopid'";
			if ($uquery = $conn->prepare($ustmt)){
				$uquery->execute();
				$conn->commit();
				$uquery->close();
			}


	    }else{
	    	echo "shop not found|error";
	    }
	    $query->close();


	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}



}

if ($_GET['t'] == "prodemand")
{
	$shopid = $_GET['shopid'];
	$username = trim($_GET['username']);
	$password = trim($_GET['password']);
	$type = $_GET['type'];

	$stmt = "select * from apilogin where shopid = ? and companyname='prodemand'";
	if ($query = $conn->prepare($stmt))
	{
		$query->bind_param("s",$shopid);
	    $query->execute();
	    $query->store_result();
	    $num_roid_rows = $query->num_rows;
	    if ($num_roid_rows > 0)
	    {
	    	if(empty($username) && empty($password))
	    	{
	    	   $stmt = "delete from apilogin where shopid = ? and companyname='prodemand'";
	           if ($query = $conn->prepare($stmt))
	           {
	             $query->bind_param("s",$shopid);
	             $query->execute();
	             $conn->commit();
	             $query->close();
	           }
	    	}
	    	else
	    	{
	    	   $stmt = "update apilogin set username=?,password=?,apikey=? where shopid = ? and companyname='prodemand'";
	    	   if ($query = $conn->prepare($stmt))
	           {
	             $query->bind_param("ssss",$username,$password,$type,$shopid);
	             $query->execute();
	             $conn->commit();
	             $query->close();
	           }
	    	}
	    }
	    else
	    {
	     
	     $stmt = "insert into apilogin (`companyname`,`apikey`,`username`,`password`, shopid) values ('prodemand',?,?,?,?)";
	     if ($query = $conn->prepare($stmt))
	     {
		  $query->bind_param("ssss",$type,$username,$password,$shopid);
	      $query->execute();
	      $conn->commit();
	      $query->close();
	     }
	     
        }
    }
}
if($_GET['t'] == "add_repairlink"){
	$uid = $_GET['uid'];
	$pwd = $_GET['pwd'];
	$shopid = $_GET['shopid'];

	include_once INTEGRATIONS_PATH."/repairlink/repair_link.php";

	$rl = new repair_link();

	$res = $rl->get_access_token($uid);

	if(!empty($res) && !empty($res->access_token)) {
		$ret = $rl->add_creds($uid, $pwd, $shopid);
	} else {
		$ret = "<strong>Invalid RepairLink Credentials</strong><br />Please verify if the Username is correct and registered with Repairlink";
	}

	if($ret === true) {
		echo "success";
	} else if ($ret === false){
		echo "Something went wrong, Please try again later";
	} else {
		echo $ret;
	}
}
if($_GET['t'] == "del_repairlink"){
	$id = $_GET['rlid'];

	include_once INTEGRATIONS_PATH."/repairlink/repair_link.php";

	$rl = new repair_link();

	echo $rl->del_creds($id) ? "success" : "false";
}
if($_POST['t'] == "generate_autoops"){

$shopid = $_POST['shopid'];
$htmlStr = $_POST['content'];
$stmt = "select id from personal_access_tokens where tokenable_id = ? and name = 'AutoOps'";
if ($query = $conn->prepare($stmt))
{
	$query->bind_param("s",$shopid);
    $query->execute();
    $query->store_result();
    $num_roid_rows = $query->num_rows;
    if ($num_roid_rows < 1)
    {
    	$newFileName = $shopid.'_'.uniqid() . ".pdf";
		try {
		    $html2pdf = new Html2Pdf('P', 'A4', 'en', true, 'UTF-8', 10);
		    $html2pdf->writeHTML($htmlStr);
		    ob_end_clean();
		    $filePath = PUBLIC_PATH."/assets/authsigs/autoops/".$newFileName;
            $pdfContent = $html2pdf->output('', 'S');
            file_put_contents($filePath, $pdfContent);
		} catch (Html2PdfException $e) {
		    $html2pdf->clean();
		    $formatter = new ExceptionFormatter($e);
		    echo $formatter->getHtmlMessage();
		    die();
		}

		
		$data = array('shop_id' => $shopid, 'role' => 'essentials', 'name'=>'Auto_Ops');
		$jsonEncodedData = json_encode($data);
		$curl = curl_init();
		$opts = array(
		    CURLOPT_URL             => 'https://'.(stripos($_SERVER['HTTP_HOST'], 'staging') !== false?'dev':'api').'.shopboss.io/api/v1/create-token-for-shop',
		    CURLOPT_RETURNTRANSFER  => true,
		    CURLOPT_CUSTOMREQUEST   => 'POST',
		    CURLOPT_POST            => 1,
		    CURLOPT_POSTFIELDS      => $jsonEncodedData,
		    CURLOPT_HTTPHEADER  => array('Content-Type: application/json','Content-Length: ' . strlen($jsonEncodedData))
		);
		curl_setopt_array($curl, $opts);
		$result = curl_exec($curl);
		$json = json_decode($result);
		curl_close($curl);

		if(isset($json->token))
		{
			$tokenarr = explode('|',$json->token);
			$token = $tokenarr[1];

			include_once(PRIVATE_PATH."/integrations/mandrill/src/Mandrill.php");

			$stmt = "select companyname,companyemail from company where shopid = ?";
			if ($query = $conn->prepare($stmt)){

				$query->bind_param("s",$shopid);
			    $query->execute();
			    $query->store_result();
			    $query->bind_result($company,$email);
			    $query->fetch();
			    $query->close();
			}

			$message = 'Hello '.$company.',<br><br>A request to provide API access to your ShopBoss Account information has been initiated via the ShopBoss Integration page.<br><br>As a reminder, here is the information they will be provided:<br><br><b>Scope of Access:</b><br><ul><li>AutoOps will have permission to create, edit, and delete appointments, customers, and vehicles within your account.</li></ul><br><b>Data Privacy and Security:</b><br><ul><li>ShopBoss employs industry-standard security measures to protect your data. AutoOps will adhere to these standards to ensure the confidentiality and integrity of your information.</li></ul><br><b>Duration of Access:</b><br><ul><li>This authorization remains valid until you choose to revoke it.</li></ul><br><b>User Responsibility:</b><br><ul><li>You are responsible for monitoring the activities performed by AutoOps with the granted access.</li></ul><br><b>Revocation and Support:</b><br><ul><li>To remove access or seek support, please open a support ticket with <NAME_EMAIL>. If you did not request this access, please contact ShopBoss support as soon as possible.</li></ul><br><b>Next Steps:</b><br><ul><li>AutoOps will be in touch with you regarding the next steps.</li></ul><br>Thank you for being a valued customer,<br><br>Shop Boss';

			sendEmailMandrill($email,"AutoOps API Key Generated for Access to Shop Boss",$message,"Shop Boss Pro","<EMAIL>");

			sendEmailMandrill("<EMAIL>;<EMAIL>;<EMAIL>","AutoOPs API Key Generated ".$shopid." ".$company,$token,"Shop Boss Pro","<EMAIL>");

			if($shopid != '6062')
			sendEmailMandrill("<EMAIL>","Shop Boss API Key Generated ".$shopid." ".$company,$token,"Shop Boss Pro","<EMAIL>");

			echo("success");
		}
	}
}

}

if($_POST['t'] == "staxenroll"){

$shopid = $_POST['shopid'];
$company = $_POST['staxcompanyname'];
$first_name = filter_var($_POST['staxuserfirstname'], FILTER_SANITIZE_STRING);
$last_name = filter_var($_POST['staxuserlastname'], FILTER_SANITIZE_STRING);
$email = filter_var($_POST['staxuseremail'], FILTER_SANITIZE_STRING);
$plan = filter_var($_POST['staxplan'], FILTER_SANITIZE_STRING);

$stmt = "select veloxmerchantid from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->store_result();
    $query->bind_result($veloxmerchantid);
    $query->fetch();
    $query->close();
}

if(!empty($veloxmerchantid))
{
  echo json_encode(array("message"=>"MerchantId already exists. Please reload the page"));
  die();
}

$url = STAXHOST."/payments/enroll";

$data = array(
"providerType" => "stax",
"pricingPlan" => $plan,
"company" => array("name" => $company),
"user" => array("firstName" => $first_name,"lastName" => $last_name,"email" => $email)
);
 
$jsonData = json_encode($data);

$headers = array(
       "Accept: application/json",
       "Authorization: Bearer ".STAXBEARERTOKEN,
       "Content-Type: application/json"
    );

$response = makeCurlRequest($url,$headers,'POST',$jsonData);

$json = json_decode($response['response']);

$message = $json->message ?? '';

if($response['http_code'] == '200')
{
	$merchantId = $json->vehloMerchantId;
	$redirectURL = $json->redirectUrl;

	$stmt = "update company set veloxmerchantid = ? where shopid = ?";
    if ($query = $conn->prepare($stmt))
    {
     $query->bind_param("ss",$merchantId,$shopid);
     $query->execute();
     $conn->commit();
     $query->close();
    }

	echo json_encode(array("status"=>"success",'redirectURL'=>$redirectURL));
}
else
echo json_encode(array("message"=>$message));
}


if($_POST['t'] == "crintegration")
{

  $ip = get_ip_address();
  $shopid = $_POST['shopid'];
  $companyname = $_POST['cr_companyname'];
  $dba = $_POST['cr_dbaname'];
  $legaltype = $_POST['cr_legaltype'];
  $ein = $_POST['cr_ein'];
  $countryein = $_POST['cr_eincountry'];
  $cellphone = $_POST['cr_phone'];
  $address = $_POST['cr_address'];
  $city = $_POST['cr_city'];
  $state = $_POST['cr_state'];
  $zip = $_POST['cr_zip'];
  $country = $_POST['cr_country'];
  $stocksymbol = $_POST['cr_stocksymbol'];
  $stockexchange = $_POST['cr_stockexchange'];
  $website = $_POST['cr_website'];
  $verticaltype = $_POST['cr_verticaltype'];
  $firstname = $_POST['cr_fname'];
  $lastname = $_POST['cr_lname'];
  $companyemail = $_POST['cr_email'];
  $refid = $_POST['cr_refid'];
  $updatebrandid = $_POST['cr_brandid'];

  if (!isValidWebsite($website))
  {
  	echo(json_encode(array('status'=>'failed','message'=>"Invalid Website")));
  	die();
  }

  $data = array(
  "entityType" => $legaltype,
  "firstName"=> $firstname,
  "lastName" => $lastname,
  "mobilePhone"=> $cellphone,
  "displayName" => $dba,
  "companyName" => $companyname,
  "ein" => $ein,
  "einIssuingCountry"=> $countryein,
  "phone"=> $cellphone,
  "street"=> $address,
  "city"=> $city,
  "state"=> $state,
  "postalCode"=> $zip,
  "country"=> $country,
  "email"=> $companyemail,
  "stockSymbol"=> $stocksymbol,
  "stockExchange"=> $stockexchange,
  "ipAddress"=> $ip,
  "website"=> $website,
  "brandRelationship"=> "BASIC_ACCOUNT",
  "vertical"=> $verticaltype,
  "altBusinessId"=> "",
  "altBusinessIdType"=> "",
  "referenceId"=> $refid,
  "mock"=> false,
  "tag"=> [],
  "businessContactEmail"=> $companyemail,
  "altBusinessIdType" => "NONE"
  );

  $encdata = json_encode($data);

  if(stripos($sn, 'staging') !== false) //sandbox
  $host = "https://csp-api-staging.campaignregistry.com";
  else
  $host = "https://csp-api.campaignregistry.com";

  if(empty($updatebrandid))
  $url = $host."/v2/brand/nonBlocking";
  else
  $url = $host."/v2/brand/".$updatebrandid;

  $smsurl = $host."/v2/brand/BRANDID/smsOtp";
  
  $authToken = base64_encode(CRUSERNAME.":".CRPASSWORD);

  $headers = array(
	   "Accept: application/json",
	   "Authorization: Basic $authToken",
	   "Content-Type: application/json"
	);
  

  $response = makeCurlRequest($url,$headers,(empty($updatebrandid)?"POST":"PUT"),$encdata);

  $json = json_decode($response['response'],true);

  $stmt = "insert into tcrlog (`rec`,`send`,shopid) values (?,?,?)";
  if ($query = $conn->prepare($stmt)){
	    $query->bind_param("sss",$response['response'],$encdata,$shopid);
	    $query->execute();
	    $conn->commit();
	    $query->close();
   } 
  

  $http_code = $response['http_code'];

  $errordesc = '';

  if($http_code == '200')
  {
  	if(empty($updatebrandid))
  	{
	  	$brandid = $json['brandId'];

	    $stmt = "insert into campaignregistry (shopid,status,brandid,companyname,dba,legaltype,ein,countryein,address,city,state,zip,country,website,stocksymbol,stockexchange,verticaltype,refid,firstname,lastname,cellphone,email) values (?,'PENDING',?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
	    if ($query = $conn->prepare($stmt)){
		    $query->bind_param("sssssssssssssssssssss",$shopid,$brandid,$companyname,$dba,$legaltype,$ein,$countryein,$address,$city,$state,$zip,$country,$website,$stocksymbol,$stockexchange,$verticaltype,$refid,$firstname,$lastname,$cellphone,$companyemail);
		    $query->execute();
		    $conn->commit();
		    $query->close();
	    } 

	    $stmt = "insert into tcrstatuses (shopid,status) values (?,'PENDING')";
	    if ($query = $conn->prepare($stmt)){
		    $query->bind_param("s",$shopid);
		    $query->execute();
		    $conn->commit();
		    $query->close();
	     }

	    if($legaltype == 'SOLE_PROPRIETOR')
        {
            $data = array(
              "pinSms" => "Your verification code is @OTP_PIN@",
              "successSms"=> "Your verification code has been confirmed successfully"
              );

            $encdata = json_encode($data);
            $url = str_ireplace('BRANDID', $brandid, $smsurl);
            $response = makeCurlRequest($url,$headers,"POST",$encdata);

            $json = json_decode($response['response'],true);

			$stmt = "insert into tcrlog (`rec`,`send`,shopid) values (?,?,?)";
			if ($query = $conn->prepare($stmt)){
			    $query->bind_param("sss",$response['response'],$encdata,$shopid);
				$query->execute();
				$conn->commit();
				$query->close();
			} 
        }
	}
	else
	{
		$stmt = "update campaignregistry set status = 'PENDING', companyname = ?,dba = ?,legaltype = ?,ein = ?,countryein = ?,address = ?,city = ?,state = ?,zip = ?,country = ?,website = ?,stocksymbol = ?,stockexchange = ?,verticaltype = ?,refid = ?,firstname = ?,lastname = ?,cellphone = ?,email = ?, errors = '' where brandid = ?";
        if ($query = $conn->prepare($stmt)){
            $query->bind_param("ssssssssssssssssssss",$companyname,$dba,$legaltype,$ein,$countryein,$address,$city,$state,$zip,$country,$website,$stocksymbol,$stockexchange,$verticaltype,$refid,$firstname,$lastname,$cellphone,$companyemail,$updatebrandid);
            $query->execute();
            $conn->commit();
            $query->close();
        } 

		$stmt = "select campaignid from campaignregistry where brandid = ?";
		if ($query = $conn->prepare($stmt)) {
		    $query->bind_param("s", $updatebrandid);
		    $query->execute();
		    $query->store_result();
		    $query->bind_result($campaignid);
		    $query->fetch();
		    $query->close();
		}

		if(empty($campaignid))
		{
			$url = $url.'/revet';

			$response = makeCurlRequest($url,$headers,"PUT");

			$json = json_decode($response['response'],true);

			$http_code = $response['http_code'];

		    $stmt = "insert into tcrlog (`rec`,`send`,shopid) values (?,?,?)";
		    if ($query = $conn->prepare($stmt)){
			    $query->bind_param("sss",$response['response'],$url,$http_code);
			    $query->execute();
			    $conn->commit();
			    $query->close();
		     } 

		    $stmt = "insert into tcrstatuses (shopid,status) values (?,'PENDING')";
		    if ($query = $conn->prepare($stmt)){
			    $query->bind_param("s",$shopid);
			    $query->execute();
			    $conn->commit();
			    $query->close();
		    } 

		    if($http_code != '200')
	        {
				$error = $json[0]['description'] ?? '';
				if(!empty($error))
				{
					$stmt = "update campaignregistry set status = 'UNVERIFIED', errors = ? where brandid = ?";
			        if ($query = $conn->prepare($stmt)){
			            $query->bind_param("ss",$error,$updatebrandid);
			            $query->execute();
			            $conn->commit();
			            $query->close();
			        } 

			        $stmt = "insert into tcrstatuses (shopid,status) values (?,'UNVERIFIED')";
				    if ($query = $conn->prepare($stmt)){
					    $query->bind_param("s",$shopid);
					    $query->execute();
					    $conn->commit();
					    $query->close();
				     }
				}
	        }
	    }
	    else
	    {
          $url = $host."/v2/campaign/".$campaignid;
          makeCurlRequest($url,$headers,"DELETE");

          //create new campaign 
          $url = "https://$sn/src/public/endpoints/tcr/webhook.php";
          $data = array(
			  "eventType" => "BRAND_IDENTITY_STATUS_UPDATE",
			  "brandIdentityStatus" => "VERIFIED",
			  "brandId"=> $updatebrandid
		  );

	      $encdata = json_encode($data);
          makeCurlRequest($url,[],"POST",$encdata);
	    }
	}
  	echo(json_encode(array('status'=>'success')));
  }
  elseif($http_code == '400')
  {
  	if(!empty($json))
  	{
  		foreach($json as $arr)
        $errordesc .= implode(', ', $arr['fields']).' - '.$arr['description']."<br>";
  	}

  	echo(json_encode(array('status'=>'failed','message'=>$errordesc)));
  }
  else
  echo(json_encode(array('status'=>'failed','message'=>$json['message'])));
}

if($_POST['t'] == "verify_otp")
{

  $shopid = filter_var($_POST['shopid'], FILTER_SANITIZE_STRING);;
  $otp = filter_var($_POST['otp'], FILTER_SANITIZE_STRING);

  $stmt = "select brandid from campaignregistry where shopid = ?";
  if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->store_result();
    $query->bind_result($brandid);
    $query->fetch();
    $query->close();
   }

   $data = array(
    "otpPin" => $otp
  );

  $encdata = json_encode($data);

  if(stripos($sn, 'staging') !== false) //sandbox
  {
    $smsurl = "https://csp-api-staging.campaignregistry.com/v2/brand/$brandid/smsOtp";
  }
  else //production
  {
    $smsurl = "https://csp-api.campaignregistry.com/v2/brand/$brandid/smsOtp";
  }

  $authToken = base64_encode(CRUSERNAME.":".CRPASSWORD);

  $headers = array(
	   "Accept: application/json",
	   "Authorization: Basic $authToken",
	   "Content-Type: application/json"
	);
  

  $response = makeCurlRequest($smsurl,$headers,"PUT",$encdata);

  $json = json_decode($response['response'],true);

  $stmt = "insert into tcrlog (`rec`,`send`,shopid) values (?,?,?)";
  if ($query = $conn->prepare($stmt)){
	    $query->bind_param("sss",$response['response'],$encdata,$shopid);
	    $query->execute();
	    $conn->commit();
	    $query->close();
   } 
  

  $http_code = $response['http_code'];

  $errordesc = '';

  if($http_code == '204')
  {
  	$stmt = "update campaignregistry set phoneverified = 'yes' where brandid = ?";
    if ($query = $conn->prepare($stmt)){
        $query->bind_param("s",$brandid);
        $query->execute();
        $conn->commit();
        $query->close();
    } 
  	echo(json_encode(array('status'=>'success')));
  }
  else
  echo(json_encode(array('status'=>'failed','message'=>$json[0]['description'])));
}

if($_POST['t'] == "resend_otp")
{

  $shopid = filter_var($_POST['shopid'], FILTER_SANITIZE_STRING);;

  $stmt = "select brandid from campaignregistry where shopid = ?";
  if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->store_result();
    $query->bind_result($brandid);
    $query->fetch();
    $query->close();
   }

   $data = array(
      "pinSms" => "Your verification code is @OTP_PIN@",
      "successSms"=> "Your verification code has been confirmed successfully"
      );

  if(stripos($sn, 'staging') !== false) //sandbox
  {
    $smsurl = "https://csp-api-staging.campaignregistry.com/v2/brand/$brandid/smsOtp";
  }
  else //production
  {
    $smsurl = "https://csp-api.campaignregistry.com/v2/brand/$brandid/smsOtp";
  }

  $encdata = json_encode($data);

  $authToken = base64_encode(CRUSERNAME.":".CRPASSWORD);

  $headers = array(
	   "Accept: application/json",
	   "Authorization: Basic $authToken",
	   "Content-Type: application/json"
	);
  
  $response = makeCurlRequest($smsurl,$headers,"POST",$encdata);

  $json = json_decode($response['response'],true);

  $stmt = "insert into tcrlog (`rec`,`send`,shopid) values (?,?,?)";
  if ($query = $conn->prepare($stmt)){
	    $query->bind_param("sss",$response['response'],$encdata,$shopid);
	    $query->execute();
	    $conn->commit();
	    $query->close();
   } 
  

  $http_code = $response['http_code'];

  $errordesc = '';

  if($http_code == '200')
  {
  	echo(json_encode(array('status'=>'success')));
  }
  else
  echo(json_encode(array('status'=>'failed','message'=>$json[0]['description'])));
}


