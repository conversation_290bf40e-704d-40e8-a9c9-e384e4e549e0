<?php

require(CONN);

$t = $_POST['t'];
if ($t == "add"){

	$shopid = filter_var($_POST['shopid'],FILTER_SANITIZE_STRING);
	$label = filter_var($_POST['label'],FILTER_SANITIZE_STRING);
	$terminalid = filter_var($_POST['terminalid'],FILTER_SANITIZE_STRING);
	$contact = strtoupper($_COOKIE['usr']);
	$reseller = "shopboss";
	
	$stmt = "select companyname,companyaddress,companycity,companystate,companyzip,companyphone,companyemail,cfpid from company where shopid = ?";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("s",$shopid);
		$query->execute();
		$query->bind_result($companyname,$companyaddress,$companycity,$companystate,$companyzip,$companyphone,$companyemail,$cfpid);
		$query->fetch();
		$query->close();
	}	

	if(empty($cfpid))
	{
		echo("Merchant id is empty. Please go to 360payment devices page and populate it");
		exit;
	}
	
	// first, register the terminal
	$url = "https://us-central1-payments360-214018.cloudfunctions.net/velox/pairTerminal?360MerchantId=".$cfpid."&terminalId=".$terminalid."&DeviceLabel=".urlencode($label)."&ResellerName=shopboss";
	
	$ch = curl_init();
	
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	
	$result = curl_exec($ch);

	curl_close ($ch);
	
	// add the terminal to the db
	$json = json_decode($result);
	
	$status = $json->Status;
	$err = $json->ErrorMessage;
	
	if ($status == "Success"){

		$sn = $json->SerialNumber;
	
		$stmt = "insert into 360devices (shopid,terminallabel,terminalid,terminalsn)"
		. " values (?,?,?,?)";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("ssss",$shopid,$label,$terminalid,$sn);
			$query->execute();
			$conn->commit();
			$query->close();
		}

		echo("success");
	
	}else{
	
		echo $err;
		
	}
	
}elseif ($t == "delete"){


	$id = $_POST['id'];
	$shopid = $_COOKIE['shopid'];
	
	$stmt = "select terminalid from 360devices where shopid = ? and id = ?";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("si",$shopid,$id);
		$query->execute();
		$query->bind_result($terminalid);
		$query->fetch();
		$query->close();
	}

	$stmt = "select cfpid from company where shopid = ?";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("s",$shopid);
		$query->execute();
		$query->bind_result($cfpid);
		$query->fetch();
		$query->close();
	}
	
	$url = "https://us-central1-payments360-214018.cloudfunctions.net/velox/unPairTerminal?terminalId=$terminalid&360MerchantId=$cfpid";
	
	$ch = curl_init();
	
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

	$result = curl_exec($ch);
	curl_close ($ch);

	$json = json_decode($result);
	
	$status = $json->Status;
	$err = $json->ErrorMessage;

	$stmt = "delete from 360devices where shopid = ? and id = ?";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("si",$shopid,$id);
		$query->execute();
		$conn->commit();
		$query->close();
	}
	
	if ($status == "Success")
    echo("success");
	else
	echo $err;

}elseif ($t == "getpassword"){

	$shopid = $_POST['shopid'];
	$pw = "";
	$stmt = "select password from 360password where shopid = ?";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("s",$shopid);
		$query->execute();
		$query->bind_result($pw);
		$query->fetch();
		$query->close();
	}
	echo $pw;

}

elseif ($t == "savecfp"){

	$shopid = $_POST['shopid'];
	$cfpid = $_POST['cfpid'];

	$stmt = "update company set cfpid = ? where shopid = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("ss",$cfpid,$shopid);
			$query->execute();
			$conn->commit();
			$query->close();
		}

}

elseif ($t == "savepassword"){

	$shopid = $_POST['shopid'];
	$pw = $_POST['pw'];
	
	$stmt = "select count(*) c from 360password where shopid = ?";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("s",$shopid);
		$query->execute();
		$query->bind_result($numrows);
		$query->fetch();
		//echo $numrows."\r\n";
		if ($numrows >= 1){
			$action = "update";
		}else{
			$action = "add";
		}
		$query->close();
	}
	
	echo $action;
	
	if ($action == "add"){
		$stmt = "insert into 360password (shopid,password) values (?,?)";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("ss",$shopid,$pw);
			$query->execute();
			$conn->commit();
			$query->close();
		}
	}elseif ($action == "update"){
		$stmt = "update 360password set password = ? where shopid = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("ss",$pw,$shopid);
			$query->execute();
			$conn->commit();
			$query->close();
		}
	}

}

elseif ($t == "sigchange"){

 $require = $_POST['val'];
 $cfpid = $_POST['cfpid'];
 $termid = $_POST['termid'];

 $ch = curl_init();
 curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
 $url = "https://us-central1-payments360-214018.cloudfunctions.net/velox/setTerminalConfiguration?360MerchantId=".$cfpid."&terminalId=".$termid."&optionName=PaxGetSignature&optionValue=".$require;
 curl_setopt($ch, CURLOPT_URL, $url);
 curl_exec($ch);
 curl_close($ch);

}

mysqli_close($conn);

