<?php

require(CONN);

$t = $_POST['t'];

if($t=='save')
{
 $shopid = $_POST['shopid'];
 $veloxmerchantid = $_POST['veloxmerchantid'];
 $terminalid = $_POST['terminalid'];
 $label = $_POST['label'];

 $url = STAXHOST."/payments/$veloxmerchantid/terminals/$terminalid";
 $headers = array(
	   "Accept: application/json",
	   "Authorization: Bearer ".STAXBEARERTOKEN,
	   "Content-Type: application/json"
	);
 $data = array(
    "displayName" => $label
    );

 $response = makeCurlRequest($url,$headers,'PUT',json_encode($data));

 if($response['http_code'] == '200')echo("success");
 else
 {
 	 $json = json_decode($response['response'],true);
 	 echo($json['message']);
 }

}

elseif ($t == "getpassword"){

	$shopid = $_POST['shopid'];
	$pw = "";
	$stmt = "select password from staxpassword where shopid = ?";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("s",$shopid);
		$query->execute();
		$query->bind_result($pw);
		$query->fetch();
		$query->close();
	}
	echo $pw;

}

elseif ($t == "savemerchantId"){

	$shopid = $_POST['shopid'];
	$id = $_POST['id'];

	$stmt = "update company set veloxmerchantid = ? where shopid = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("ss",$id,$shopid);
			$query->execute();
			$conn->commit();
			$query->close();
		}

}

elseif ($t == "savepassword"){

	$shopid = $_POST['shopid'];
	$pw = $_POST['pw'];
	
	$stmt = "select count(*) c from staxpassword where shopid = ?";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("s",$shopid);
		$query->execute();
		$query->bind_result($numrows);
		$query->fetch();
		//echo $numrows."\r\n";
		if ($numrows >= 1){
			$action = "update";
		}else{
			$action = "add";
		}
		$query->close();
	}
	
	echo $action;
	
	if ($action == "add"){
		$stmt = "insert into staxpassword (shopid,password) values (?,?)";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("ss",$shopid,$pw);
			$query->execute();
			$conn->commit();
			$query->close();
		}
	}elseif ($action == "update"){
		$stmt = "update staxpassword set password = ? where shopid = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("ss",$pw,$shopid);
			$query->execute();
			$conn->commit();
			$query->close();
		}
	}

}

mysqli_close($conn);

