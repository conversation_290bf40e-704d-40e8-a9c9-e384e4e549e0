<?php
require CONN;
//require(PRIVATE_PATH."/integrations/mandrill/src/Mandrill.php");

$shopid = $_POST['shopid'];
$t = $_POST['t'];


if ($t == "checkrec") {

    $empid = $_POST['empid'];

    $stmt = "select count(*) c from techpermissions where shopid = '$shopid' and empid = $empid";
    if ($query = $conn->prepare($stmt)) {

        $query->execute();
        $query->bind_result($c);
        $query->fetch();
        $query->close();

    }

    if ($c == 0) {

        $stmt = "insert into techpermissions (shopid,empid) values ('$shopid',$empid)";
        if ($query = $conn->prepare($stmt)) {

            $query->execute();
            $conn->commit();
            $query->close();

        }


    }

    echo "success";

} elseif ($t == "checkbox") {
    $id = $_POST['id'];
    $v = $_POST['val'];
    $empid = $_POST['empid'];

    $stmt = "update employees set " . $id . " = '" . $v . "' where id = ? and shopid = ?";
    printf(str_replace('?', "'%s'", $stmt), $empid, $shopid);

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("is", $empid, $shopid);
        if (!$query->execute()) {
            echo $conn->errno;
        } else {
            $conn->commit();
            echo "success";
        }
        $query->close();

        //Notification
        $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='143'";
        $query = $conn->prepare($stmt);
        $query->execute();
        $query->store_result();
        $numrows = $query->num_rows();
        if ($numrows > 0) {
            $query->bind_result($textcontent, $emailcontent, $popupcontent);
            $query->fetch();
            $stmt = "select employees.employeefirst, employees.employeelast from employees where shopid = '$shopid' and id = '$empid'";
            if ($query = $conn->prepare($stmt)) {
                $query->execute();
                $query->bind_result($empfname, $emplname);
                $query->fetch();
                $query->close();
            }
            $empname = $empfname . ' ' . $emplname;
            $emailcontent = str_replace("*|EMPLOYEE|*", $empname, $emailcontent);
            $popupcontent = str_replace("*|EMPLOYEE|*", $empname, $popupcontent);
            $textcontent = str_replace("*|EMPLOYEE|*", $empname, $textcontent);
            $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'143',?,?,?)";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param('ssss', $shopid, $popupcontent, $textcontent, $emailcontent);
                $query->execute();
                $conn->commit();
                $query->close();
            }
        }


    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }


} elseif ($t == "checkboxtech") {
    if ($_POST['id'] == "techonlytechissues") {
        $id = "onlytechissues";
    } else {
        if ($_POST['id'] != "techreport" && $_POST['id'] != "techsupervisor") {
            $id = str_replace("tech", "", $_POST['id']);
        } else {
            $id = $_POST['id'];
        }
    }
    $v = $_POST['val'];
    $empid = $_POST['empid'];

    $stmt = "select empid from techpermissions where empid = $empid";
    $query = mysqli_query($conn, $stmt);

    if (mysqli_num_rows($query) > 0) {

        $stmt = "update techpermissions set " . $id . " = '" . $v . "' where empid = $empid and shopid = '$shopid'";
        echo $stmt;

        if ($query = $conn->prepare($stmt)) {
            if (!$query->execute()) {
                echo $conn->errno;
            } else {
                $conn->commit();
                echo "success";
            }
            $query->close();
        } else {
            echo "UPdate Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }

    } else {

        $stmt = "insert into techpermissions (shopid,empid,$id) values ('$shopid',$empid,'$v')";

        if ($query = $conn->prepare($stmt)) {
            if (!$query->execute()) {
                echo $conn->errno;
            } else {
                $conn->commit();
                echo "success";
            }
            $query->close();
        } else {
            echo "Insert Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }


    }

    //Notification
    $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='143'";
    $query = $conn->prepare($stmt);
    $query->execute();
    $query->store_result();
    $numrows = $query->num_rows();
    if ($numrows > 0) {
        $query->bind_result($textcontent, $emailcontent, $popupcontent);
        $query->fetch();
        $stmt = "select employees.employeefirst, employees.employeelast from employees where shopid = '$shopid' and id = '$empid'";
        if ($query = $conn->prepare($stmt)) {
            $query->execute();
            $query->bind_result($empfname, $emplname);
            $query->fetch();
            $query->close();
        }
        $empname = $empfname . ' ' . $emplname;
        $emailcontent = str_replace("*|EMPLOYEE|*", $empname, $emailcontent);
        $popupcontent = str_replace("*|EMPLOYEE|*", $empname, $popupcontent);
        $textcontent = str_replace("*|EMPLOYEE|*", $empname, $textcontent);
        $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'143',?,?,?)";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param('ssss', $shopid, $popupcontent, $textcontent, $emailcontent);
            $query->execute();
            $conn->commit();
            $query->close();
        }
    }

} elseif ($t == "emp") {

    $id = $_POST['id'];
    $last = trim($_POST['last']);
    $first = trim($_POST['first']);
    $phone = str_replace("-", "", $_POST['phone']);
    $email = trim($_POST['empemail']);
    $emergcontact = trim($_POST['emergcontact']);
    $emergphone = trim($_POST['emergphone']);
    $Position = $_POST['position'];
    $password = $_POST['password'];
    $oldpassword = $_POST['oldpassword'];
    $michmech = $_POST['michmech'];
    $showtechlist = $_POST['showtechlist'];
    $Active = $_POST['Active'];
    $rate = $_POST['rate'];
    $paytype = $_POST['paytype'];
    $pin = $_POST['pin'];
    $mode = $_POST['mode'];
    $dateHired = (!empty($_POST['datehired']) && $_POST['datehired'] != '0000-00-00')?$_POST['datehired']:time();
    $datehired = date_format(new DateTime($dateHired), 'Y-m-d');
    $comments = $_POST['comments'];
    $color = $_POST['color'];
    //echo $datehired;

    $stmt = "update employees set comments = ?,employeephone = ?,employeeemail = ?,employeelast = ?, employeefirst = ?, jobdesc = ?, mechanicnumber = ?, showtechlist = ?, active = ?, hourlyrate = ?, paytype = ?, pin = ?, mode = ?, datehired = ?, color = ?, emergcontact =?, emergphone = ? where shopid = ? and id = ?";

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("sssssssssdssssssssi", $comments, $phone, $email, $last, $first, $Position, $michmech, $showtechlist, $Active, $rate, $paytype, $pin, $mode, $datehired, $color, $emergcontact, $emergphone, $shopid, $id);
        if ($query->execute()) {
            $conn->commit();

            if (!empty($password) && $password != $oldpassword) {
                $passwordhash = password_hash($password, PASSWORD_DEFAULT);

                if($id!=$_COOKIE['empid'])$extrastr = ",forcelogout='1'";
                else $extrastr = "";

                $stmt = "update employees set password = ?,passwordenc = ? $extrastr where shopid = ? and id = ?";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("sssi", $password, $passwordhash, $shopid, $id);
                    $query->execute();
                    $conn->commit();
                    $query->close();
                }
            }
            echo "success";
        } else {
            echo $conn->errno;
        }

        $query->close();

    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

} elseif ($t == "add") {

    $logintosbp = "NO";
    if (isset($_POST['logintosbp'])) {
        $logintosbp = $_POST['logintosbp'];
    }
    $CompanyAccess = "NO";
    if (isset($_POST['CompanyAccess'])) {
        $CompanyAccess = $_POST['CompanyAccess'];
    }
    $EmployeeAccess = "NO";
    if (isset($_POST['EmployeeAccess'])) {
        $EmployeeAccess = $_POST['EmployeeAccess'];
    }
    $ReportAccess = "NO";
    if (isset($_POST['ReportAccess'])) {
        $ReportAccess = $_POST['ReportAccess'];
    }
    $CreateRO = "NO";
    if (isset($_POST['CreateRO'])) {
        $CreateRO = $_POST['CreateRO'];
    }
    $CreateCT = "NO";
    if (isset($_POST['CreateCT'])) {
        $CreateCT = $_POST['CreateCT'];
    }
    $EditSupplier = "NO";
    if (isset($_POST['EditSupplier'])) {
        $EditSupplier = $_POST['EditSupplier'];
    }
    $InventoryLookup = "NO";
    if (isset($_POST['InventoryLookup'])) {
        $InventoryLookup = $_POST['InventoryLookup'];
    }
    $candelete = "NO";
    if (isset($_POST['candelete'])) {
        $candelete = $_POST['candelete'];
    }
    $changeshopnotice = "NO";
    if (isset($_POST['changeshopnotice'])) {
        $changeshopnotice = $_POST['changeshopnotice'];
    }
    $accounting = "NO";
    if (isset($_POST['accounting'])) {
        $accounting = $_POST['accounting'];
    }
    $downloaddata = "NO";
    if (isset($_POST['downloaddata'])) {
        $downloaddata = $_POST['downloaddata'];
    }
    $showgpinro = "NO";
    if (isset($_POST['showgpinro'])) {
        $showgpinro = $_POST['showgpinro'];
    }
    $editcommentsinro = "NO";
    if (isset($_POST['editcommentsinro'])) {
        $editcommentsinro = $_POST['editcommentsinro'];
    }
    $EditInventory = "NO";
    if (isset($_POST['EditInventory'])) {
        $EditInventory = $_POST['EditInventory'];
    }
    $ReOpenRO = "";
    if (isset($_POST['ReOpenRO'])) {
        $ReOpenRO = $_POST['ReOpenRO'];
    }
    $changerodate = "NO";
    if (isset($_POST['changerodate'])) {
        $changerodate = $_POST['changerodate'];
    }
    $ChangePartMatrix = "NO";
    if (isset($_POST['ChangePartMatrix'])) {
        $ChangePartMatrix = $_POST['ChangePartMatrix'];
    }
    $ChangePartCodes = "NO";
    if (isset($_POST['ChangePartCodes'])) {
        $ChangePartCodes = $_POST['ChangePartCodes'];
    }
    $ChangeJobDescription = "NO";
    if (isset($_POST['ChangeJobDescription'])) {
        $ChangeJobDescription = $_POST['ChangeJobDescription'];
    }
    $ChangeSources = "NO";
    if (isset($_POST['ChangeSources'])) {
        $ChangeSources = $_POST['ChangeSources'];
    }
    $ChangeRepairOrderTypes = "NO";
    if (isset($_POST['ChangeRepairOrderTypes'])) {
        $ChangeRepairOrderTypes = $_POST['ChangeRepairOrderTypes'];
    }
    $sendupdates = "NO";
    if (isset($_POST['sendupdates'])) {
        $sendupdates = $_POST['sendupdates'];
    }
    $deletepaymentsreceived = "NO";
    if (isset($_POST['deletepaymentsreceived'])) {
        $deletepaymentsreceived = $_POST['deletepaymentsreceived'];
    }
    $deletecustomer = "NO";
    if (isset($_POST['deletecustomer'])) {
        $deletecustomer = $_POST['deletecustomer'];
    }
    $editnotifications = "NO";
    if (isset($_POST['editnotifications'])) {
        $editnotifications = $_POST['editnotifications'];
    }
    $edittechpaidlog = "NO";
    if (isset($_POST['edittechpaidlog'])) {
        $edittechpaidlog = $_POST['edittechpaidlog'];
    }
    $dashboardAccess = "NO";
    if (isset($_POST['DashboardAccess'])) {
        $dashboardAccess = $_POST['DashboardAccess'];
    }
    $changerostatus = "NO";
    if (isset($_POST['changerostatus'])) {
        $changerostatus = $_POST['changerostatus'];
    }
    $partsordering = "NO";
    if (isset($_POST['partsordering'])) {
        $partsordering = $_POST['partsordering'];
    }
    $mergecustomers = "NO";
    if (isset($_POST['mergecustomers'])) {
        $mergecustomers = $_POST['mergecustomers'];
    }

    $last = trim($_POST['last']);
    $first = trim($_POST['first']);
    $photo = trim($_POST['photo_name']);
    $phone = str_replace("-", "", $_POST['phone']);
    $email = trim($_POST['empemail']);
    $emergcontact = trim($_POST['emergcontact']);
    $emergphone = trim($_POST['emergphone']);
    $Position = $_POST['Position'];
    $password = $_POST['password'];
    $passwordenc = password_hash($password, PASSWORD_DEFAULT);
    $michmech = $_POST['michmech'];
    $showtechlist = $_POST['showtechlist'];
    $Active = $_POST['Active'];
    $rate = $_POST['rate'];
    $paytype = $_POST['paytype'];
    $pin = $_POST['pin'];
    $mode = $_POST['mode'];
    $dateHired = (!empty($_POST['datehired']) && $_POST['datehired'] != '0000-00-00')?$_POST['datehired']:time();
    $datehired = is_numeric($dateHired) ? date('Y-m-d', $dateHired) : date_format(new DateTime($dateHired), 'Y-m-d');
    $empid = strtoupper($shopid . "-" . generateRandomStr());
    $defaultwriter = "NO";


    $stmt = "insert into employees (employeephone,employeeemail,defaultwriter, employeelast, employeefirst, jobdesc, password, passwordenc, mechanicnumber, showtechlist, active, hourlyrate, paytype,"
        . " pin, mode, datehired, logintosbp, CompanyAccess, EmployeeAccess, ReportAccess, CreateRO, CreateCT, EditSupplier, "
        . "InventoryLookup, candelete, changeshopnotice, accounting, downloaddata, showgpinro, editcommentsinro, EditInventory, ReOpenRO, changerodate, ChangePartMatrix, ChangePartCodes, "
        . "ChangeJobDescription, ChangeSources, ChangeRepairOrderTypes, sendupdates, deletepaymentsreceived, deletecustomer, edittechpaidlog, editnotifications, shopid,employeeid,emergcontact,emergphone,DashboardAccess, changerostatus, photo, partsordering, mergecustomers)"
        . " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    //printf (str_replace('?',"'%s'",$stmt),$last,$first,$Position,$password,$michmech,$showtechlist,$Active,$rate,$paytype,$pin,$mode,$datehired,$logintosbp,$CompanyAccess,$EmployeeAccess,$ReportAccess,$CreateRO,$CreateCT,$EditSupplier,$InventoryLookup,$candelete,$changeshopnotice,$replacerowithtag,$EditInventory,$ReOpenRO,$ChangePartMatrix,$ChangePartCodes,$ChangeJobDescription,$ChangeSources,$ChangeRepairOrderTypes,$sendupdates,$deletepaymentsreceived,$deletecustomer,$shopid,$empid);


    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("sssssssssssdssssssssssssssssssssssssssssssssssssssss", $phone, $email, $defaultwriter, $last, $first, $Position, $password, $passwordenc, $michmech, $showtechlist, $Active, $rate, $paytype, $pin, $mode, $datehired, $logintosbp, $CompanyAccess, $EmployeeAccess, $ReportAccess, $CreateRO, $CreateCT, $EditSupplier, $InventoryLookup, $candelete, $changeshopnotice, $accounting, $downloaddata, $showgpinro, $editcommentsinro, $EditInventory, $ReOpenRO, $changerodate, $ChangePartMatrix, $ChangePartCodes, $ChangeJobDescription, $ChangeSources, $ChangeRepairOrderTypes, $sendupdates, $deletepaymentsreceived, $deletecustomer, $editnotifications, $edittechpaidlog, $shopid, $empid, $emergcontact, $emergphone, $dashboardAccess, $changerostatus, $photo, $partsordering, $mergecustomers);
        if ($query->execute()) {
            $conn->commit();
            echo "success";
        } else {
            echo $conn->errno;
        }

        $query->close();

    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

    // now create the tech permissions record
}

elseif ($t == "forcelogout") {

    $empids = $_POST['empids'];

    $idar = explode(",",$empids);

    if(!empty($idar))
    {
     foreach($idar as $id)
     {

        $stmt="update employees set forcelogout='1' where id = ? and shopid = ?";
        if ($query = $conn->prepare($stmt))
        {
            $query->bind_param("is",$id,$shopid);
            $query->execute();
            $conn->commit();
        }

     }

    }

}

elseif ($t == "changepassword") {

    $id = filter_var($_POST['id'], FILTER_SANITIZE_STRING);
    $password = filter_var($_POST['password'], FILTER_SANITIZE_STRING);
    $passwordhash = password_hash($password, PASSWORD_DEFAULT);

    if($id!=$_COOKIE['empid'])$extrastr = ",forcelogout='1'";
    else $extrastr = "";

    $stmt = "update employees set password = ?,passwordenc = ? $extrastr where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("sssi", $password, $passwordhash, $shopid, $id);
        $query->execute();
        $affected_rows = $conn->affected_rows;
        if (!$conn->commit()){
               echo "Something Went Wrong, Please try again later".$conn->error;
        } else {
            echo "Password Updated";
        }
        $query->close();
    }
    return;
}

mysqli_close($conn);

?>
