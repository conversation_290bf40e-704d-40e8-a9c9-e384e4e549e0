<?php
$component = "settings-v2";
include INTEGRATIONS_PATH . "/sbp_bucket/sbp_bucket.php";
include getRulesComponent($component);
include getHeadGlobal($component);
//$fleetshops = array();

$shopid = $_COOKIE['shopid'];
//echo $shopid;
$id = $_GET['id'];

$stmt = "select year(datestarted) from company where shopid = ?";

if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($yearstarted);
    $query->fetch();
    $query->close();
}

$stmt = "select comments,downloaddata,changerodate,shopid,EmployeeID,EmployeeLast,EmployeeFirst,EmployeeAddress,EmployeeCity,EmployeeState,EmployeeZip,EmployeePhone,EmployeeEmail,JobDesc,DateHired,DefaultWriter,Active,"
    . "hourlyrate,pin,tooltip,CompanyAccess,EmployeeAccess,ReportAccess,CreateRO,CreateCT,EditSupplier,InventoryLookup,ViewSecurityLog,EditInventory,ReOpenRO,ChangeUserSecurity,"
    . "ChangePartMatrix,ChangePartCodes,ChangeJobDescription,ChangeSources,ChangeRepairOrderTypes,logintosbp,accounting,mode,password,passwordenc,sendupdates,deletepaymentsreceived,ipadhelp,"
    . "editable,mechanicnumber,paytype,showtechlist,candelete,changeshopnotice,deletecustomer,editnotifications,showgpinro,edittechpaidlog,editcommentsinro,color,DashboardAccess,IntegrationAccess,changerostatus, photo, partsordering, mergecustomers, pphAccess from employees where id = ? and shopid = ?";

if ($query = $conn->prepare($stmt)) {

    $query->bind_param("is", $id, $shopid);
    $query->execute();
    $query->bind_result($comments, $downloaddata, $changerodate, $shopid, $EmployeeID, $EmployeeLast, $EmployeeFirst, $EmployeeAddress, $EmployeeCity, $EmployeeState, $EmployeeZip, $EmployeePhone, $EmployeeEmail, $JobDesc, $DateHired, $DefaultWriter, $Active, $hourlyrate, $pin, $tooltip, $CompanyAccess, $EmployeeAccess, $ReportAccess, $CreateRO, $CreateCT, $EditSupplier, $InventoryLookup, $ViewSecurityLog, $EditInventory, $ReOpenRO, $ChangeUserSecurity, $ChangePartMatrix, $ChangePartCodes, $ChangeJobDescription, $ChangeSources, $ChangeRepairOrderTypes, $logintosbp, $accounting, $mode, $password, $passwordenc, $sendupdates, $deletepaymentsreceived, $ipadhelp, $editable, $mechanicnumber, $paytype, $showtechlist, $candelete, $changeshopnotice, $deletecustomer, $editnotifications, $showgpinro, $edittechpaidlog, $editcommentsinro, $schcolor, $dashboardaccess, $integrationacc, $changerostatusfull, $photo, $partsordering,$mergecustomers, $pphAccess);
    $query->fetch();
    $query->close();
} else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

// get permissions for this tech
$empid = $id;
$stmt = "select orderparts,techreport,changerostatus,showcustonwip,editparts,editlabor,viewschedule,editschedule,editcannedjobs,editsublet,changevistatus,viewhistory,editmilesin,editmilesout,vieweditcomments,showsublet,onlytechissues,showcustomerinfo,sendinspection,techsupervisor,showpartscostonro,accessworkflow, showwtkonwip,showlaborhoursonro from techpermissions where shopid = '$shopid' and empid = $empid";
if ($query = $conn->prepare($stmt)) {
    if ($query->execute()) {
        $query->bind_result($orderparts, $techreport, $changerostatus, $showcustonwip, $editparts, $editlabor, $viewschedule, $editschedule, $editcannedjobs, $editsublet, $changevistatus, $viewhistory, $editmilesin, $editmilesout, $vieweditcomments, $showsublet, $onlytechissues, $showcustomerinfo, $sendinspection, $techsupervisor, $showpartscostonro, $accessworkflow, $showwtkonwip, $showlaborhoursonro);
        $query->fetch();
    } else {
        $editparts = "yes";
        $editlabor = "yes";
        $viewschedule = "yes";
        $editschedule = "no";
        $editcannedjobs = "yes";
        $editsublet = "yes";
        $changevistatus = "yes";
        $viewhistory = "yes";
        $editmilesin = "yes";
        $editmilesout = "yes";
        $vieweditcomments = "yes";
        $changerostatus = "yes";
        $showcustomerinfo = "yes";
        $sendinspection = "yes";
        $techreport = "no";
        $orderparts = "no";
        $techsupervisor = "no";
        $showpartscostonro = "yes";
        $showwtkonwip = 'no';
        $showlaborhoursonro = "yes";
    }
    $query->close();
} else {
    $editparts = "yes";
    $editlabor = "yes";
    $viewschedule = "yes";
    $editschedule = "no";
    $editcannedjobs = "yes";
    $editsublet = "yes";
    $changevistatus = "yes";
    $viewhistory = "yes";
    $editmilesin = "yes";
    $editmilesout = "yes";
    $vieweditcomments = "yes";
    $changerostatus = "yes";
    $showcustomerinfo = "yes";
    $sendinspection = "yes";
    $techreport = "no";
    $orderparts = "no";
    $techsupervisor = "no";
    $showpartscostonro = "yes";
    $showwtkonwip = 'no';
    $showlaborhoursonro = "yes";
}

if ($editparts == "") {
    $editparts = "yes";
}
if ($editlabor == "") {
    $editlabor = "yes";
}
if ($viewschedule == "") {
    $viewschedule = "yes";
}
if ($editschedule == "") {
    $editschedule = "no";
}
if ($editcannedjobs == "") {
    $editcannedjobs = "yes";
}
if ($editsublet == "") {
    $editsublet = "yes";
}
if ($changevistatus == "") {
    $changevistatus = "yes";
}
if ($viewhistory == "") {
    $viewhistory = "yes";
}
if ($editmilesin == "") {
    $editmilesin = "yes";
}
if ($editmilesout == "") {
    $editmilesout = "yes";
}
if ($vieweditcomments == "") {
    $vieweditcomments = "yes";
}
if ($changerostatus == "") {
    $changerostatus = "yes";
}
if ($showcustomerinfo == "") {
    $showcustomerinfo = "yes";
}
if ($sendinspection == "") {
    $sendinspection = "yes";
}
if ($techreport == "") {
    $techreport = "no";
}
if ($orderparts == "") {
    $orderparts = "no";
}
if ($techsupervisor == "") {
    $techsupervisor = "no";
}

if($_COOKIE['empid'] == 'Admin')
$loginasemployee = true;
else
{
    $stmt = "select jobdesc from employees where shopid = ? and id = ?";

    if ($query = $conn->prepare($stmt)) {

        $query->bind_param("si", $shopid,$_COOKIE['empid']);
        $query->execute();
        $query->bind_result($userjobdesc);
        $query->fetch();
        $query->close();
    }

    if(strtolower($userjobdesc) == 'owner')
    $loginasemployee = true;
    else
    $loginasemployee = false;
}
?>

<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/color-picker/color-picker.css">

<body>
<?php
include getHeaderGlobal($component);
include getMenuGlobal($component);
?>
<main id="settings" class="min-vh-100">
    <div class="report">
        <div class="col-12">
            <div class="row">
                <div class="col-md-9 col-sm-6">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/settings/settings.php"
                           class="text-secondary d-print-none">Settings</a>
                        <span class="text-secondary d-print-none ps-3 pe-3">/</span>
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/settings/employees.php"
                           class="text-secondary d-print-none">Employee</a>
                        <span class="text-secondary d-print-none ps-3 pe-3">/</span>
                        <h2 class="d-print-none">Employee Edit</span></h2>
                    </div>
                </div>
            </div>
            <hr/>
        </div>
    </div>
    <div class="d-flex">
        <section class="container-fluid" id="">
            <form id="mainform" class="form-horizontal push-10-t" name="mainform">
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="form-outline mb-4">
                            <input class="form-control" type="text"
                                   value="<?php echo $EmployeeLast; ?>" id="last" name="last">
                            <label for="last" class="form-label">Last Name*</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control"
                                   value="<?php echo $EmployeeFirst; ?>" type="text" id="first"
                                   name="first">
                            <label for="first" class="form-label">First Name*</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control"  value="<?php echo $EmployeePhone; ?>"
                                   type="text" id="phone"
                                   name="phone" onKeyUp="javascript:return mask(this.value,this,'3,7','-');"
                                   onBlur="javascript:return mask(this.value,this,'3,7','-');">
                            <label for="phone" class="form-label">Phone</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control"  value="<?php echo $EmployeeEmail; ?>"
                                   type="text" id="empemail" name="empemail">
                            <label class="form-label" for="empemail">Email</label>
                        </div>
                        <div class="form-group mb-4">
                            <select class="select" name="position" id="position">
                                <?php
                                echo "<option selected value='" . $JobDesc . "'>" . $JobDesc . "</option>";
                                $stmt = "select JobDesc from jobdesc where ucase(JobDesc) != '" . strtoupper($JobDesc) . "' and shopid = '" . $shopid . "'";
                                $result = $conn->query($stmt);
                                while ($row = $result->fetch_array()) {
                                    echo "<option value='" . $row['JobDesc'] . "'>" . $row['JobDesc'] . "</option>";
                                }
                                ?>
                            </select>
                            <label for="position" class="form-label select-label">Position</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control" type="text"
                                   value="<?php echo $mechanicnumber; ?>" id="michmech" name="michmech">
                            <label class="form-label" id="cityfloatinglabel">Michigan Mech #</label>
                        </div>
                        <div class="form-group mb-4">
                            <select class="select" name="showtechlist" id="showtechlist"
                                    onchange="setMode(this.value)">
                                <?php
                                if (strtoupper($showtechlist) == "YES") {
                                    $ys = " selected='selected' ";
                                    $ns = "";
                                } else {
                                    $ns = " selected='selected' ";
                                    $ys = "";
                                }
                                ?>
                                <option <?php echo $ys; ?> value="YES">YES</option>
                                <option <?php echo $ns; ?> value="NO">NO</option>
                            </select>
                            <label for="showtechlist" class="form-label select-label" id="statefloatinglabel">Is Employee a Technician (Must be marked yes to be assigned to a labor line)</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-4">
                            <select class="select" name="Active" id="Active">
                                <?php
                                if (strtoupper($Active) == "YES") {
                                    $ys = " selected='selected' ";
                                    $ns = "";
                                } else {
                                    $ns = " selected='selected' ";
                                    $ys = "";
                                }
                                ?>
                                <option <?php echo $ys; ?> value="YES">YES</option>
                                <option <?php echo $ns; ?> value="NO">NO</option>
                            </select>
                            <label for="Active" class="form-label select-label">Active</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control" type="text"
                                   value="<?php echo number_format($hourlyrate, 2); ?>" id="rate"
                                   name="rate">
                            <label class="form-label" for="rate">Pay Rate</label>
                        </div>
                        <div class="form-group mb-4">
                            <select name="paytype" class="select" id="paytype">
                                <?php
                                $frs = "";
                                $hrs = "";
                                $prs = "";

                                if (strtoupper($paytype) == "FLATRATE") {
                                    $frs = " selected='selected' ";
                                }
                                if (strtoupper($paytype) == "HOURLY") {
                                    $hrs = " selected='selected' ";
                                }
                                if (strtoupper($paytype) == "PERCENTAGE") {
                                    $prs = " selected='selected' ";
                                }

                                ?>
                                <option <?php echo $frs; ?> value="FLATRATE">Flat Rate</option>
                                <option <?php echo $hrs; ?> value="HOURLY">Hourly</option>
                                <?php if ($shopid == '1932') { ?>
                                    <option <?php echo $prs; ?> value="PERCENTAGE">Percentage</option>
                                    <?php
                                }
                                ?>
                            </select>
                            <label class="form-label select-label" for="paytype">Pay Type</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control" type="text"  value="<?php echo $pin; ?>"
                                   id="pin" name="pin">
                            <label for="pin" class="form-label">Timeclock PIN Number</label>
                        </div>
                        <div class="form-group mb-2">
                            <select name="mode" class="select" id="mode">
                                <option value="Full" <?= strtolower($mode)=='full'?'selected':''?>>Full</option>
                                <?php if($yearstarted <= 2024){?><option value="Tech" <?= strtolower($mode)=='tech'?'selected':''?>>Tech</option><?php }?>
                                <option value="Tech2" <?= strtolower($mode)=='tech2'?'selected':''?>>Tech 2.0</option>
                            </select>

                            <label for="mode" class="form-label select-label">User Mode</label>
                        </div>
                        <div class="mb-4" style="width: 15vh">
                            <label for="colorpicker" class="form-label">Schedule Color</label>
                            <input class="form-control" type="text"
                                   name="color" id="colorpicker">
                        </div>
                        <div class="form-outline mb-4 datepicker" data-mdb-inline="true" data-mdb-format="mm/dd/yyyy">
                            <input class="form-control" type="text"
                                   value="<?= $DateHired != '0000-00-00' ? date_format(new DateTime($DateHired), 'm/d/Y') : '' ?>"
                                   id="datehired" name="datehired" data-mdb-toggle="datepicker">
                            <label class="form-label" for="datehiredinput">Date Hired</label>
                        </div>
                        <div class="form-outline mb-4">
                                <textarea class="form-control"  id="comments"
                                          name="comments"><?php echo $comments; ?></textarea>
                            <label for="comments" class="form-label">Comments</label>
                        </div>
                        <div class="col-3 mb-4">
                            <?php if (!empty($photo)) { ?>
                                <div class="text-center d-flex">
                                    <div class="text-primary float-end">
                                        <i class="fas fa-trash pe-auto" style='cursor:pointer'
                                           onclick="deletePhoto()"></i>
                                    </div>
                                    <?php
                                    $emp_photo = UPLOAD_URL . "/$shopid/$photo";
                                    if (strpos($photo, "/") !== false){
                                        $emp_photo = sbp_bucket::public_url.$photo;
                                    }

                                    echo "<img id='photoImage' class='img-thumbnail ripple rounded-circle' onclick='editPhoto()' style='cursor:pointer' src='$emp_photo'>";
                                    ?>
                                </div>
                            <?php } else { ?>
                                <div class="text-center">
                                    <img id='sbpdropzone' class="dropzone w-100 rounded-circle"
                                         src='https://upload.wikimedia.org/wikipedia/commons/a/ac/Default_pfp.jpg'>
                                </div>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-9 text-center">
                        <br><br>
                        <div style="margin-left:100px;" class="col-sm-12">
                            <button class="btn btn-secondary btn-md" data-mdb-toggle="modal" data-mdb-target="#passwordmodal" type="button">
                                    Change Password
                            </button>
                            <?php if($loginasemployee){ ?>
                                <button class="btn btn-secondary btn-md" onclick="loginAsEmployee()" type="button">
                                    Login as <?= $EmployeeFirst.' '.$EmployeeLast?>
                                </button>
                            <?php }?>
                            <?php if (in_array($shopid, $fleetshops)) { ?>
                                <button class="btn btn-secondary btn-md" onclick="assignCustomers()" type="button">
                                    Assign
                                    Customers
                                </button><?php } ?>
                            <button id="techperm"
                                    style="display: <?= strtoupper($showtechlist) != 'YES' ? 'none' : '' ?>"
                                    class="btn btn-secondary btn-md" onclick="openTechPermissions()" type="button">
                                Tech Mode
                                Permissions
                            </button>
                            <button class="btn btn-secondary btn-md" data-mdb-toggle="modal"
                                    data-mdb-target="#permissionsmodal" type="button">Permissions
                            </button>
                            <button class="btn btn-primary btn-md" onclick="saveAll()" type="button">Save</button>
                        </div>
                    </div>
                </div>

                <?php if (in_array($shopid, $profitboost)) { ?>
                    <div class="row">
                        <div class="col-md-9 text-center">
                            <p class="text-primary">*Make sure to update the PPH Calculator with the updated
                                employee settings</p>
                        </div>
                    </div>
                <?php } ?>
            </form>
        </section>
    </div>
</main>

<div id="techpermissionsmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-lg modal-dialog modal-dialog-top">
        <div class="modal-content p-4">
            <div class="modal-header">
                <h5 class="modal-title" id="spdLabel">Tech Mode Permissions</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox" id="techeditparts" <?php if (strtoupper($editparts) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label for="techeditparts" class="form-check-label">Edit Parts</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Technicians can edit part lines in a repair order." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox" id="techeditlabor" <?php if (strtoupper($editlabor) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="techeditlabor">Edit Labor</label>

                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Technicians can edit labor lines in a repair order." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox" id="techeditsublet" <?php if (strtoupper($editsublet) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label for="techeditsublet" class="form-check-label">Edit Sublet</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Technicians can edit sublet lines in a repair order." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="techeditcannedjobs" <?php if (strtoupper($editcannedjobs) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="techeditcannedjobs">Edit Canned Jobs</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Technicians can edit canned jobs in a repair order." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox" id="techshowsublet" <?php if (strtoupper($showsublet) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="techshowsublet">Show Sublet</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Technicians can see sublet jobs in a repair order." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="techonlytechissues" <?php if (strtoupper($onlytechissues) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="techonlytechissues">Show ONLY Tech Issues</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Technicians can only see labor lines assigned to them." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="techshowcustonwip" <?php if (strtoupper($showcustonwip) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="techshowcustonwip">Show Customer on WIP</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Customers name is visible on the Tech Mode Work in process." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="showwtkonwip" <?php if (strtoupper($showwtkonwip) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="showwtkonwip">Show Writer & Technician on WIP</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Technician can see the service writer and technician assigned to a repair order." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="techviewschedule" <?php if (strtoupper($viewschedule) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="techviewschedule">View Customer Schedule</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Technician can view the customer schedule for upcoming appointments." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="techeditschedule" <?php if (strtoupper($editschedule) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="techeditschedule">Edit Customer Schedule</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Technician can edit the customer schedule for scheduled appointments." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="techsupervisor" <?php if (strtoupper($techsupervisor) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="techsupervisor">Tech Supervisor/Shop
                                Foreman</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Allows the technician to see all assigned labor lines." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="showpartscostonro" <?php if (strtoupper($showpartscostonro) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="showpartscostonro">Show Parts Cost in RO</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title=" Parts cost will be visible to technicians in a repair order." ></i>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="techchangevistatus" <?php if (strtoupper($changevistatus) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="techchangevistatus">Change Vehicle Issue
                                Status</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Technician can change vehicle issue status per customer concern – [pending, approved, scheduled, parts ordered, assembly, job complete, and declined]." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="techviewhistory" <?php if (strtoupper($viewhistory) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="techviewhistory">View History</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Technician can view previous service history performed at the shop on a customer’s vehicle." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="techeditmilesin" <?php if (strtoupper($editmilesin) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="techeditmilesin">Edit Miles In</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Technician can edit the mileage in field in a repair order." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="techeditmilesout" <?php if (strtoupper($editmilesout) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="techeditmilesout">Edit Miles Out</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Technician can edit the mileage out field in a repair order." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="techvieweditcomments" <?php if (strtoupper($vieweditcomments) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="techvieweditcomments">Edit Comments</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Technician can edit comments field in a repair order. Can be used in addition to tech notes." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="techchangerostatus" <?php if (strtoupper($changerostatus) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="techchangerostatus">Change RO Status</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Can change the RO Status in a repair order including marking a ticket closed." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="techshowcustomerinfo" <?php if (strtoupper($showcustomerinfo) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="techshowcustomerinfo">Show Customer Info</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Technician can view customer information in a repair order." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="techsendinspection" <?php if (strtoupper($sendinspection) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="techsendinspection">Send Inspection to
                                Customer</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Technician can send completed inspections to a customer. " ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="techreport" <?php if (strtoupper($techreport) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="techreport">View Tech Production Report</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Technician can view Technician Production Report by date range including Hours worked, total labor, and tech pay." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="orderparts" <?php if (strtoupper($orderparts) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="orderparts">Order Parts Online</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title=" Technician can access online parts integrations to order parts." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="accessworkflow" <?php if (strtoupper($accessworkflow) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="accessworkflow">Workflow Access</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Technician can view the Kanban workflow job board in addition to the work in process." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="showlaborhoursonro" <?php if (strtoupper($showlaborhoursonro) == "YES") {
                                echo 'checked';
                            } ?> onchange="saveTech(this.id)" class="form-check-input">
                            <label class="form-check-label" for="showlaborhoursonro">Show Labor Hours in RO</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title=" Labor Hours will be visible to technicians in a repair order." ></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-center">

            </div>
        </div>
    </div>
</div>

<div id="permissionsmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-lg modal-dialog modal-dialog-top">
        <div class="modal-content p-4">
            <div class="modal-header">
                <h5 class="modal-title" id="spdLabel">Employee Permissions</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?php //$InventoryLookup,$EditInventory,$ReOpenRO,$ChangeUserSecurity,$ChangeSources,$ChangeRepairOrderTypes,$logintosbp,$accounting,$sendupdates,$showtechlist,$deletecustomer ?>
                <input id="customerid" type="hidden">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="logintosbp" <?php if (strtoupper($logintosbp) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="logintosbp">Login to Shop Boss Pro</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Allows employee to login to their Shop Boss Account." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="CompanyAccess" <?php if (strtoupper($CompanyAccess) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="CompanyAccess">Settings Access</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Has access to system settings." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="EmployeeAccess" <?php if (strtoupper($EmployeeAccess) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="EmployeeAccess">Add/Edit Employees</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Can add/edit employee profiles." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="ReportAccess" <?php if (strtoupper($ReportAccess) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="ReportAccess">Report Access</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Allows access to system reports." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="CreateRO" <?php if (strtoupper($CreateRO) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="CreateRO">Create RO</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Ability to create a repair order." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="CreateCT" <?php if (strtoupper($CreateCT) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="CreateCT">Create Part Sale</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Ability to do a part sale/over the counter sale." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="EditSupplier" <?php if (strtoupper($EditSupplier) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="EditSupplier">Edit Supplier</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Ability to add/edit part suppliers." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="InventoryLookup" <?php if (strtoupper($InventoryLookup) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="InventoryLookup">Inventory Lookup</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Can search current Inventory." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="candelete" <?php if (strtoupper($candelete) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="candelete">Delete Labor and Parts from RO</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Can delete labor/parts from a repair order." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="changeshopnotice" <?php if (strtoupper($changeshopnotice) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="changeshopnotice">Change Shop Notice</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Can update shop notice alerts." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="accounting" <?php if (strtoupper($accounting) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="accounting">Accounting Access</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Can access accounting." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="downloaddata" <?php if (strtoupper($downloaddata) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="downloaddata">Download Data</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Can download and backup system data." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="showgpinro" <?php if (strtoupper($showgpinro) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="showgpinro">Show GP in RO</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Gross Profit is visible in a Repair Order." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="editcommentsinro" <?php if (strtoupper($editcommentsinro) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="editcommentsinro">Edit Comments in RO</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Ability to edit Communication log notes." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="IntegrationAccess" <?php if (strtoupper($integrationacc) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="IntegrationAccess">Integrations Access</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Access to system integrations." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="partsordering" <?php if (strtoupper($partsordering) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="partsordering">Order Parts Online</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Ability to order parts through parts integrations." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="pphAccess" <?php if (strtoupper($pphAccess) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="pphAccess">PPH Access</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Allows access to PPH" ></i>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="EditInventory" <?php if (strtoupper($EditInventory) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="EditInventory">Edit Inventory</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Can edit counts and costs along with inventory fees. " ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="ReOpenRO" <?php if (strtoupper($ReOpenRO) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="ReOpenRO">Re-Open RO</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Can re-open a closed repair order for adjustment." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="changerodate" <?php if (strtoupper($changerodate) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="changerodate">Change RO Dates</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Can edit Status date in a repair order." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="ChangePartMatrix" <?php if (strtoupper($ChangePartMatrix) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="ChangePartMatrix">Change Parts Matrix</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Can adjust parts matrix." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="ChangePartCodes" <?php if (strtoupper($ChangePartCodes) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="ChangePartCodes">Change Parts Codes</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Can change Part code descriptions." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="ChangeJobDescription" <?php if (strtoupper($ChangeJobDescription) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="ChangeJobDescription">Change Job
                                Descriptions</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Can create and change employee job descriptions." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="ChangeSources" <?php if (strtoupper($ChangeSources) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="ChangeSources">Change Advertising Sources</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Can change advertising sources in a repair order." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="ChangeRepairOrderTypes" <?php if (strtoupper($ChangeRepairOrderTypes) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="ChangeRepairOrderTypes">Change RO Types</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Can change RO Types in a repair order." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="sendupdates" <?php if (strtoupper($sendupdates) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="sendupdates">Allow Send Updates to
                                Customer?</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Can send out a repair order update link to the customer from a repair order." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="deletepaymentsreceived" <?php if (strtoupper($deletepaymentsreceived) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="deletepaymentsreceived">Can Delete
                                Payments?</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Ability to delete a payment in a repair order." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="deletecustomer" <?php if (strtoupper($deletecustomer) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="deletecustomer">Can Delete Customer?</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Ability to delete a customer profile from the Customer list." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="editnotifications" <?php if (strtoupper($editnotifications) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="editnotifications">Edit Notifications</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Ability to change what notifications the system reports on in settings." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="edittechpaidlog" <?php if (strtoupper($edittechpaidlog) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="edittechpaidlog">Edit Tech Paid Log in RO</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Ability to add and adjust technician paid hours in a repair order." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="DashboardAccess" <?php if (strtoupper($dashboardaccess) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="DashboardAccess">BOSS Board Access</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Access to the analytics dashboard (Must be apart of your package tier)" ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="changerostatus" <?php if (strtoupper($changerostatusfull) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="changerostatus">Change RO Status</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Can change the RO Status in a repair order including marking a ticket closed." ></i>
                        </div>
                        <div class="form-check form-switch mb-4">
                            <input type="checkbox"
                                   id="mergecustomers" <?php if (strtoupper($mergecustomers) == "YES") {
                                echo 'checked';
                            } ?> onchange="save(this.id)" class="form-check-input">
                            <label class="form-check-label" for="mergecustomers">Merge Customers</label>
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Permission to merge two customers" ></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="passwordmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="changepasswordLabel">Change Password <?php if($id != $_COOKIE['empid']){?><i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Changing password will force logout this employee."></i><?php }?></h5>
                <div class="text-right">
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">        
                        <div class="form-outline mb-4">
                                <input class="form-control" type="password" id="password" name="password">
                                <label for="password" class="form-label">New Password</label>
                            </div>
                            <div class="form-outline mb-4">
                                <input class="form-control" type="password" id="cpassword" name="cpassword">
                                <label for="cpassword" class="form-label">Confirm New Password</label>
                            </div>
                        </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-center pt-2">
                <button class="btn btn-md btn-primary" type="button" onclick="changePassword()">Save</button>
            </div>
        </div>
    </div>
</div>

<form id="loginform" method="post" action="<?= COMPONENTS_PUBLIC ?>/login/loginsettings.php">
    <input type="hidden" name="loginshopid" value="<?= $shopid?>">
    <input type="hidden" name="loginempid" value="<?= $id?>">
    <input type="hidden" name="newui" value="yes">
    <input type="hidden" name="ownerid" value="<?= $_COOKIE['empid']?>">
</form>

<?php
$component = '';
include getScriptsGlobal('');
?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/spectrum/1.8.0/spectrum.min.js"></script>
<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/dropzonejs/dropzone.min.css">
<script src="<?= SCRIPT ?>/plugins/dropzonejs/dropzone.min.js"></script>

<script>
    <?php if (empty($photo)) { ?>
    Dropzone.autoDiscover = false;

    let photoDropzone = new Dropzone('#sbpdropzone', {
        url: 'upload-photo.php?employeeId=<?= $id; ?>',
        maxFilesize: 5, // MB
        uploadMultiple: false,
        acceptedFiles: "image/*",
        success: function (file, response) {
            location.reload()
        },
    });
    <?php } ?>

    function deletePhoto() {
        let photo = encodeURIComponent("<?php echo $photo; ?>");
        sbconfirm("Are you sure?", "This will delete the employee photo so you can upload another.  Are you sure?", function () {
            showLoader();
            $.ajax({
                data: "photo=" + photo + "&employeeId=<?= $id; ?>&shopid=<?= $shopid; ?>",
                url: "delete-photo.php",
                type: "post",
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                    hideLoader();
                },
                success: function (r) {
                    console.log(r)
                    if (r == "success") {
                        location.reload()
                    }
                    hideLoader();
                }
            });
        });

    }

    function editPhoto() {
        let imgurl = encodeURIComponent($('#photoImage').attr("src"))
        eModal.iframe({
            title: 'Edit Employee Photo',
            url: 'edit-photo.php?imgurl=' + imgurl + '&employeeId=<?php echo $id; ?>',
            size: eModal.size.xl,
        });
    }

    function mask(str, textbox, loc, delim) {
        var x = event.which || event.keyCode;
        if (x != 8) {
            var locs = loc.split(',');
            for (var i = 0; i <= locs.length; i++) {
                for (var k = 0; k <= str.length; k++) {
                    if (k == locs[i]) {
                        if (str.substring(k, k + 1) != delim) {
                            str = str.substring(0, k) + delim + str.substring(k, str.length)
                        }

                    }

                }

            }
            textbox.value = str
        }
    }

    function openTechPermissions() {
        showLoader();
        ds = "t=checkrec&shopid=<?php echo $shopid; ?>&empid=<?php echo $id; ?>"
        $.ajax({

            url: "employee-action.php",
            data: ds,
            type: "post",
            success: function (r) {
                if (r == "success") {
                    $('#techpermissionsmodal').modal('show')
                }
                hideLoader();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
                hideLoader();
            }

        });


    }

    function setMode_p(v) {

        if (v.toLowerCase() === "yes") {
            $('#techperm').show()
        } else {
            $('#techperm').hide()
        }

    }

    function save(id) {

        if ($('#' + id).is(':checked')) {
            ds = "t=checkbox&shopid=<?php echo $shopid; ?>&id=" + id + "&val=yes&empid=<?php echo $id; ?>"
        } else {
            ds = "t=checkbox&shopid=<?php echo $shopid; ?>&id=" + id + "&val=no&empid=<?php echo $id; ?>"
        }

        console.log(ds)
        $.ajax({
            type: "post",
            data: ds,
            url: "employee-action.php",
            success: function (r) {
                //console.log(r)
            }
        });

    }

    function saveTech(id) {

        if ($('#' + id).is(':checked')) {
            ds = "t=checkboxtech&shopid=<?php echo $shopid; ?>&id=" + id + "&val=yes&empid=<?php echo $id; ?>"
        } else {
            ds = "t=checkboxtech&shopid=<?php echo $shopid; ?>&id=" + id + "&val=no&empid=<?php echo $id; ?>"
        }

        console.log(ds)
        $.ajax({
            type: "post",
            data: ds,
            url: "employee-action.php",
            success: function (r) {
                console.log(r)
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }

        });
    }

    function isDate(dateVal) {
        var d = new Date(dateVal);
        return d.toString() === 'Invalid Date' ? false : true;
    }

    function saveAll() {
        fn = $('#first').val()
        ln = $('#last').val()

        if (fn.length == 0 || ln.length == 0) {
            sbalert("First and Last name are required fields")
        } else {
            //console.log(isDate($('#datehired').val()))
            ds = "id=<?php echo $id; ?>&t=emp&shopid=<?php echo $shopid; ?>&" + $('#mainform').serialize()
            if (!isDate($('#datehired').val())) {
                sbalert("Please provide a valid date hired")
            } else {
                console.log(ds)
                $.ajax({
                    data: ds,
                    type: "post",
                    url: "employee-action.php",
                    success: function (r) {
                            sbconfirm("Changes Saved", "Employee changes saved successfully", function () {
                                    location.href = '<?= COMPONENTS_PRIVATE ?>/v2/settings/employees.php'
                                }
                            )
                        }
                });
            }
        }
    }

    function assignCustomers() {
        eModal.iframe({
            title: 'Assign Customers',
            url: 'employee-customers.php?shopid=<?php echo $shopid; ?>&empid=<?php echo $id; ?>',
            size: eModal.size.xl,
        });
    }

    function loginAsEmployee()
    {
        sbconfirm("Confirmation","Do you really want to login as this employee?",function(){
            
            showLoader();
            $('#loginform').submit();
        })
    }

    function changePassword()
    {
        var password = $('#password').val()
        var cpassword = $('#cpassword').val()

        if(password != cpassword)
        {
            sbalert("Passwords do not match")
            return
        }

        $('#btn-password').attr('disabled','disabled')
        showLoader()

        $.ajax({
            data: "id=<?php echo $id; ?>&t=changepassword&shopid=<?php echo $shopid; ?>&password=" + password,
            type: "post",
            url: "employee-action.php",
            success: function (r) {
                sbalert(r)
                $('#passwordmodal').modal('hide')
                $('#btn-password').attr('disabled',false)
                hideLoader()
            }
        });
    }

    $(document).ready(function () {

        $("#colorpicker").spectrum({
            allowEmpty: true,
            showInput: true,
            preferredFormat: "hex",
            color: "<?= $schcolor ?>"
        })
    });
</script>

</body>
</html>
<?php
mysqli_close($conn);
?>
