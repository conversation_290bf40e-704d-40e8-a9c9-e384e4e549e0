<?php

require(CONN);

$photo = $_POST['photo'];
$employeeId = $_POST['employeeId'];
$shopid = $_COOKIE['shopid'];

$stmt = "update employees set photo = '' where shopid = ? and id = ?";

if ($query = $conn->prepare($stmt)) {
    $query->bind_param("ii", $shopid, $employeeId);
    if ($query->execute()) {
        $conn->commit();
    }
    $query->close();
    echo "success";

    // Delete file
    $file = "//fs.shopboss.aws/share/upload/" . $shopid . "/" . $photo;
    if (file_exists($file)) {
        unlink($file);
    }
} else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}
