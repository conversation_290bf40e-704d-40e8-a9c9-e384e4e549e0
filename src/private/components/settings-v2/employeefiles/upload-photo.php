<?php

include INTEGRATIONS_PATH . "/sbp_bucket/sbp_bucket.php";
require CONN;
$shopid = $_COOKIE['shopid'];
$employeeId = $_REQUEST['employeeId'];

$target_path = "//fs.shopboss.aws/share/upload/" . $shopid . "/";
$target_dir = realpath($target_path);

if (!$target_dir) {
    mkdir("//fs.shopboss.aws/share/upload/" . $shopid);
}

    $fname = "$shopid/Employees/$employeeId/" . basename($_FILES['file']['name']);
    if ($sbp_bucket->add_file(file_get_contents($_FILES['file']['tmp_name']), $fname, $_FILES['file']['type'])) {
        $stmt = "update employees set photo = ? where shopid = ? and id = ?";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("sii", $fname, $shopid, $employeeId);
            if ($query->execute()) {
                $conn->commit();
                echo "success";
            } else {
                echo $conn->errno;
            }

            $query->close();
        } else {
            echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }
    }

if (isset($conn)) {
    mysqli_close($conn);
}
