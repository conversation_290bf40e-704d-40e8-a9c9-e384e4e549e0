<?php
ini_set('max_execution_time', '300'); //300 seconds = 5 minutes
require CONN;
require(INTEGRATIONS_PATH . "/apination/closerowebhook.php");
require(INTEGRATIONS_PATH . "/backoffice/closerowebhook.php");


$shopid = $_POST['shopid'];
$roids = $_POST['roids'];
$status = !empty($_POST['status']) ? $_POST['status'] : '';
$rotype = !empty($_POST['rotype']) ? $_POST['rotype'] : '';
$tech = !empty($_POST['tech']) ? $_POST['tech'] : '';
$techid = !empty($_POST['techid']) ? $_POST['techid'] : '';
$techrate = !empty($_POST['techrate']) ? $_POST['techrate'] : '';
$success = false;
// check for multiple roids
if (strpos($roids, "|") > 0) {
    $rar = explode("|", $roids);
    if (!empty($status)) {
        if ($status == 'close') {
            $stmt = "select lower(requirepayments) from company where shopid = '$shopid'";
            if ($query = $conn->prepare($stmt)) {
                $query->execute();
                $query->bind_result($requirepayments);
                $query->fetch();
                $query->close();
            }
        }

        $stmt = "insert into servertracking (shopid,page) values ('$shopid','Mass Update Used')";
        if ($query = $conn->prepare($stmt)) {
            $query->execute();
            $conn->commit();
            $query->close();
        }

        // backoffice
        $backofficeapikey = "";
        $stmt = "select `locid` from backofficekeys where shopid = '$shopid' and active='yes'";
        if ($query = $conn->prepare($stmt)) {
            $query->execute();
            $query->bind_result($backofficeapikey);
            $query->fetch();
            $query->close();
        }

        foreach ($rar as $v) {

            if (strlen(trim($v)) > 1) {
                $roid = $v;
                if ($status == "close") {

                    if ($requirepayments == 'yes') {
                        $totalpayments = 0;
                        $pmtstmt = "select coalesce(sum(amt),0) tamt from accountpayments where shopid = ? and roid = ?";
                        if ($squery = $conn->prepare($pmtstmt)) {
                            $squery->bind_param("si", $shopid, $roid);
                            $squery->execute();
                            $squery->store_result();
                            $squery->bind_result($totalpayments);
                            $squery->fetch();
                            $pmtfetch = "none";
                        }

                        $totalpayments = sbpround($totalpayments, 2);

                        if ($totalpayments == 0) continue;
                    }

                    // get the overridestatusdate
                    $stmt = "select overridestatusdate from repairorders where shopid = '$shopid' and roid = $roid";
                    if ($query = $conn->prepare($stmt)) {
                        $query->execute();
                        $query->bind_result($overridestatusdate);
                        $query->fetch();
                        $query->close();
                    }

                    $currtimestamp = localTimeStamp($shopid);
                    $statusdate = date("Y-m-d");
                    if (strtolower($overridestatusdate) == "yes") {
                        $stmt = "update repairorders set status = 'CLOSED' where shopid = '$shopid' and roid = $roid";
                    } else {
                        $stmt = "update repairorders set updatesent = '$currtimestamp', status = 'CLOSED', statusdate = '$statusdate', finaldate = '$statusdate' where shopid = '$shopid' and roid = $roid";
                    }
                    if ($query = $conn->prepare($stmt)) {
                        if ($query->execute()) {
                            $conn->commit();
                            recordAudit("Closed RO", "Closed RO#$roid from Mass Update");

                        } else {
                            echo $conn->errno;
                        }
                    } else {
                        echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    APINationRO($roid);

                    if ($backofficeapikey != "")
                        BackofficeRO($roid);


                    $useaccounting = $_COOKIE['useaccounting'];
                    if ($useaccounting == "yes") {
                        // remove all previous parts expenses, then post new amounts
                        $stmt = "delete from unpostedexpenses where shopid = '$shopid' and roid = $roid";
                        if ($query = $conn->prepare($stmt)) {
                            if ($query->execute()) {
                                $conn->commit();
                            } else {
                                echo $conn->errno;
                            }
                        } else {
                            echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }


                    }

                    $stmt = "select `shopid`,`PartID`,`PartNumber`,`PartDesc`,`PartPrice`,`Quantity`,`ROID`,`Supplier`,`Cost`,`PartInvoiceNumber`,`PartCode`,`LineTTLPrice`,`LineTTLCost`,`Date`,`PartCategory`,`complaintid`,`discount`,`net`,`bin`,`tax`,`overridematrix`,`posted`,`scheduledreceivedate`,`ponumber`,`allocated`,`received`,`updated`,`displayorder`,`pstatus`,`deleted`,`datedone`,`salesperson` from parts where deleted = 'no' and shopid = '$shopid' and ROID = $roid";
                    if ($query = $conn->prepare($stmt)) {
                        $query->execute();
                        $result = $query->get_result();
                        while ($row = $result->fetch_assoc()) {

                            // post the parts cost to the unpostedexpenses table
                            if ($useaccounting = "yes" && strtolower($row['posted']) <> "yes") {
                                $supplier = strtoupper(str_replace("'", "''", $row['Supplier']));
                                $linettlcost = $row['LineTTLCost'];
                                $solddate = date("Y-m-d");
                                $partid = $row['PartID'];
                                $istmt = "insert into unpostedexpenses (shopid,paidto,amount,category,memo,udate,roid,partid) values ('$shopid','$supplier',$linettlcost,'Cost of Goods Sold',"
                                    . "'Parts cost for RO# $roid', '$solddate',$roid,$partid)";
                                if ($iquery = $conn->prepare($istmt)) {
                                    if ($iquery->execute()) {
                                        $conn->commit();
                                    } else {
                                        echo $conn->errno;
                                    }
                                } else {
                                    echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                }
                            }

                        }
                    }

                    // check for podium
                    $podiumcount = 0;
                    $stmt = "select count(*) c from apilogin where companyname = 'podium' and shopid = '$shopid'";
                    if ($query = $conn->prepare($stmt)) {

                        $query->execute();
                        $query->bind_result($podiumcount);
                        $query->fetch();
                        $query->close();


                    }

                    $stmt = "select customerid from repairorders where shopid = '$shopid' and roid = $roid";
                    if ($query = $conn->prepare($stmt)) {

                        $query->execute();
                        $query->bind_result($customerid);
                        $query->fetch();
                        $query->close();

                    }

                    $stmt = "select follow from customer where shopid = '$shopid' and customerid = $customerid";
                    if ($query = $conn->prepare($stmt)) {

                        $query->execute();
                        $query->bind_result($follow);
                        $query->fetch();
                        $query->close();

                    }


                    if ($podiumcount > 0 && strtolower($follow) == "yes") {

                        $stmt = "select customerfirst,customerlast,email,customerphone,customerwork,cellphone from repairorders where shopid = '$shopid' and roid = $roid";
                        //echo $stmt;
                        if ($query = $conn->prepare($stmt)) {
                            $query->execute();
                            $query->bind_result($customerfirst, $customerlast, $email, $homephone, $workphone, $cellphone);
                            $query->fetch();
                            $query->close();
                        }

                        $stmt = "select apikey,username from apilogin where shopid = '$shopid' and companyname = 'podium'";
                        if ($query = $conn->prepare($stmt)) {
                            $query->execute();
                            $query->bind_result($locationid, $token);
                            $query->fetch();
                            $query->close();
                        }

                        $token = '"' . $token . '"';

                        $stmt = "select companyemail from company where shopid = '$shopid'";
                        if ($query = $conn->prepare($stmt)) {
                            $query->execute();
                            $query->bind_result($shopemail);
                            $query->fetch();
                            $query->close();
                        }

                        $phonetouse = "";
                        if (strlen($cellphone) > 0) {
                            $phonetouse = $cellphone;
                        } else {
                            $phonetouse = "";
                            if (strlen($homephone) > 0) {
                                $phonetouse = $homephone;
                            } else {
                                $phonetouse = "";
                                if (strlen($workphone) > 0) {
                                    $phonetouse = $workphone;
                                } else {
                                    $phonetouse = "";
                                }
                            }
                        }


                        $json = array();
                        $json['locationId'] = $locationid;
                        $json['phoneNumber'] = $phonetouse;
                        $json['email'] = $email;
                        $json['firstName'] = $customerfirst;
                        $json['lastName'] = $customerlast;
                        $json['senderEmail'] = $shopemail;
                        //$json['test'] = "true";

                        $json = json_encode($json);

                        $curl = curl_init();
                        $base = "https://platform.podium.com/api/v2/review_invitations";
                        curl_setopt_array($curl, array(
                            CURLOPT_URL => $base,
                            CURLOPT_POST => 1,
                            CURLOPT_CUSTOMREQUEST => "POST",
                            CURLOPT_POSTFIELDS => $json,
                            CURLOPT_RETURNTRANSFER => 1,
                            CURLOPT_SSL_VERIFYHOST => 0, // to avoid SSL issues if you need to fetch from https
                            CURLOPT_SSL_VERIFYPEER => 0, // same ^
                        ));
                        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                            'Content-Type: application/json',
                            'Accept: application/json',
                            'Authorization: ' . $token
                        ));

                        $jsonr = json_decode(curl_exec($curl));

                    }

                    // test for AutoPilothq.com integration
                    $stmt = "select apikey from apilogin where shopid = '$shopid' and companyname = 'autopilot'";
                    if ($query = $conn->prepare($stmt)) {

                        $query->execute();
                        $query->bind_result($apikey);
                        $query->fetch();
                        $query->close();

                    } else {
                        $apikey = "";
                    }

                    if ($apikey != "") {
                        // now get the customer info
                        //echo "sending to autopilot";
                        $stmt = "select customerid,customerlast,customerfirst,customeraddress,customercity,customerstate,customerzip,customerphone,cellphone,email,vehyear,vehmake,vehmodel,vin,vehlicense,vehengine,milesout,customvehicle1,customvehicle1label,customvehicle2,customvehicle2label,customvehicle3,customvehicle3label,customvehicle4,customvehicle4label,customvehicle5,customvehicle5label,customvehicle6,customvehicle6label,customvehicle7,customvehicle7label,customvehicle8,customvehicle8label from repairorders where shopid = '$shopid' and roid = $roid";
                        if ($query = $conn->prepare($stmt)) {

                            $query->execute();
                            $query->bind_result($customerid, $lastname, $firstname, $customeraddress, $customercity, $customerstate, $customerzip, $customerphone, $cellphone, $email, $vehyear, $vehmake, $vehmodel, $vin, $vehlicense, $vehengine, $milesout, $customvehicle1, $customvehicle1label, $customvehicle2, $customvehicle2label, $customvehicle3, $customvehicle3label, $customvehicle4, $customvehicle4label, $customvehicle5, $customvehicle5label, $customvehicle6, $customvehicle6label, $customvehicle7, $customvehicle7label, $customvehicle8, $customvehicle8label);
                            $query->fetch();
                            $query->close();

                        }

                        $stmt = "select userdefined1,userdefined2,userdefined3 from customer where customerid = $customerid and shopid = '$shopid'";
                        if ($query = $conn->prepare($stmt)) {

                            $query->execute();
                            $query->bind_result($userdefined1, $userdefined2, $userdefined3);
                            $query->fetch();
                            $query->close();

                        }

                        $stmt = "select customuserfield1,customuserfield2,customuserfield3 from company where shopid = '$shopid'";
                        //echo $stmt;
                        if ($query = $conn->prepare($stmt)) {

                            $query->execute();
                            $query->bind_result($customuserfield1, $customuserfield2, $customuserfield3);
                            $query->fetch();
                            $query->close();

                        }

                        $endpoint = "https://api2.autopilothq.com/v1/trigger/0002/contact";
                        $headers[] = "autopilotapikey: " . $apikey;
                        $headers[] = "Content-Type: application/json";

                        $vehstr = "";

                        if (strlen($customvehicle1label) > 0) {
                            $vehstr .= '"string--' . str_replace(" ", "--", str_replace("\t", "", $customvehicle1label)) . '": "' . str_replace("\t", "", $customvehicle1) . '",';
                        }
                        if (strlen($customvehicle2label) > 0) {
                            $vehstr .= '"string--' . str_replace(" ", "--", str_replace("\t", "", $customvehicle2label)) . '": "' . str_replace("\t", "", $customvehicle2) . '",';
                        }
                        if (strlen($customvehicle3label) > 0) {
                            $vehstr .= '"string--' . str_replace(" ", "--", str_replace("\t", "", $customvehicle3label)) . '": "' . str_replace("\t", "", $customvehicle3) . '",';
                        }
                        if (strlen($customvehicle4label) > 0) {
                            $vehstr .= '"string--' . str_replace(" ", "--", str_replace("\t", "", $customvehicle4label)) . '": "' . str_replace("\t", "", $customvehicle4) . '",';
                        }
                        if (strlen($customvehicle5label) > 0) {
                            $vehstr .= '"string--' . str_replace(" ", "--", str_replace("\t", "", $customvehicle5label)) . '": "' . str_replace("\t", "", $customvehicle5) . '",';
                        }
                        if (strlen($customvehicle6label) > 0) {
                            $vehstr .= '"string--' . str_replace(" ", "--", str_replace("\t", "", $customvehicle6label)) . '": "' . str_replace("\t", "", $customvehicle6) . '",';
                        }
                        if (strlen($customvehicle7label) > 0) {
                            $vehstr .= '"string--' . str_replace(" ", "--", str_replace("\t", "", $customvehicle7label)) . '": "' . str_replace("\t", "", $customvehicle7) . '",';
                        }
                        if (strlen($customvehicle8label) > 0) {
                            $vehstr .= '"string--' . str_replace(" ", "--", str_replace("\t", "", $customvehicle8label)) . '": "' . str_replace("\t", "", $customvehicle8) . '",';
                        }
                        if (strlen($customuserfield1) > 0) {
                            $vehstr .= '"string--' . str_replace(" ", "--", str_replace("\t", "", $customuserfield1)) . '": "' . str_replace("\t", "", $userdefined1) . '",';
                        }
                        if (strlen($customuserfield2) > 0) {
                            $vehstr .= '"string--' . str_replace(" ", "--", str_replace("\t", "", $customuserfield2)) . '": "' . str_replace("\t", "", $userdefined2) . '",';
                        }
                        if (strlen($customuserfield3) > 0) {
                            $vehstr .= '"string--' . str_replace(" ", "--", str_replace("\t", "", $customuserfield3)) . '": "' . str_replace("\t", "", $userdefined3) . '",';
                        }
                        //echo $vehstr."\r\n";
                        if (substr($vehstr, -1) == ",") {
                            $vehstr = "," . substr($vehstr, 0, strlen($vehstr) - 1);
                        }

                        $params = '{
						"contact": {
							"FirstName": "' . str_replace("\t", "", $lastname) . '",
							"LastName": "' . str_replace("\t", "", $firstname) . '",
							"Email": "' . str_replace("\t", "", $email) . '",
							"MailingStreet": "' . str_replace("\t", "", $customeraddress) . '",
							"MailingCity": "' . str_replace("\t", "", $customercity) . '",
							"MailingState": "' . str_replace("\t", "", $customerstate) . '",
							"MailingPostalCode": "' . str_replace("\t", "", $customerzip) . '",
							"Phone": "' . str_replace("\t", "", $customerphone) . '",
							"MobilePhone": "' . str_replace("\t", "", $cellphone) . '",
						    "custom": {
						    	"string--Year": "' . str_replace("\t", "", $vehyear) . '",
						    	"string--Make": "' . str_replace("\t", "", $vehmake) . '",
						    	"string--Model": "' . str_replace("\t", "", $vehmodel) . '",
						    	"string--VIN": "' . str_replace("\t", "", $vin) . '",
						    	"string--License": "' . str_replace("\t", "", $vehlicense) . '",
						    	"string--Engine": "' . str_replace("\t", "", $vehengine) . '",
						    	"string--Miles--Out": "' . str_replace("\t", "", $milesout) . '"
						    	' . $vehstr . '
						    }
		    			}
					}';


                        //echo $params."\r\n";
                        $curl = curl_init();
                        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
                        curl_setopt_array($curl, array(
                            CURLOPT_URL => $endpoint,
                            CURLOPT_POST => 1,
                            CURLOPT_POSTFIELDS => $params,
                            CURLOPT_RETURNTRANSFER => 1,
                            CURLOPT_SSL_VERIFYHOST => 0,
                            CURLOPT_SSL_VERIFYPEER => 0
                        ));

                        $result = curl_exec($curl);
                        //echo $result."\r\n";
                    }

                    // test for referralrock.com integration
                    $apikey = "";
                    $stmt = "select apikey from apilogin where shopid = '$shopid' and companyname = 'referralrock'";
                    if ($query = $conn->prepare($stmt)) {

                        $query->execute();
                        $query->bind_result($apikey);
                        $query->fetch();
                        $query->close();

                    } else {
                        $apikey = "";
                    }

                    if ($apikey != "") {

                        $stmt = "select customerid,customerlast,customerfirst,cellphone,email,totalro from repairorders where shopid = '$shopid' and roid = $roid";
                        if ($query = $conn->prepare($stmt)) {

                            $query->execute();
                            $query->bind_result($customerid, $lastname, $firstname, $cellphone, $email, $totalro);
                            $query->fetch();
                            $query->close();

                        }

                        $totalro = number_format($totalro, 2, ".", "");
                        $headers = "";
                        $endpoint = "https://api.referralrock.com/api/referral/update";
                        $headers[] = "Authorization: Basic " . $apikey;
                        $headers[] = "Content-Type: application/json";

                        //print_r($headers);

                        $json = '[{
					    "query": {
					      "fuzzyInfo": {
					        "Identifier": "' . $email . '"
					      }
					    },
					    "referral": {
					      "firstName": "' . $firstname . '",
					      "lastName": "' . $lastname . '",
					      "email": "' . $email . '",
					      "phoneNumber": "' . $cellphone . '",
					      "externalIdentifier": "' . $customerid . '",
					      "amount": ' . $totalro . ',
					      "status": "approved"
					    }
					  }]';

                        //echo $json."\r\n";
                        $curl = curl_init();
                        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
                        curl_setopt_array($curl, array(
                            CURLOPT_URL => $endpoint,
                            CURLOPT_POST => 1,
                            CURLOPT_POSTFIELDS => $json,
                            CURLOPT_RETURNTRANSFER => 1,
                            CURLOPT_SSL_VERIFYHOST => 0,
                            CURLOPT_SSL_VERIFYPEER => 0
                        ));

                        $result = curl_exec($curl);
                        //echo $result;
                    }


                } elseif ($status == "final") {
                    $sdate = date("Y-m-d");
                    $status = "FINAL";
                    $stmt = "update repairorders set status = ?, finaldate = '$sdate', datefinal = '$sdate', statusdate = '$sdate' where shopid = ? and roid = ?";

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("ssi", $status, $shopid, $roid);
                        if ($query->execute()) {
                            $conn->commit();
                            $query->close();
                            recordAudit("ROStatusChange", "Status changed to $status on RO#$roid from Mass Update");
                        } else {
                            echo $conn->errno;
                            $query->close();
                        }

                    } else {
                        echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }


                } else {
                    $assigned_status = ucwords($status);
                    $stmt = "update repairorders set status = ? where shopid = ? and roid = ?";
                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("ssi", $assigned_status, $shopid, $roid);
                        if ($query->execute()) {
                            $conn->commit();
                            $query->close();
                            //echo "success";
                            recordAudit("ROStatusChange", "RO Status set to $assigned_status on RO# $roid from Mass Update");
                        } else {
                            echo $conn->errno;
                            $query->close();
                        }

                    } else {
                        echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }
                }

            }

        }
        $success = true;
    }
    if (!empty($rotype)) {

        foreach ($rar as $v) {
            if (strlen($v) > 1) {
                $roid = $v;
                $assigned_rotype = ucwords($rotype);
                $stmt = "update repairorders set rotype = ? where shopid = ? and roid = ?";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("ssi", $assigned_rotype, $shopid, $roid);
                    if ($query->execute()) {
                        $conn->commit();
                        $query->close();
                        //echo "success";
                        recordAudit("ROTypeNoApproval", "RO Type set to $assigned_rotype on RO# $roid from Mass Update");
                    } else {
                        echo $conn->errno;
                        $query->close();
                    }

                } else {
                    echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }
            }
        }

        $success = true;
    }
    if (!empty($techid) && !empty($tech)) {
        foreach ($rar as $v) {
            if (strlen($v) > 1) {
                $roid = $v;
                $stmt = "update labor set techrate = ?, tech = ?, laborop = ? where shopid = ? and roid = ? and tech != 'DISCOUNT, DISCOUNT'";
                if ($query = $conn->prepare($stmt)) {

                    $query->bind_param("dsssi", $techrate, $tech, $techid, $shopid, $roid);
                    $query->execute();
                    $conn->commit();
                    $query->close();

                }
                $stmt = "update repairorders set DefaultTech = ? where shopid = ? and roid = ?";
                if ($query = $conn->prepare($stmt)) {

                    $query->bind_param("ssi", $tech, $shopid, $roid);
                    $query->execute();
                    $conn->commit();
                    $query->close();

                }
            }
        }
        $success = true;
    }
    echo $success ? "success" : "error";
} else {
    echo "No RO's Selected";
}
