<script defer src="<?= SCRIPT; ?>/core/js.cookie.min.js"></script>
<script defer src="//cdnjs.cloudflare.com/ajax/libs/annyang/2.6.0/annyang.js"></script>
<script src="<?= SCRIPT ?>/ion.sound.min.js"></script>
<?php include(AI_WRITING_TOOL) ?>
<script>
    storednotifications = ''
    workflowWindow = ''

    jQuery(function () {
        // Init page helpers (Slick Slider plugin)

        if (document.getElementById('myshopnotice')) {
            var editor = document.getElementById('myshopnotice');
            editor.isContentEditable;
            editor.contentEditable = true;
        }

        checkSMS()

        <?php if ($empid != "Admin") { ?>
        checkPassChange()
        <?php } ?>

        <?php
        // Initialize usernamecheck to prevent undefined variable notice
        $usernamecheck = isset($usernamecheck) ? $usernamecheck : "no";

        if ($usernamecheck == "yes" && empty($username)) { ?>
        $('#usernamemodal').modal('show')
        <?php } ?>

        $('#newreminder').keyup(function () {
            maxchar = 300
            thischar = $(this).val().length
            remchar = maxchar - thischar
            $('#maxchar').html(remchar + " remaining")
        });

        <?php
        if ($shopid == "6263") {
        ?>
        if (annyang) {

            var commands = {
                '*command': alfredCommands,
            }

            // Add our commands to annyang
            annyang.addCommands(commands);

            annyang.start();

        }
        <?php
        }
        ?>

    });




    function openWorkflow() {

     if (workflowWindow && !workflowWindow.closed && workflowWindow.location)
     workflowWindow.focus()
     else
     {
        workflowWindow = window.open("<?= COMPONENTS_PRIVATE ?>/v2/workflow/board.php","_blank");
     }

    }

    function dismissAllRemotePayments() {

        $.ajax({
            data: "t=dismissall&shopid=<?php echo $shopid; ?>",
            url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/getpmts.php",
            type: "post",
            success: function (r) {
                $('#pmtbody').html('')
                $('#pmtmodal').modal('hide')

            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });


    }

    function hideMsg(id) {

        $.ajax({
            data: "id=" + id,
            url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/shopalerts.php",
            type: "post",
            success: function (r) {
                $('#shopmsg' + id).hide()
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        })

    }

    function apptResponse(id, email) {
        $('#appointment_id').val(id)
        if (email.length > 0) {
            $('#emailmessageaddress').val(email)
            $('#apptmodal').modal('hide')
            $('#confirmapptmodal').modal('show')
        } else {
            sbalert("Email address not supplied so you cannot respond to the appointment request")
        }
    }

    function sendEmailMessage() {

        if ($('#emailmodal').css("display") == "none") {
            $('#emailmodal').modal('show')
        } else {
            email = $('#emailmessageaddress').val()
            msg = $('#emailmessagemessage').val()
            subj = $('#emailmessagesubject').val()
            if (email.length >= 1 && msg.length > 0 && subj.length > 0) {

                $('.btn-md').attr('disabled', 'disabled')

                $.ajax({
                    data: "shopid=<?php echo $shopid; ?>&roid=1234&t=sendemail&email=" + email + "&subj=" + subj + "&msg=" + msg,
                    type: "post",
                    url: "<?= COMPONENTS_PRIVATE ?>/ro/saveData.php",
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    },
                    success: function (r) {
                        var aid = $('#appointment_id').val()
                        $.ajax({
                            data: "t=dismissappt&shopid=<?php echo $shopid; ?>&id=" + aid,
                            url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/getnewappts.php",
                            type: "post",
                            success: function (r) {
                                if (r == "success") {

                                }
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                console.log(xhr.status);
                                console.log(xhr.responseText);
                                console.log(thrownError);
                            }
                        });

                        if (r == "success") {
                            sbalert("Email Message Sent")
                            $('#confirmapptmodal').modal('hide')
                        }

                        $('.btn-md').attr('disabled', false)
                    }
                });
            } else {
                sbalert("You must enter an email, subject and message")
                hideLoader()
            }

        }
    }


    function alfredCommands(command) {

        command = command.toLowerCase()

        //console.log(command)
        if (command == "alfred help") {
            $('#alfredmodal').modal('show')
        }
        if (command == "create repair order") {
            top.location.href = "<?= COMPONENTS_PRIVATE ?>/customer/customer-search.php"
        }
        if (command == "whip") {
            top.location.href = "<?= COMPONENTS_PRIVATE ?>/v2/wip/wip-v2.php"
        }
        if (command == "cancel help") {
            $('#alfredmodal').modal('hide')
        }
        if (command == "clear search") {
            wip.contentWindow.document.getElementById("srch").value = ""
            srchtext = ""
            wip.contentWindow.searchTable(srchtext)
            wip.contentWindow.searchClosed(srchtext)
        }
        if (command == "manage customers") {
            top.location.href = "<?= COMPONENTS_PRIVATE ?>/customer/customer-find.php"
        }
        if (command.indexOf("find repair order") >= 0) {
            rar = command.split("order")
            srchtext = rar[1]
            srchtext = srchtext.trim()
            wip = document.getElementById("myframe")
            wip.contentWindow.document.getElementById("srch").value = srchtext
            showLoader()
            wip.contentWindow.searchTable(srchtext)
            wip.contentWindow.searchClosed(srchtext)
            hideLoader()
        }
        if (command.indexOf("open repair order") >= 0 || command.indexOf("open ro") >= 0) {
            if (command.indexOf("order") >= 0) {
                rar = command.split("order")
            } else if (command.indexOf("ro") >= 0) {
                rar = command.split("ro")
            }
            srchtext = rar[1]
            srchtext = srchtext.trim()
            srchtext = srchtext.replace(":", "")
            // check to see if it is a valid ro
            $.ajax({
                data: "t=findro&shopid=<?php echo $shopid; ?>&roid=" + srchtext,
                url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/alfred.php",
                type: "post",
                success: function (r) {
                    if (r == "found") {
                        top.location.href = "<?= COMPONENTS_PRIVATE ?>/ro/ro.php?roid=" + srchtext
                    } else {
                        sbalert("RO " + srchtext + " was not found")
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });
        }

    }


    function saveReminder() {

        r = encodeURIComponent($('#newreminder').val())
        ts = $('#newreminderdate').val()

        if (r.length > 0 && ts.length > 0) {
            $('.btn').attr('disabled', 'disabled')
            $.ajax({
                data: "t=addreminder&shopid=<?php echo $shopid; ?>&r=" + r + "&empid=<?php echo $empid; ?>&ts=" + ts,
                url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/checkreminders.php",
                type: "post",
                success: function (r) {
                    console.log(r)
                    if (r == "success") {
                        changeIconColor()
                        $('#newreminder').val('')
                        $('#newreminderdate').val('')
                        $('#newreminderdate').val('<?= date('m/d/Y')?> 12:00 AM').blur()
                    }
                    $('#addremindermodal').modal('hide')
                    $('.btn').attr('disabled', false)
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });
        } else {
            sbalert("You must enter something in the Reminder box and select a date and time")
        }
    }

    var remrotate

    function changeIconColor() {

        showLoader()

        $.ajax({
            data: "t=getreminders&empid=<?php echo $empid; ?>&shopid=<?php echo $shopid; ?>",
            url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/checkreminders.php",
            type: "post",
            success: function (r) {
                clearInterval(remrotate)
                hideLoader()
                if (r == "none" || r == "") {
                    $('#reminderlist').html("<p class='text-center'>You have no Personal Reminders on your list.  Click below to add one</p>")
                    $('#remindermodal').modal('show')
                } else {
                    $('#reminderlist').html(r)
                    $('#remindermodal').modal('show')
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        })
    }

    function markDone(id) {

        sbconfirm("Mark Reminders","You cannot recover this.  You will need to recreate it. Are you sure you want to mark it Done?",function()
        {

                $.ajax({
                    data: "t=markdone&shopid=<?php echo $shopid; ?>&id=" + id,
                    url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/checkreminders.php",
                    type: "post",
                    success: function (r) {
                        $.ajax({
                            data: "t=getreminders&empid=<?php echo $empid; ?>&shopid=<?php echo $shopid; ?>",
                            url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/checkreminders.php",
                            type: "post",
                            success: function (r) {
                                if (r == "none") {
                                    $('#reminderlist').html("<p class='text-center'>You have no Personal Reminders on your list.  Click below to add one</p>")
                                    $('#reminderbutton').css("color", "inherit")
                                } else {
                                    $('#reminderlist').html(r)
                                }
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                console.log(xhr.status);
                                console.log(xhr.responseText);
                                console.log(thrownError);
                            }
                        })

                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    }
                })
        })

    }

    function openTechMode(type) {

        if(type == 'old')
        {
            var uri = '<?= $_SERVER['SERVER_NAME'] ?>';
            top.location.href = 'https://' + (uri.indexOf("staging") > -1 ? 'staging.' : (uri.indexOf("alpha") > -1 ? 'alpha.' : '')) + 'tech.' + (uri.indexOf("matco") > -1 ? 'matcosms' : (uri.indexOf("protractorgo") > -1 ? 'protractorgo' : 'shopbosspro')) + '.com/wip.asp?empid=<?php echo $_COOKIE['empid']; ?>&shopname=<?php echo urlencode($_COOKIE['shopname']); ?>&shopid=<?php echo urlencode($_COOKIE['shopid']); ?>&login=<?php echo urlencode($_COOKIE['username']); ?>&mode=<?php echo urlencode($_COOKIE['mode']); ?>'
        }

        else if(type == 'new')
        {
            showLoader()
            $.ajax({
                data: "t=techmode",
                url: "<?= COMPONENTS_PRIVATE ?>/shared/changemode.php",
                type: "post",
                success: function (r) {
                   setTimeout(function(){location.reload()},2000)
                }
            });
        }

    }

    function showIM() {

        showLoader()

        $('#message_badge').html('')
        $.ajax({
            data: "t=getmsg&shopid=<?php echo $shopid; ?>&usr=<?php echo addslashes($usr); ?>",
            url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/chatinfo.php",
            type: "post",
            success: function (r) {
                $('#imlist').html(r)
                $('#immodal').modal('show')
                setTimeout(function () {
                    $("#imlist").scrollTop($("#imlist")[0].scrollHeight)
                }, 200)
                hideLoader()
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });
    }

    function sendMessage() {
        shopid = "<?php echo $shopid; ?>";
        mto = $('#emps').val();
        msg = encodeURIComponent($('#msgboxsend').val())
        usr = encodeURIComponent("<?php echo addslashes($usr); ?>");
        ds = "t=sendmsg&shopid=" + shopid + "&usr=" + usr + "&to=" + mto + "&msg=" + msg
        if (mto != "none" && msg != '') {

            $('.btn-md').attr('disabled', 'disabled')
            $.ajax({
                data: ds,
                url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/chatinfo.php",
                type: "post",
                success: function (r) {
                    $.ajax({
                        data: "t=getmsg&shopid=<?php echo $shopid; ?>&usr=" + usr,
                        url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/chatinfo.php",
                        type: "post",
                        success: function (r) {
                            $('#imlist').html(r)
                            $('#msgboxsend').val('')
                            setTimeout(function () {
                                $("#imlist").scrollTop($("#imlist")[0].scrollHeight)
                            }, 200)
                            $('.btn-md').attr('disabled', false)
                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            console.log(xhr.status);
                            console.log(xhr.responseText);
                            console.log(thrownError);
                        }
                    });
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });
        } else {
            sbalert("You must type a message AND select someone to send the message to")
        }
    }

    function alertActions(p, t) {

        eModal.iframe({
            title: t,
            url: p,
            size: eModal.size.xl,
            buttons: [{
                text: 'Close',
                style: 'warning',
                close: true
            }]
        });


    }

    function extendedKPIs() {

        $('#techaction').css("z-index", "999");
        eModal.iframe({
            title: "Extented KPI's",
            url: "<?= COMPONENTS_PRIVATE ?>/v2/reports/goals.php?m=<?php echo date("Y-m-d", strtotime("first day of this month")); ?>&template=no",
            size: eModal.size.xl
        });

    }

    function extendedPPH() {
        $('#techaction').css("z-index", "999");
        eModal.iframe({
            title: "PPH Net Variance",
            url: "<?= COMPONENTS_PRIVATE ?>/v2/settings/pb_variance.php",
            size: eModal.size.xl
        })
    }

    function setTechAction() {
        $('#techaction').css("z-index", "9999");
    }


    function maxTA() {

        $('#techaction').animate({
            height: 400,
            width: 800
        })
        $('#techheader').animate({
            width: 800
        })

    }

    function minTA() {

        $('#techaction').animate({
            height: 100,
            width: 400
        })
        $('#techheader').animate({
            width: 400
        })

    }

    function showShopStats() {

        $('#shopstats').toggle()

        ssclock = setInterval(function () {
            if ($('#shopstats').html() == "") {
            } else {
                clearInterval(ssclock)
            }
        }, 500)

    }

    function openTA() {

        mdb.Toast.getInstance(document.getElementById('tech-activities-toast')).show();

    }

    $('.shopnotice').mousedown(function (e) {
        drag = $(this).closest('.draggable')
        drag.addClass('dragging')
        $(this).on('mousemove', function (e) {
            drag.css('left', e.clientX - $(this).width() / 2)
            drag.css('top', e.clientY - $(this).height() / 2 - 10)
            window.getSelection().removeAllRanges()
        })
    })
    $('.shopnotice').mouseleave(stopDragging)
    $('.shopnotice').mouseup(stopDragging)
    $('.shopnotice').blur(function () {
        msg = encodeURIComponent($(this).html())
        $.ajax({
            data: "shopid=<?php echo $shopid; ?>&msg=" + msg,
            url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/shopnotice.php",
            success: function (r) {
                //console.log(r)
            }
        });
    });


    function checkPassChange() {
        $.post("<?= COMPONENTS_PUBLIC ?>/login/loginaction.php", {
            t: 'checkpasschange',
            empid: '<?= $empid ?>',
            shopid: '<?= $shopid ?>'
        }, function (data) {
            if (data == 'yes') {
                $("#changepassmodal").modal({
                    backdrop: 'static',
                    keyboard: false
                });
            }
        });
    }

    function changepass() {
        $('#changepassmsg').html("")
        var newpass = $('#newpass').val()
        var cnewpass = $('#cnewpass').val()
        var number = /([0-9])/;
        var alphabets = /([a-zA-Z])/;

        if (newpass == '' || cnewpass == '') {
            $('#changepassmsg').html("<span style='color:var(--primary)'>*Passwords cannot be blank</span>")
            return
        } else if (newpass != cnewpass) {
            $('#changepassmsg').html("<span style='color: var(--primary)'>*Passwords do not match</span>")
            return
        } else if (newpass.length < 8 || !newpass.match(number) || !newpass.match(alphabets)) {
            $('#changepassmsg').html("<span style='color:var(--primary)'>*Password should be minimum eight characters long, at least one letter, at least one number.</span>")
            return
        }

        $('#btn-change-pass').attr('disabled', 'disabled')

        $.post("<?= COMPONENTS_PUBLIC ?>/login/loginaction.php", {
            t: 'changepass',
            empid: '<?= $empid ?>',
            shopid: '<?= $shopid ?>',
            password: newpass
        }, function (data) {
            if (data.status == 'success') {
                $('#changepassmsg').html("<span style='color:var(--green)'>*Password changed successfully. Please login using the new password.</span>")
                setTimeout(function () {
                    document.location = '<?= COMPONENTS_PRIVATE ?>/login/logoff.php'
                }, 2000);
            } else {
                $('#changepassmsg').html("<span style='color:var(--primary)'>*" + data.msg + "</span>")
                $('#btn-change-pass').attr('disabled', false)
            }

        }, 'json');

    }

    var timer;
    var timer2;

    function checkSMS() {

        timer2 = setInterval(function () {
            if ($('#myframe').attr("src") != "smslive-v2.php") {
                $.ajax({
                    data: "t=getcount&shopid=<?php echo $shopid; ?>",
                    url: "<?= COMPONENTS_PRIVATE ?>/shared/smsliveaction-v2.php",
                    type: "post",
                    xhrFields: { withCredentials: true },
                    crossDomain: true,
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    },
                    success: function (r) {

                        if (r > 0) {
                            //$('#smstab').addClass('text-primary')
                            $('#livetext_badge').html(r);
                        } else {
                           // $('#smstab').removeClass('text-primary')
                            $('#livetext_badge').html('');
                        }
                    }
                });
            }
            <?php
            if ($_COOKIE['mode'] == 'full' && $autoshowta == "yes") {
            ?>
            $.ajax({
                data: "shopid=<?php echo $shopid; ?>",
                url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/techactivities.php",
                type: "post",
                xhrFields: { withCredentials: true },
                crossDomain: true,
                success: function (r) {
                    $('#techresults').html(r)
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });
            <?php
            }
            ?>

            // add the check for IM
            $.ajax({
                data: "t=countmsg&shopid=<?php echo $shopid; ?>&usr=<?php echo addslashes($usr); ?>",
                url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/chatinfo.php",
                type: "post",
                xhrFields: { withCredentials: true },
                crossDomain: true,
                success: function (r) {
                    if (r > 0)
                        $('#message_badge').html(r)
                    else
                        $('#message_badge').html('')
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });

        }, 10000)

    }

    function changeWip(tab = 'wip', reset = false) {
        var iOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;

        Cookies.set('tab', tab, {
            expires: 360
        });

        var resetqs = ""
        if (reset == true){
            resetqs = "&reset"
        }

        $('#wipmain').hide()

        if (tab == "timeclocktab") {
            checkSMS()
            $('#myframe').attr("src", "<?= COMPONENTS_PRIVATE ?>/v2/wip/timeclock.php?shopid=<?php echo $shopid; ?>")
            $('#wiplistframe').show()
            clearInterval(wipinterval)

        } else if (tab == "smstab") {
            $('#myframe').attr("src", "<?= COMPONENTS_PRIVATE ?>/v2/livetext/livetext.php")
            $('#wiplistframe').show()
            clearInterval(wipinterval)

        } else if (tab == "kanban") {
            $('#myframe').attr("src", "<?= COMPONENTS_PRIVATE ?>/v2/workflow/board.php")
            $('#wiplistframe').show()
            clearInterval(wipinterval)
        } else {
            $('#wipmain').show()
        }

    }

    function stopDragging() {
        drag = $(this).closest('.draggable')
        drag.removeClass('dragging')
        $(this).off('mousemove')
    }

    function loadWip(type) {

        showLoader()
        Cookies.remove('kanban')
        Cookies.set('tab', 'wip', {
            expires: 360
        });
        document.location = "<?= COMPONENTS_PRIVATE ?>/v2/wip/wip.php?reset"
    }


    function loadMotor() {

        <?php if($motor == 'no'){?>
            $('#motorGlobalModal').modal('show');
        <?php }else{ ?>

        var iOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;

         $('#motor-iframe').attr('src', "loadmotor.php?ios=" + iOS + "&shopid=<?php echo $shopid; ?>&pid=<?php echo $pid; ?>")
         $('#motorModal').modal('show')

        <?php }?>
    }


    function resize_iframe (myframe) {
        var doc_height = $(document).height();
        var top_offset = $("#myframe").offset().top;
        frame_height = doc_height - top_offset - 50;
        myframe.style.height = frame_height+'px'
        if(Cookies.get('tab') != 'wip')hideLoader();
    }


    $(document).ready(function () {

        $(".ma-checkbox").change(function(){
                var op = $(this).data('option');
                $("."+op+"-checkbox").not(this).prop('checked', false).blur();
        });

        $("#massupdate_btn").click(function(elem){
            console.log("MassAssignDropDownMenu");
            roids = ""
            zeroroid = ""

            var status = '';
            var tech = '';
            var techid = '';
            var techrate = '';
            var rotype = '';

            if ($(".status-checkbox:checked").length > 0) {
                status = $(".status-checkbox:checked").val();
            }
            if ($(".type-checkbox:checked").length > 0){
                rotype = $(".type-checkbox:checked").val();
            }
            if ($(".tech-checkbox:checked").length > 0){
                tech = $(".tech-checkbox:checked").val();
                techid = $(".tech-checkbox:checked").data("techid");
                techrate = $(".tech-checkbox:checked").data("techrate");
            }

            $("table#WIP input:checkbox").each(function () {
                if ($(this).is(':checked')) {
                    console.log($(this));
                    roid = $(this).attr("id")
                    roid = roid.replace("checkbox", "")
                    roids = roids + roid + "|"
                    if ($(this).attr("data-rec") == 0) zeroroid = $(this).attr("data-rec")
                    return
                }
            });

            if (roids != '') {
                if (status != '' || rotype != '' || tech != '') {
                    console.log(roids);
                    message = "RO#(s) (" + roids.replaceAll("|", ", ").trim().replace(/[,]*$/, '') + ") will be Assigned <ul>";
                    if (status != "") {
                        message += "<li>Status of <strong class='text-primary'>" + status + "</strong></li>";
                    }
                    if (rotype != '') {
                        message += "<li>RO Type to <strong class='text-primary'>" + rotype + "</strong></li>";
                    }
                    if (tech != '') {
                        message += "<li>Technician for all Labor as <strong class='text-primary'>" + tech + "</strong></li>";
                    }
                    message += "</ul>";
                    if (status != "" && status == "Closed") {
                        if ('<?= $requirepayments?>' == 'yes' && zeroroid != '') {
                            message += "<p><strong class='text-primary'>One or more ROs cannot be closed as payment is required. Would you still like to continue?</strong></p>";
                        }
                    }

                    sbconfirm('Repair Order Mass Update', message, function () {
                        //confirmed

                        showLoader()
                        ds = "shopid=<?= $shopid; ?>&roids=" + roids + "&status=" + status + "&rotype=" + rotype + "&techid=" + techid + "&techrate=" + techrate + "&tech=" + tech;
                        $.ajax({
                            data: ds,
                            url: "massupdate_new.php",
                            type: "post",
                            success: function (r) {
                                console.log(r);
                                if (r == "success") {
                                    parent.location.reload()
                                }
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                console.log(xhr.status);
                                console.log(xhr.responseText);
                                console.log(thrownError);
                                hideLoader()
                            }
                        })
                    })
                } else {
                    sbalert("Please select at least one assign item")
                }
            } else {
                sbalert("No RO(s) selected, please make a selection")
            }
        });

        wtab = Cookies.get('tab')



        if(wtab == 'kanban')
        changeWip(wtab)
        else
        changeWip()


        <?php if($_COOKIE['mode'] == 'full'){?>

        // get shop stats
        setTimeout(function () {
            $.ajax({
                data: "shopid<?php echo $shopid; ?>",
                url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/wipstats.php",
                success: function (r) {
                    $('#shopstatsbody').append(r)
                }
            });
        }, 5000);

        // check for cores once
        setTimeout(function () {
            $.ajax({
                data: "shopid=<?php echo $shopid; ?>",
                url: "<?= COMPONENTS_PRIVATE ?>/cores/corecheck.php",
                type: "get",
                xhrFields: { withCredentials: true },
                crossDomain: true,
                success: function (r) {
                    if (r > 0) {
                        $('.cores').css("color", "var(--primary)").css("text-transform", "uppercase").css("font-weight", "bold")
                    } else {
                        $('.cores').css("color", "inherit").css("text-transform", "capitalize").css("font-weight", "normal")
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });
        }, 2500);

        // check for cores every 60 seconds
        setInterval(function () {
            $.ajax({
                data: "shopid=<?php echo $shopid; ?>",
                url: "<?= COMPONENTS_PRIVATE ?>/cores/corecheck.php",
                type: "get",
                xhrFields: { withCredentials: true },
                crossDomain: true,
                success: function (r) {
                    //console.log(r)
                    if (r > 0) {
                        $('.cores').css("color", "var(--primary)").css("text-transform", "uppercase").css("font-weight", "bold")
                    } else {
                        $('.cores').css("color", "inherit").css("text-transform", "capitalize").css("font-weight", "normal")
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });
        }, 60000);

        // check for new appointments
        setTimeout(function () {
            $.ajax({
                data: "t=getappts&shopid=<?php echo $shopid; ?>",
                url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/getnewappts.php",
                type: "post",
                success: function (r) {
                    if (r != '' && !$('#changepassmodal').is(':visible')) {
                        $('#apptbody').append(r)
                        $('#apptmodal').modal('show')
                        $("#apptmodal .ttip").tooltip()
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });
        }, 5000)

        // check for new remote payments
        setTimeout(function () {
            $.ajax({
                data: "t=getpmts&shopid=<?php echo $shopid; ?>",
                url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/getpmts.php",
                type: "post",
                success: function (r) {
                    if (r != '' && !$('#changepassmodal').is(':visible')) {
                        $('#pmtbody').append(r)
                        $('#pmtmodal').modal('show')
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });
        }, 5000)

        setInterval(function () {
            get_notifications();
        }, 60000);
        setTimeout(function () {
            $.post("notification_actions.php", {
                type: 'activecount',
                shopid: '<?= $shopid ?>'
            }, function (data) {
                if (data > 0)
                    $('#notification_badge').html(data);
            });

        }, 30000);


        <?php }?>

        setInterval(function () {

            $.ajax({
                data: "t=getreminders&empid=<?php echo $empid; ?>&shopid=<?php echo $shopid; ?>",
                url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/checkreminders.php",
                type: "post",
                success: function (r) {
                    if (r == "none" || r == "") {
                        $('#reminderbutton').css("color", "inherit")
                        $('#reminderlist').html("<p class='text-center'>You have no Personal Reminders on your list.  Click below to add one</p>")
                    } else {
                        issuccess = r.indexOf("alert-success")
                        isoverdue = r.indexOf("alert-danger")
                        if (issuccess > 0) {
                            remstate = true
                            remrotate = setInterval(function () {
                                if (remstate) {
                                    $("#reminderbutton").css("color", "orange")
                                } else {
                                    $("#reminderbutton").css("color", "inheit")
                                }
                                remstate = !remstate;
                            }, 1000);
                            $('#reminderlist').html(r)
                            $('#remindermodal').modal('show')
                        } else if (isoverdue > 0) {
                            $('#reminderbutton').css("color", "orange")
                            $('#reminderlist').html(r)
                        } else {
                            $('#reminderlist').html(r)
                        }
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            })

            <?php if($_COOKIE['mode'] == 'full'){?>

            $.ajax({
                data: "showall=no&shopid=<?php echo $shopid; ?>",
                url: "<?= INTEGRATIONS ?>/lyft/ridestatuscount.php",
                type: "get",
                xhrFields: { withCredentials: true },
                crossDomain: true,
                success: function (r) {
                    r = parseFloat(r)
                    //console.log("lyftcount:"+r)
                    if (r > 0) {
                        eModal.iframe({
                            title: "Lyft Activity",
                            url: "<?= INTEGRATIONS ?>/lyft/ridestatus.php",
                            size: eModal.size.xl,
                            buttons: [{
                                text: 'Close',
                                style: 'warning',
                                close: true
                            }]
                        });
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });

          <?php }?>

        }, 60000);


        $('#trialclose').on('click', function () {
            $.post("<?= COMPONENTS_PRIVATE ?>/v2/wip/setcookie.php", function (data) {
                location.reload();
            });

        })


        $('#notificationmodal').on('click', '.btn-dismiss', function () {
            var $this = $(this);
            var did = $this.data('id');
            $.post("<?= COMPONENTS_PRIVATE ?>/v2/wip/notification_actions.php", {
                type: 'dismiss',
                shopid: '<?= $shopid ?>',
                id: did
            }, function (data) {
                $this.parent().parent().remove();
                if ($('#notificationmodal .btn-dismiss').length < 1) {
                    $('#notificationmodal').modal('hide');
                    $('#notification_badge').html('');
                } else
                    $('#notification_badge').html($('#notificationmodal .btn-dismiss').length);
            });

        })

        $('#notificationmodal').on('click', '.btn-dismiss-all', function () {

            sbconfirm("Dismiss Notifications","Are you sure?",function(){

                var dismiss_ids = [];
                $.each($('#notificationmodal .btn-dismiss'), function () {
                    dismiss_ids.push($(this).data('id'));
                });

                var ids = dismiss_ids.join(",");

                $.post("<?= COMPONENTS_PRIVATE ?>/v2/wip/notification_actions.php", {
                    type: 'dismissall',
                    shopid: '<?= $shopid ?>',
                    ids: ids
                }, function (data) {
                    $('#notificationmodal').modal('hide');
                    $('#notification_badge').html('');
                });


            });

        })

        <?php if($showmatcovideo == 'yes'){?>
        openQuickStartVideo("<a data-embed='https://www.youtube.com/embed/1qy5OtK1UOM'></a>")
        <?php }?>


        <?php if ($_COOKIE['mode'] == 'full' && $autoshowta == "yes"){?>
            openTA()
        <?php }?>

        $(document).on('click', function(event) {
            const clickedElement = event.target;
            const $toast = $('#tech-activities-toast');
            const tamenu = $('#techactivities')

            if (!$toast.is(clickedElement) && !tamenu.is(clickedElement) && $toast.has(clickedElement).length === 0) {
              $toast.toast('hide');
            }
        });

        $.post("getversion.php", function (data) {
            if(data.title != '')
            $('#cannyversion').html("<a target='_blank' href='" + data.url + "'><small>" + data.title + "</small></a>")
        },'json');

    });


    function get_notifications(t = '') {
        if (t == 'panel')
            showLoader()

        $.post("<?= COMPONENTS_PRIVATE ?>/v2/wip/notification_actions.php", {
            type: 'getlist',
            shopid: '<?= $shopid ?>',
            trigger: t
        }, function (data) {
            if (t == 'panel')
                hideLoader()
            if (data.content != '' || t == 'panel') {
                if (data.content == '')
                    data.content = "<div class='text-center'>You are all caught up on your notifications.</div>";
                else if (data.content != storednotifications && data.chimes != '') {
                    var chimes = data.chimes.split(',')
                    for (var i = 0; i < chimes.length; i++) {
                        ion.sound.play(chimes[i]);
                    }
                }
                storednotifications = data.content
                $('#notificationmodal #notificationlist').html(data.content);

                if ($('#notificationmodal .btn-dismiss').length > 1)
                    $('#notificationmodal .modal-footer').show();
                else
                    $('#notificationmodal .modal-footer').hide();

                $('#notificationmodal').modal('show');
                if ($('#notificationmodal .btn-dismiss').length > 0)
                    $('#notification_badge').html($('#notificationmodal .btn-dismiss').length);
            }
        }, 'json');
    }

    function gotoro(roid) {

        location.href = "<?= COMPONENTS_PRIVATE ?>/v2/ro/ro.php?showpmts=yes&roid=" + roid

    }

    function dismissAllAppt() {

        sbconfirm("Appointments","Are you sure you want to mark all of these appointments as viewed?",function(){

         $.ajax({
                data: "t=dismissall&shopid=<?php echo $shopid; ?>",
                url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/getnewappts.php",
                type: "post",
                success: function (r) {
                    if (r == "success") {
                        sbalert("All appointments marked as read")
                        $('#apptmodal').modal('hide')
                        $('#apptbody').html('')
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });

        });
    }

    function dismissAppt(id, email) {

        $.ajax({
            data: "t=dismissappt&shopid=<?php echo $shopid; ?>&id=" + id,
            url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/getnewappts.php",
            type: "post",
            success: function (r) {
                if (r == "success") {
                    $('#apptmodal').modal('hide')
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });

    }

    function gotoSchedule(id, d) {

        location.href = '<?= COMPONENTS_PRIVATE ?>/v2/calendar/calendar.php?d=' + d
    }

    function createROVINScan(customerid, shopid, vehid) {

        sbconfirm("Remove VIN","VIN will be removed. Are you sure?",function(){

               location.href = '<?= COMPONENTS_PRIVATE ?>/v2/createro/addconcerns.php?cid=' + customerid + '&vid=' + vehid

        });
    }

    function openQuickStartVideo(elem) {
        var video = $(elem).data("embed");
        $("#youtubeFrame").attr("src", video);
        $("#quickStartModal").modal('show');

        $("#quickStartModal").on('hide.bs.modal', function (event) {
            $("#youtubeFrame").attr("src", '');
        });
    }

    function hideCapital() {

        $.ajax({url: "<?= COMPONENTS_PRIVATE ?>/accounting/hidecapital.php"});
    }

    function hideOverCount() {

      sessionStorage.setItem("hideovercount", 'yes');

    }

    function addUsername()
    {
        var username = encodeURIComponent($('#empusername').val())
        if(username.length < 5)
        $('#usernamemsg').html("<span style='color: var(--primary);'>*Username must be at least 5 characters long</span>")
        else
        {
            $('#btn-add-username').attr('disabled','disabled')
            $.ajax({
            data: "empid=<?= $empid?>&shopid=<?php echo $shopid; ?>&username=" + username,
            url: "<?= COMPONENTS_PRIVATE ?>/v2/wip/addusername.php",
            type: "post",
            success: function (r) {
                if (r == "success") {
                    $('#usernamemsg').html("<span style='color: var(--green);'>Saved</span>")
                    setTimeout(function(){$('#usernamemodal').modal('hide')},1000)
                }
                else if(r == 'duplicate')
                $('#usernamemsg').html("<span style='color:var(--primary);'>*Username already in use. Please enter a different one</span>")
                else
                sbalert(r)

                $('#btn-add-username').attr('disabled',false)
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
            });
        }
    }


    ion.sound({
        sounds: [
            {name: "beer_can"},
            {name: "bell_ring"},
            {name: "branch_break"},
            {name: "button_click"},
            {name: "keyboard_click"},
            {name: "big_button"},
            {name: "tiny_button"},
            {name: "camera_flashing"},
            {name: "camera_flashing_2"},
            {name: "cd_tray"},
            {name: "computer_error"},
            {name: "door_bell"},
            {name: "door_bump"},
            {name: "glass"},
            {name: "keyboard_desk"},
            {name: "light_bulb_breaking"},
            {name: "metal_plate"},
            {name: "metal_plate_2"},
            {name: "pop_cork"},
            {name: "snap"},
            {name: "staple_gun"},
            {name: "tap"},
            {name: "water_droplet_1"},
            {name: "water_droplet_2"}
        ],
        path: "<?= ASSETS?>/sounds/",
        preload: false,
        multiplay: true
    });

</script>


<script>

$('#navbar02 li.nav-item a').on('click', function() {
  $('#navbar02').collapse('hide');
});

$(document).ready(function () {

    $('.dateTimePickerBs').datetimepickerbs({
        format: "MM/DD/YYYY hh:mm a",
        sideBySide: true,
    });
})

</script>