<?php
require CONN;

$shopid = $_GET['shopid'];
$msg = strip_tags(html_entity_decode($_GET['msg']));
$stmt = "update company set shopnotice = ? where shopid = ?";
echo $stmt;

if ($query = $conn->prepare($stmt)){
	$query->bind_param("ss",$msg,$shopid);
    if ($query->execute()){
	   	$conn->commit();
	   	echo "success";
	}else{
		echo $conn->errno;
	}
    $query->close();
    

}else{
	echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

?>
<?php if(isset($conn)){mysqli_close($conn);} ?>