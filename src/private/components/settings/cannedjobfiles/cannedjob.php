<?php
require CONN;

$shopid = $_GET['shopid'];
$type = $_GET['type'];

if ($type == "newjob"){

	$jobname = filter_var($_GET['cjname'], FILTER_SANITIZE_STRING);
	$jobflat = filter_var($_GET['cjflat'], FILTER_SANITIZE_STRING);
	$jobtax = filter_var($_GET['cjtax'], FILTER_SANITIZE_STRING);
	
	$stmt = "select id from cannedjobs where shopid = '$shopid' order by id desc limit 1";
	if ($query = $conn->prepare($stmt)){
	    if ($query->execute()){
	    	$query->bind_result($id);
	    	$query->fetch();
		    $query->close();
	    }else{
	    	echo $conn->errno;
	    }
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
	$id = $id + 1;
	
	$stmt = "insert into cannedjobs (jobname,flatprice,taxable,shopid,id) values ('$jobname',$jobflat,'$jobtax','$shopid',$id)";
	//echo $stmt;
	if ($query = $conn->prepare($stmt)){
	    if ($query->execute()){
		    $conn->commit();
		    $query->close();
	    }else{
	    	echo $conn->errno;
	    }
	
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

	echo "success|".$id;

}

if ($type == "dellabor"){
	$laborid = $_GET['laborid'];
	$stmt = "delete from cannedlabor where shopid = '$shopid' and id = $laborid";
	if ($query = $conn->prepare($stmt)){
	
		$query->execute();
		$conn->commit();
		$query->close();
		echo "success";
	
	}


}

if ($type == "delpart"){
	$partid = $_GET['partid'];
	$stmt = "delete from cannedparts where shopid = '$shopid' and id = $partid";
	if ($query = $conn->prepare($stmt)){
	
		$query->execute();
		$conn->commit();
		$query->close();
		echo "success";
	
	}


}


if ($type == "editlabor"){

	$laborid = $_GET['laborid'];
	$labor = filter_var($_GET['labor'], FILTER_SANITIZE_STRING);
	$laborhours = $_GET['hours'];
	$cannedid = $_GET['cannedjobid'];
	
	if ($laborid != 0){
		$stmt = "update cannedlabor set labor = '$labor', laborhours = $laborhours where shopid = '$shopid' and id = $laborid";
	}else{
		$stmt = "insert into cannedlabor (labor,laborhours,cannedjobsid,shopid) values ('$labor',$laborhours,$cannedid,'$shopid')";
	}
	if ($query = $conn->prepare($stmt)){
	    if ($query->execute()){
		    $conn->commit();
		    $query->close();
			echo "success";
	    }else{
	    	echo $conn->errno;
	    }
	
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

}

if ($type == "editpart"){

	$partid = $_GET['partid'];
	
	$stmt = "select partnumber,partdescription,supplier,partcost,partprice,qty,tax,partcategory,partcode from cannedparts where shopid = '$shopid' and id = $partid";
	//echo $stmt;
	if ($query = $conn->prepare($stmt)){
		
	    if ($query->execute()){
	    	$query->bind_result($partnumber,$partdescription,$supplier,$partcost,$partprice,$qty,$tax,$partcategory,$partcode);
	    	$query->fetch();
	    	echo $partnumber."|".$partdescription."|".$supplier."|".$partcost."|".$partprice."|".$qty."|".$tax."|".$partcategory."|".$partcode;
		    $query->close();
			
	    }else{
	    	echo $conn->errno;
	    }
	
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}


}

if ($type == "deletejob"){

	$cannedid = $_GET['cannedid'];
	$stmt = "delete from cannedjobs where shopid = '$shopid' and id = $cannedid";
	if ($query = $conn->prepare($stmt)){
	    $query->execute();
	    $conn->commit();
	    //echo "success";
	    $query->close();
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
	
	$stmt = "delete from cannedlabor where shopid = '$shopid' and cannedjobsid = $cannedid";
	if ($query = $conn->prepare($stmt)){
	    $query->execute();
	    $conn->commit();
	    //echo "success";
	    $query->close();
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
	
	$stmt = "delete from cannedparts where shopid = '$shopid' and cannedjobsid = $cannedid";
	if ($query = $conn->prepare($stmt)){
	    $query->execute();
	    $conn->commit();
	    //echo "success";
	    $query->close();
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
	
	echo "success";
}

if ($type == "savepart"){

	// ds = "type=savepart&pn="+pn+"&pd="+pd+"&su="+su+"&cat="+cat+"&pc="+pc+"&pcost="+pcost+"&pp="+pp+"&qty="+qty+"&id="+id+"&shopid="
	$pn = filter_var($_GET['pn'], FILTER_SANITIZE_STRING);
	$pd = filter_var($_GET['pd'], FILTER_SANITIZE_STRING);
	$su = filter_var($_GET['su'], FILTER_SANITIZE_STRING);
	$cat = filter_var($_GET['cat'], FILTER_SANITIZE_STRING);
	$pc = filter_var($_GET['pc'], FILTER_SANITIZE_STRING); // part code
	$pcost = filter_var($_GET['pcost'], FILTER_SANITIZE_STRING);
	$qty = filter_var($_GET['qty'], FILTER_SANITIZE_STRING);
	$id = filter_var($_GET['id'], FILTER_SANITIZE_STRING);
	$pp = filter_var($_GET['pp'], FILTER_SANITIZE_STRING);
	$tax = "yes";
	$cannedid = $_GET['cannedid'];
	
	if ($id != 0){
		$stmt = "update cannedparts set partnumber = ?, partdescription = ?, supplier = ?, partcost = ?, partprice = ?, qty = ?, partcategory = ?, partcode = ?"
		. " where id = ? and shopid = ?";
	}else{
		$stmt = "insert into cannedparts (partnumber,partdescription,supplier,partcost,partprice,qty,tax,partcategory,partcode,cannedjobsid,shopid) values (?,?,?,?,?,?,?,?,?,?,?)";
	}
	//printf(str_replace("?","%s",$stmt),$pn,$pd,$su,$pcost,$pp,$qty,$cat,$pc,$id,$shopid);
	
	if ($query = $conn->prepare($stmt)){
	
		if ($id != 0){
			$query->bind_param("sssddissis",$pn,$pd,$su,$pcost,$pp,$qty,$cat,$pc,$id,$shopid);
		}else{
			$query->bind_param("sssddisssis",$pn,$pd,$su,$pcost,$pp,$qty,$tax,$cat,$pc,$cannedid,$shopid);
		}
	    $query->execute();
	    $conn->commit();
	    echo "success";
	    $query->close();
	
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}


}

if ($type == "savejob"){

	// "id="+cannedid+"&jobname="+editcjname+"&flatprice="+editcjflat+"&taxable="+editcjtax+"&type=savejob&shopid=
	$jobname = filter_var($_GET['jobname'], FILTER_SANITIZE_STRING);
	$flatprice = filter_var($_GET['flatprice'], FILTER_SANITIZE_STRING);
	$taxable = filter_var($_GET['taxable'], FILTER_SANITIZE_STRING);
	$cannedid = $_GET['id'];

	$stmt = "update cannedjobs set jobname = '$jobname', flatprice = $flatprice, taxable = '$taxable' where shopid = '$shopid' and id = $cannedid";
	if ($query = $conn->prepare($stmt)){
	    $query->execute();
	    $conn->commit();
	    echo "success";
	    $query->close();
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}


}

if ($type == "main"){
	
	$id = $_GET['id'];
	$stmt = "select jobname,flatprice,taxable from cannedjobs where id = ? and shopid = ?";
	
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("is",$id,$shopid);
	    $query->execute();
	    $query->bind_result($j,$f,$t);
	    $query->fetch();
	    echo "success|".strtoupper($j)."|".strtoupper($f)."|".strtoupper($t);
	    $query->close();
	
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
}

if ($type == "sub"){
	$id = $_GET['id'];
?>
	<div>
	<br><br>
	<b>Parts and Labor on this Canned Job.  Click a line to Edit / Delete it.</b>
	</div>
	
	<table class="table table-condensed table-striped table-bordered table-header-bg">
		<thead>
			<tr>
				<td style="width:15%">Type</td>
				<td style="width:65%">Item</td>
				<td style="width:10%">Qty/Hrs</td>
				<td style="width:10%">Price</td>
			</tr>
		</thead>
		<tbody>
<?php
	
	$stmt = "select labor, laborhours, id from cannedlabor where shopid = ? and cannedjobsid = ?";
	if($query = $conn->prepare($stmt)){
		$query->bind_param("si",$shopid,$id);
	    $query->execute();
	    $result = $query->get_result();
		while($row = $result->fetch_array()) {
	?>
			<tr onclick="editLabor(<?php echo $row['id']; ?>,'<?php echo str_replace("\r\n","",str_replace("'","\'",$row['labor'])); ?>','<?php echo $row['laborhours']; ?>')">
				<td>Labor</td>
				<td><?php echo strtoupper($row['labor']); ?></td>
				<td><?php echo $row['laborhours']; ?></td>
				<td></td>
			</tr>
	<?php
		}
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

	$stmt = "select partnumber,partdescription,qty,partprice,id,cannedjobsid from cannedparts where shopid = ? and cannedjobsid = ?";
	//echo $stmt;
	if($query = $conn->prepare($stmt)){
		$query->bind_param("si",$shopid,$id);
	    $query->execute();
	    $result = $query->get_result();
		while($row = $result->fetch_array()) {
	?>
			<tr onclick="editPart(<?php echo $row['id']; ?>,<?php echo $row['cannedjobsid']; ?>)">
				<td>Part #<?php echo strtoupper($row['partnumber']); ?></td>
				<td><?php echo strtoupper($row['partdescription']); ?></td>
				<td><?php echo $row['qty']; ?></td>
				<td><?php echo $row['partprice']; ?></td>
			</tr>
	<?php
		}
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
?>
		</tbody>
	</table>
<?php
}
?>
<?php if(isset($conn)){mysqli_close($conn);} ?>