<!DOCTYPE html>
<?php
require CONN;
$shopid = $_COOKIE['shopid'];

$stmt = "select usepartsmatrix from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {

	$query->bind_param("s", $shopid);
	$query->execute();
	$query->store_result();
	$num_roid_rows = $query->num_rows;
	//echo "rotype rows:".$num_roid_rows."<BR>";
	if ($num_roid_rows > 0) {
		$query->bind_result($usematrix);
		$query->fetch();
	} else {
		$usematrix = "no";
	}
	$query->close();
} else {
	echo "RO Type failed: (" . $conn->errno . ") " . $conn->error;
}

?>
<!--[if IE 9]>         <html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus">
<!--<![endif]-->

<head>
	<meta charset="utf-8">

	<title><?= getPageTitle() ?></title>

	<meta name="robots" content="noindex, nofollow">
	<meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
	<link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon()?>' type='image/x-icon' />
	<!-- Icons -->
	<!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

	<link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
	<link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
	<link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
	<link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
	<link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

	<link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
	<link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
	<link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
	<link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
	<link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
	<link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
	<link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
	<link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
	<link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
	<!-- END Icons -->

	<!-- Stylesheets -->
	<!-- Web fonts -->
	<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
	<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

	<!-- Page JS Plugins CSS -->
	<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
	<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
	<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css?v=1">

	<!-- Bootstrap and OneUI CSS framework -->
	<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
	<link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
	<link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
	<style>
		.modal-wide .modal-dialog {
			width: 70%;
			/* or whatever you wish */
		}

		#canneddetails {
			width: 96%;
			margin: auto
		}

		.highlight {
			background-color: yellow;
			font-weight: bold
		}

		.unhighlighted {
			background-color: transparent;
			font-weight: normal
		}
	</style>
	<!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
	<!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
	<!-- END Stylesheets -->
</head>

<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
	<!-- Page Container -->
	<!--
            Available Classes:

            'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

            'sidebar-l'                  Left Sidebar and right Side Overlay
            'sidebar-r'                  Right Sidebar and left Side Overlay
            'sidebar-mini'               Mini hoverable Sidebar (> 991px)
            'sidebar-o'                  Visible Sidebar by default (> 991px)
            'sidebar-o-xs'               Visible Sidebar by default (< 992px)

            'side-overlay-hover'         Hoverable Side Overlay (> 991px)
            'side-overlay-o'             Visible Side Overlay by default (> 991px)

            'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

            'header-navbar-fixed'        Enables fixed header
        -->
	<div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">
		<!-- Side Overlay-->
		<aside id="side-overlay">
			<!-- Side Overlay Scroll Container -->
			<div id="side-overlay-scroll">
				<!-- Side Header -->
				<div class="side-header side-content">
					<!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
					<button class="btn btn-default pull-right" type="button" data-toggle="layout" data-action="side_overlay_close">
						<i class="fa fa-times"></i>
					</button>
					<span>
						<img class="img-avatar img-avatar32" src="<?= IMAGE ?>/avatars/avatar10.jpg" alt="">
						<span class="font-w600 push-10-l">Walter Fox</span>
					</span>
				</div>
				<!-- END Side Header -->

			</div>
			<!-- END Side Overlay Scroll Container -->
		</aside>
		<!-- END Side Overlay -->

		<!-- Sidebar -->
		<nav id="sidebar">
			<!-- Sidebar Scroll Container -->
			<div id="sidebar-scroll">
				<!-- Sidebar Content -->
				<!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
				<div class="sidebar-content">
					<!-- Side Header -->
					<div class="side-header side-content bg-white-op">
						<!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
						<button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button" data-toggle="layout" data-action="sidebar_close">
							<i class="fa fa-times"></i>
						</button>
						<a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
							<i class="text-primary">
								<?php getLogo() ?></i>
							<span class="h4 font-w600 sidebar-mini-hide">
							</span>
						</a>
					</div>
					<!-- END Side Header -->

					<!-- Side Content -->
					<div class="side-content-sbp-settings side-content">
						<?php require("menu.php"); ?>
					</div>

					<!-- END Side Content -->
				</div>
				<!-- Sidebar Content -->
			</div>
			<!-- END Sidebar Scroll Container -->
		</nav>
		<!-- END Sidebar -->

		<!-- Header -->
		<header id="header-navbar" class="content-mini content-mini-full">
			<!-- Header Navigation Right -->
			<ul class="nav-header pull-right">
				<li>
					<div id="shopnotice">
						<?php echo $_COOKIE['shopname'] . " #" . $shopid . "<br>" . $_COOKIE['username'] . '<a class="btn btn-primary btn-sm btn-logoff" href="' . COMPONENTS_PUBLIC . '/login/logoff.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Logoff</span></a>'; ?>
					</div>
				</li>
			</ul>
			<!-- END Header Navigation Right -->

			<!-- Header Navigation Left -->

			<ul class="nav-header pull-left">
				<li class="hidden-md hidden-lg">
					<!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
					<button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
						<i class="fa fa-navicon"></i>
					</button>
				</li>
				<li class="hidden-xs hidden-sm">
					<!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
					<button class="btn btn-default" data-toggle="layout" id="close-sidebar" data-action="sidebar_mini_toggle" type="button">
						<i class="fa fa-bars"></i>
					</button>
				</li>
				<li>
					<!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
					<button style="display:none" class="btn btn-default pull-right" data-toggle="modal" data-target="#apps-modal" type="button">
						<i class="si si-grid"></i>
					</button>
				</li>
				<li class="visible-xs">
					<!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
					<button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search" data-class="header-search-xs-visible" type="button">
						<i class="fa fa-search"></i>
					</button>
				</li>
				<li>

				</li>
			</ul>

			<!-- END Header Navigation Left -->
		</header>
		<!-- END Header -->

		<!-- Main Container -->
		<main class="container-fluid" id="main-container">
			<br>
			<h3>Canned Jobs Expanded View</h3>
			<p>Here you have an expanded view of Canned Jobs showing the parts and labor on each. Use the search box below to find a part or labor that you need to change on several jobs. Type in a part number for example to find all instances of that part number so you can change all jobs that use that part.</p>
			<button style="float:right" onclick="showCreateCannedJob()" class="btn btn-warning btn-md">Create New Canned Job</button>
			<br><br>
			<div class="row" style="margin-left:25px;">
				<div class="col-md-4"><input type="text" class="form-control" id="srch" style="text-transform:uppercase" placeholder="Enter any data to find on this page">
					<span class="btn btn-sm btn-success" onclick="findOnPage()">Find</span>
					<span class="btn btn-sm btn-default" onclick="clearHighlight()">Clear</span>

				</div>
			</div><br>
			<div class="row sbp-grid-header">
				<div class="col-md-8">Canned Job</div>
				<div class="col-md-2 text-right" style="left: 0px; top: 0px">Calculated Price</div>
				<div class="col-md-2 text-center">Taxable</div>
			</div>
			<?php
			$stmt = "select jobname,flatprice,taxable,id from cannedjobs where shopid = '" . $shopid . "' order by trim(jobname)";
			//echo $stmt;
			$result = $conn->query($stmt);
			//$conn->store_result();
			while ($row = $result->fetch_array()) {
				if ($row['flatprice'] > 0) {
					$fpm = "<br><span style='color:red;font-weight:bold'>If you edit this job it will be converted to a calculated price job instead of a flat price job</span>";
					$fpm = "";
					$totalcalcprice = $row['flatprice'];
				} else {
					$fpm = "";

					// now get the total of the parts and labor
					$lstmt = "select coalesce(sum(flatprice),0) fp, coalesce(sum(rateforcalc * laborhours),0) cp from cannedlabor where shopid = '$shopid' and cannedjobsid = " . $row['id'];
					if ($lquery = $conn->prepare($lstmt)) {
						$lquery->execute();
						$lquery->bind_result($lfp, $lcp);
						$lquery->fetch();
						$lquery->close();
					} else {
						echo $conn->error;
					}

					$pstmt = "select coalesce(sum(partprice * qty),0) pfp from cannedparts where shopid = '$shopid' and cannedjobsid = " . $row['id'];
					if ($pquery = $conn->prepare($pstmt)) {
						$pquery->execute();
						$pquery->bind_result($pfp);
						$pquery->fetch();
						$pquery->close();
					}

					if ($lfp > 0) {
						$totalcalcprice = $lfp + $pfp;
					} else {
						$totalcalcprice = $lcp + $pfp;
					}
				}
			?>
				<div style="color:white;background-color:#006600" onclick="editJob(<?php echo $row['id']; ?>,'<?php echo $row['flatprice']; ?>')" class="row sbp-grid">
					<div class="col-md-8 srchbox"><b><?php echo strtoupper($row['jobname']); ?></b></div>
					<div class="col-md-2 text-right"><b>$<?php echo number_format($totalcalcprice, 2) . " " . $fpm; ?></b></div>
					<div class="col-md-2 text-center"><b><?php echo strtoupper($row['taxable']); ?></b></div>
				</div>
				<?php
				// get the parts and labor and output them as hidden with a class to show them
				$pstmt = "select id,partnumber,partdescription,partcost,partprice from cannedparts where shopid = '$shopid' and "
					. "cannedjobsid = " . $row['id'];
				//echo $pstmt."<BR>";
				if ($pquery = $conn->prepare($pstmt)) {
					//$pquery->bind_param("si",$shopid,$row['id']);
					$pquery->execute();
					$pr = $pquery->get_result();
					while ($prs = $pr->fetch_array()) {
				?>
						<div class="row sbp-grid" onclick="editJob(<?php echo $row['id']; ?>,'<?php echo $row['flatprice']; ?>')">
							<div class="col-md-2">&nbsp;&nbsp;&nbsp;&nbsp; - PART#: <?php echo strtoupper($prs['partnumber']); ?></div>
							<div class="col-md-6">DESC: <?php echo strtoupper($prs['partdescription']); ?></div>
							<div class="col-md-2">SELL: $<?php echo number_format($prs['partprice'], 2); ?></div>
							<div class="col-md-2">COST: $<?php echo number_format($prs['partcost'], 2); ?></div>
						</div>
					<?php
					}
				}
				// get the parts and labor and output them as hidden with a class to show them
				$pstmt = "select id,labor,laborhours,flatprice from cannedlabor where shopid = '$shopid' and "
					. "cannedjobsid = " . $row['id'];
				//echo $pstmt."<BR>";
				if ($pquery = $conn->prepare($pstmt)) {
					//$pquery->bind_param("si",$shopid,$row['id']);
					$pquery->execute();
					$pr = $pquery->get_result();
					while ($prs = $pr->fetch_array()) {
					?>
						<div class="row sbp-grid" onclick="editJob(<?php echo $row['id']; ?>,'<?php echo $row['flatprice']; ?>')">
							<div class="col-md-2">&nbsp;&nbsp;&nbsp;&nbsp; - LABOR:</div>
							<div class="col-md-6"><?php echo strtoupper($prs['labor']); ?></div>
							<div class="col-md-2">HOURS: <?php echo number_format($prs['laborhours']), 2; ?></div>
							<div class="col-md-2">PRICE: $<?php echo number_format($prs['flatprice'], 2); ?></div>
						</div>
			<?php
					}
				}
			}
			?>
		</main>
		<!-- END Main Container -->

		<!-- Footer -->
		<!-- END Footer -->
	</div>
	<!-- END Page Container -->

	<!-- Apps Modal -->
	<div id="cannedmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="block block-themed block-transparent remove-margin-b">
					<div class="block-header bg-primary-dark">
						<ul class="block-options">
							<li>
								<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
							</li>
						</ul>
						<h3 class="block-title">Create New Canned Job</h3>
					</div>
					<div id="cannedinfo" class="block-content"></div>
					<div class="block-content">
						<div class="row">
							<div class="col-md-12">
								<div class="col-sm-12">
									<div class="form-material floating">
										<input class="form-control sbp-form-control" style="padding:20px;" tabindex="1" type="text" id="cjname" name="cjname">
										<label for="cjname">Job Name</label>
									</div>
								</div>
							</div>
							<div style="margin-top:20px;" class="col-sm-12">
								<div class="form-material floating">
									<select class="form-control" id="cjtax" name="cjtax">
										<option value="yes">Yes</option>
										<option value="no">No</option>
									</select>
									<label id="cjtaxlabel" for="cjtax">Taxable</label>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div style="margin-top:20px;" class="modal-footer">
					<button class="btn btn-md btn-primary" type="button" onclick="createCannedJob()">Create Canned Job</button>
					<button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
				</div>
			</div>
		</div>
	</div>

	<!-- Apps Modal -->
	<div id="editcannedmodal" class="modal fade modal-wide" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
		<input type="hidden" id="cannedjobid" name="cannedjobid">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="block block-themed block-transparent remove-margin-b">
					<div class="block-header bg-primary-dark">
						<ul class="block-options">
							<li>
								<button onclick="$('#editcannedmodal').modal('hide');$('#cannedjobid').val('')" type="button"><i class="si si-close"></i></button>
							</li>
						</ul>
						<h3 class="block-title">Edit Canned Job</h3>
					</div>
					<div id="vehinfo" class="block-content"></div>
					<div class="block-content">
						<div class="row">
							<div class="col-md-12">
								<div class="col-sm-12">
									<div class="form-material floating">
										<input class="form-control sbp-form-control" style="padding:20px;" tabindex="1" type="text" id="editcjname" name="editcjname">
										<label id="editcjnamelabel" for="editcjname">Job Name</label>
									</div>
								</div>
								<div class="col-sm-12">
									<div class="form-material floating">
										<input class="form-control sbp-form-control" readonly style="padding:20px;" tabindex="2" type="text" id="editcjcalc" name="editcjcalc">
										<label id="editcjcalclabel" for="editcjcalc">Calculated Total Price</label>
									</div>
								</div>
								<div class="col-sm-12">
									<div class="form-material floating">
										<select class="form-control" id="editcjtax" name="editcjtax">
											<option value="yes">Yes</option>
											<option value="no">No</option>
										</select>
										<label id="editcjtaxlabel" for="editcjtax">Taxable</label>
									</div>
								</div>
							</div>
							<div id="canneddetails"></div>
						</div>
					</div>
				</div>
				<div style="margin-top:20px;" class="modal-footer">
					<button class="btn btn-md btn-success" style="float:left" type="button" onclick="addLabor()">Add Labor</button>
					<button class="btn btn-md btn-warning" style="float:left;margin-left:5px" type="button" onclick="addPart()">Add Part</button>
					<button class="btn btn-md btn-danger" type="button" onclick="deleteJob()">Delete This Job</button>

					<button class="btn btn-md btn-primary" type="button" onclick="saveCannedJob()">Save This Job</button>
					<button class="btn btn-md btn-default" type="button" onclick="$('#editcannedmodal').modal('hide');$('#cannedjobid').val('')">Cancel</button>
				</div>
			</div>
		</div>
	</div>

	<!-- Apps Modal -->
	<div id="addlabormodal" class="modal fade" id="modal-large" style="z-index:9999" tabindex="-1" role="dialog" aria-hidden="true">
		<input type="hidden" id="cannedlaborid" value="0">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="block block-themed block-transparent remove-margin-b">
					<div class="block-header bg-primary-dark">
						<ul class="block-options">
							<li>
								<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
							</li>
						</ul>
						<h3 class="block-title">Edit / Add Labor</h3>
					</div>
					<div id="vehinfo" class="block-content"></div>
					<div class="block-content">
						<div class="row">
							<div class="col-md-12">
								<div class="col-sm-12">
									<div class="form-material floating">
										<input class="form-control sbp-form-control" style="padding:20px;" tabindex="1" type="text" id="addlaborname" name="addlaborname">
										<label id="editlabornamelabel" for="addlaborname">Labor Description</label>
									</div>
								</div>
								<div class="col-sm-12">
									<div class="form-material floating">
										<input class="form-control sbp-form-control" onblur="if (this.value.length === 0){this.value = 0}" style="padding:20px;" tabindex="2" type="text" id="addlabortime" name="addlabortime">
										<label id="editlabortimelabel" for="addlabortime">Labor Time</label>
									</div>
								</div>
								<div class="col-sm-12">
									<div class="form-material floating">
										<input class="form-control sbp-form-control" onblur="if (this.value.length == 0){this.value = 0}" style="padding:20px;" tabindex="3" type="text" id="addlaborflatprice" name="addlaborflatprice">
										<label id="editlaborpricelabel" for="addlaborflatprice">Flat Price for Labor</label>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div style="margin-top:20px;" class="modal-footer">
					<button class="btn btn-md btn-danger" style="float:left" type="button" onclick="delLabor()">Delete Labor</button>
					<button class="btn btn-md btn-primary" type="button" onclick="saveLabor()">Save</button>
					<button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
				</div>
			</div>
		</div>
	</div>

	<!-- Apps Modal -->
	<div id="addpartmodal" class="modal fade" id="modal-large" style="z-index:9999;border:2px black solid" tabindex="-1" role="dialog" aria-hidden="true">
		<input type="hidden" id="cannedpartid" value="0">
		<input type="hidden" id="cannedjobsid" value="0">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="block block-themed block-transparent remove-margin-b">
					<div class="block-header bg-primary-dark">
						<ul class="block-options">
							<li>
								<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
							</li>
						</ul>
						<h3 class="block-title">Edit/Add Part</h3>
					</div>
					<div id="vehinfo" class="block-content"></div>
					<div class="block-content">
						<div class="row">
							<div class="col-md-12">
								<div class="col-sm-12">
									<div class="form-material floating">
										<input class="form-control sbp-form-control" style="padding:20px;" tabindex="1" type="text" list="addpartdatalist" id="addpartnumber" name="addpartnumber">
										<datalist style="border:1px black solid" id="addpartdatalist">
											<option selected value="Select below">Select</option>
										</datalist>
										<label id="addpartnumberlabel" for="addpartnumber">Part Number</label>
									</div>
								</div>
								<div class="col-sm-12">
									<div class="form-material floating">
										<input class="form-control sbp-form-control" style="padding:20px;" tabindex="1" type="text" id="addpartdesc" name="addpartdesc">
										<label id="addpartdesclabel" for="addpartdesc">Part Description</label>
									</div>
								</div>
								<div class="col-sm-12">
									<div class="form-material floating">
										<select id="addsupplier" class="form-control sbp-form-control">
											<?php
											$stmt = "select suppliername s from supplier where shopid = ? order by displayorder";
											//printf ( str_replace('?',"'%s'",$stmt),$PartSupplier,$shopid);
											$matrix = strtolower($_COOKIE['matrix']);
											if ($query = $conn->prepare($stmt)) {
												$query->bind_param("s", $shopid);
												$query->execute();
												$result = $query->get_result();
												$query->store_result();
												$numrows = $result->num_rows;
												if ($numrows > 0) {
													while ($row = $result->fetch_array()) {

														echo "<option value='" . strtoupper($row['s']) . "'>" . strtoupper($row['s']) . "</option>";
													}
												} else {
													echo "<option value='none'>No Suppliers Entered</option>";
												}
											}
											?>
										</select>
										<label id="addsupplierlabel" for="addsupplier">Supplier</label>
									</div>
								</div>
								<div class="col-sm-12">
									<div class="form-material floating">
										<select id="addcategory" onchange="calcPrices()" class="form-control sbp-form-control">
											<?php
											$stmt = "select distinct category c from category where shopid = ? order by displayorder";
											$matrix = strtolower($_COOKIE['matrix']);
											if ($query = $conn->prepare($stmt)) {
												$query->bind_param("s", $shopid);
												$query->execute();
												$result = $query->get_result();
												$query->store_result();
												$numrows = $result->num_rows;
												if ($numrows > 0) {
													while ($row = $result->fetch_array()) {

														echo "<option value='" . strtoupper($row['c']) . "'>" . strtoupper($row['c']) . "</option>";
													}
												} else {
													echo "<option value='none'>No Suppliers Entered</option>";
												}
											}
											?>
										</select>
										<label id="addcategorylabel" for="addcategory">Category</label>
									</div>
								</div>
								<div class="col-sm-12">
									<div class="form-material floating">
										<select id="addpartcode" class="form-control sbp-form-control">
											<?php
											$stmt = "select codes from codes where shopid = ? order by codes asc";
											if ($query = $conn->prepare($stmt)) {
												$query->bind_param("s", $shopid);
												$query->execute();
												$result = $query->get_result();
												$query->store_result();
												$numrows = $result->num_rows;
												if ($numrows > 0) {
													while ($row = $result->fetch_array()) {
														echo '<option value="' . strtoupper($row['codes']) . '">' . strtoupper($row['codes']) . '</option>';
													}
												} else {
													echo '<option value="New">New</option>';
												}
											}
											?>
										</select>
										<label id="addpartcodelabel" for="addpartcode">Part Code</label>
									</div>
								</div>
								<div class="col-sm-12">
									<div class="form-material floating">
										<input class="form-control sbp-form-control" onblur="calcPrices()" style="padding:20px;" tabindex="2" type="text" id="addpartcost" name="addpartcost">
										<label id="addpartcostlabel" for="addpartcost">Shop Cost</label>
									</div>
								</div>
								<div class="col-sm-12">
									<div class="form-material floating">
										<input class="form-control sbp-form-control" style="padding:20px;" tabindex="2" type="text" id="addpartprice" name="addpartprice">
										<label id="addpartpricelabel" for="addpartprice">Selling Price</label>
									</div>
								</div>
								<div class="col-sm-12">
									<div class="form-material floating">
										<input class="form-control sbp-form-control" style="padding:20px;" tabindex="2" type="text" id="addquantity" name="addquantity">
										<label id="addquantitylabel" for="addquantity">Quantity</label>
									</div>
								</div>
								<div class="col-sm-12">
									<div class="form-material floating">
										<select class="form-control sbp-form-control" style="padding:20px;" tabindex="2" type="text" id="addtaxable" name="addtaxable">
											<option value="yes">Yes</option>
											<option value="no">No</option>
										</select>
										<label id="addtaxablelabel" for="addtaxable">Taxable</label>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div style="margin-top:20px;" class="modal-footer">
					<button class="btn btn-md btn-danger" type="button" style="float:left" onclick="delPart()">Delete Part</button>
					<button class="btn btn-md btn-primary" type="button" onclick="savePart()">Save</button>
					<button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
				</div>
			</div>
		</div>
	</div>
	<img alt="" id="spinner" src="<?= IMAGE ?>/loaderbig.gif">
	<script src="https://code.jquery.com/jquery-2.2.4.min.js"></script>
	<script src="<?= SCRIPT ?>/tipped.js"></script>
	<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>

	<!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
	<script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
	<script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
	<script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
	<script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
	<script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
	<script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
	<script src="<?= SCRIPT ?>/app.js"></script>
	<script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
	<script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
	<script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
	<script src="<?= SCRIPT ?>/plugins/findinpage/find.js"></script>
	<!-- Page Plugins -->

	<!-- Page JS Code
        <script src="assets/js/pages/base_pages_dashboard.js"></script>-->
	<script>
		function findOnPage() {

			clearHighlight()
			vals = $('#srch').val()
			$('div').highlight(vals)
			$('html, body').animate({
				scrollTop: ($('.highlight').first().offset().top - 100)
			}, 500);

		}

		function clearHighlight() {

			$('.highlight').removeClass('highlight').addClass('unhighlighted')
			$('#srch').val('')


		}



		jQuery(function() {

			$('#srch').keyup(function() {
				vals = $(this).val().toLowerCase()
				/*$('.srchbox').each(function(){
					v = $(this).html().toLowerCase()
					if (v.indexOf(vals) >= 0){
						$(this).parent().show()
						console.log(vals+":"+v)
					}else{
						$(this).parent().hide()
						console.log(vals+":"+v)
					}
				})*/
				//$('div').highlight(vals)

			})

			// Init page helpers (Slick Slider plugin)
			App.initHelpers('slick');
			<?php
			if (isset($_GET['id'])) {
				echo "swal('Canned Job has been saved');";
				echo 'history.pushState(null, "", location.href.split("?")[0]);';
			}
			if (isset($_GET['newid'])) {
				echo "editJob(" . $_GET['newid'] . ");";
				echo 'history.pushState(null, "", location.href.split("?")[0]);';
			}

			?>

			$('#addpartnumberlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
			$('#addpartdesclabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
			$('#addpartpricelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
			$('#addsupplierlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
			$('#addcategorylabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
			$('#addpartcostlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")


			$("#addpartnumber").on("input", function() {
				if ($(this).val().length >= 2) {
					var options = {};
					options.url = "cannedjobfiles/findpart.php";
					options.type = "GET";
					options.data = {
						"query": $("#addpartnumber").val()
					};
					options.dataType = "json";
					options.success = function(data) {
						//console.log(data)
						$("#addpartdatalist").empty();
						$.each(data, function(i, v) {
							datastr = "<option value='" + data[i].partnumber + "'></option>"
							$("#addpartdatalist").append(datastr).show();
							//console.log(datastr)
						});
					};
					$.ajax(options);
					//calcPrices()
				}
				v = $(this).val()
				//console.log("input val:"+v)
				if (v.indexOf("|") > 0) {
					//console.log("changing")
					infolist = $(this).val()
					// split on the " | " and complete the form
					console.log("infolist:" + infolist)
					infolist = infolist.replace("Part No:", "").replace("Desc:", "").replace("Price:", "").replace("Supplier:", "").replace("Category:", "").replace("Cost:", "").replace("ID:", "").replace("TAX:", "")
					infoarray = infolist.split(" | ")
					pn = infoarray[0]
					pd = infoarray[1]
					pp = infoarray[2]
					su = infoarray[3]
					ca = infoarray[4]
					co = infoarray[5]
					tax = infoarray[7]
					console.log("taxable:" + tax)
					$('#addpartnumber').val(pn)
					$('#addpartdesc').val(pd)
					$('#addpartprice').val(pp)
					$('#addsupplier').val(su)
					$('#addcategory').val(ca)
					$('#addpartcost').val(co)
					$('#addtaxable').val(tax)
					$('#addquantity').focus()
				}
			});

			$("#addpartdatalist").select(function() {
				console.log("clicked")
			});

			$("#addpartnumber").on("blur", function() {
				v = $(this).val()
				//console.log("blur val:"+v)
				if (v.indexOf("|") > 0) {
					infolist = $(this).val()
					// split on the " | " and complete the form
					infolist = infolist.replace("Part No:", "").replace("Desc:", "").replace("Price:", "").replace("Supplier:", "").replace("Category:", "").replace("Cost:", "").replace("ID:", "")
					infoarray = infolist.split(" | ")
					pn = infoarray[0]
					pd = infoarray[1]
					pp = infoarray[2]
					su = infoarray[3]
					ca = infoarray[4]
					co = infoarray[5]
					$('#addpartnumber').val(pn)
					$('#addpartdesc').val(pd)
					$('#addpartprice').val(pp)
					$('#addsupplier').val(su)
					$('#addcategory').val(ca)
					$('#addpartcost').val(co)
					$('#addquantity').focus()
				}
				//calcPrices()
			});
		});

		function calcPrices() {
			console.log("calcing")
			<?php
			if (strtolower($usematrix) == "yes") {
				$stmt = "select category,factor,start,end from category where shopid = ? order by Category, Start";

				if ($query = $conn->prepare($stmt)) {
					$query->bind_param("s", $shopid);
					$query->execute();
					$result = $query->get_result();
					$rs = array();
					while ($row = $result->fetch_assoc()) {
						$rs[] = $row;
					}
					echo "var clist = " . json_encode($rs) . "\r\n";
				}
			?>
				srch = $('#addcategory').val().toLowerCase()
				amt = $('#addpartcost').val()
				$.each(clist, function(i, v) {
					//console.log(JSON.stringify(i)+":"+JSON.stringify(v))
					if (v.category.toUpperCase() === srch.toUpperCase() && v.start <= amt && v.end >= amt) {
						console.log(JSON.stringify(i) + ":" + JSON.stringify(v))
						$('#addpartprice').val(Math.round((amt * v.factor) * 100) / 100)
					}
				});



			<?php
			}
			?>
		}

		function createCannedJob() {

			cjname = encodeURIComponent($('#cjname').val())
			cjtax = $('#cjtax').val()
			//console.log(cjname+":"+cjflat+":"+cjtax)///

			if (cjname.length == 0 || cjname == 'null') {
				swal("Job Name is Required");
				return;
			}

			$.ajax({
				data: "taxable=" + cjtax + "&shopid=<?php echo $shopid; ?>&type=newjob&cjname=" + cjname,
				url: "cannedjobfiles/cannedjob_new.php",
				success: function(r) {
					console.log(r)
					if (r.indexOf("|") > 0) {
						rar = r.split("|")
						newid = rar[1]
						location.href = 'cannedjobs_new.php?newid=' + newid
					} else {
						console.log(r)
					}
				}
			});

		}

		function saveCannedJob() {

			cannedid = $('#cannedjobid').val()
			editcjname = encodeURIComponent($('#editcjname').val())
			editcjtax = $('#editcjtax').val()
			$.ajax({
				data: "taxable=" + editcjtax + "&id=" + cannedid + "&jobname=" + editcjname + "&type=savejob&shopid=<?php echo $shopid; ?>",
				url: "cannedjobfiles/cannedjob_new.php",
				success: function(r) {
					if (r == "success") {
						location.href = 'cannedjobs_new.php?id=' + cannedid
					} else {
						console.log(r)
					}
				}
			});

		}

		function deleteJob() {

			cannedid = $('#cannedjobid').val()
			swal({
					title: "Are you sure?",
					text: "Are you sure you want to delete this Canned Job",
					type: "warning",
					showCancelButton: true,
					confirmButtonClass: "btn-danger",
					confirmButtonText: "Yes, delete it!",
					closeOnConfirm: false
				},
				function() {
					$('#spinner').show()
					$.ajax({
						data: "shopid=<?php echo $shopid; ?>&cannedid=" + cannedid + "&type=deletejob",
						url: "cannedjobfiles/cannedjob_new.php",
						success: function(r) {
							if (r == "success") {
								location.reload()
							} else {
								console.log(r)
							}
						}
					});
				});

		}

		function savePart() {

			pn = encodeURIComponent($('#addpartnumber').val())
			pd = encodeURIComponent($('#addpartdesc').val())
			su = encodeURIComponent($('#addsupplier').val())
			cat = encodeURIComponent($('#addcategory').val())
			pc = encodeURIComponent($('#addpartcode').val())
			pcost = encodeURIComponent($('#addpartcost').val())
			pp = encodeURIComponent($('#addpartprice').val())
			qty = encodeURIComponent($('#addquantity').val())
			tax = encodeURIComponent($('#addtaxable').val())
			id = $('#cannedpartid').val()

			console.log(cat == 'null')

			// check for required values
			if (pn.length == 0 || pn == 'null') {
				swal("Part Number is required");
				return;
			}
			if (pd.length == 0) {
				swal("Part Description is required");
				return
			}
			if (su.length == 0 || su == 'null' || su == '') {
				swal("Supplier is required");
				return
			}
			if (cat.length == 0 || cat == 'null' || cat == '') {
				swal("Part Category is required");
				return;
			}
			if (pc.length == 0 || pc == 'null' || pc == '') {
				swal("Part Code is required");
				return
			}
			if (pcost.length == 0 || !$.isNumeric(pcost)) {
				swal("Part Cost is required and must be a number");
				return
			}
			if (pp.length == 0 || !$.isNumeric(pp)) {
				swal("Selling Price is required and must be a number");
				return
			}
			if (qty.length == 0 || !$.isNumeric(qty)) {
				swal("Quantity is required and must be a number");
				return
			}
			cannedid = $('#cannedjobid').val()

			ds = "tax=" + tax + "&cannedid=" + cannedid + "&type=savepart&pn=" + pn + "&pd=" + pd + "&su=" + su + "&cat=" + cat + "&pc=" + pc + "&pcost=" + pcost + "&pp=" + pp + "&qty=" + qty + "&id=" + id + "&shopid=<?php echo $shopid; ?>"
			console.log(ds)
			$.ajax({
				data: ds,
				url: "cannedjobfiles/cannedjob_new.php",
				success: function(r) {
					if (r == "success") {
						$('#addpartmodal').modal('hide')
						cannedid = $('#cannedjobsid').val()
						getCannedJobDetails()
						$('#cannedjobsid').val("0")
						$('#cannedpartsid').val("0")
						$('#cannedlaborid').val("0")

					} else {
						console.log(r)
					}
				}
			});


		}

		function addPart() {

			$('#addpartmodal').modal('show')
			$('#addpartnumber').val()
			$('#addpartdesc').val()
			$('#addpartcost').val()
			$('#addpartprice').val()
			$('#addquantity').val()
			$('#cannedpartid').val(0)
			setTimeout(function() {
				$('#addpartnumber').focus()
			}, 500);
		}

		function editPart(id, cannedjobsid) {

			$('#cannedjobsid').val(cannedjobsid)
			ds = "type=editpart&shopid=<?php echo $shopid; ?>&partid=" + id
			$.ajax({
				data: ds,
				url: "cannedjobfiles/cannedjob_new.php",
				success: function(r) {
					console.log(r)
					//$partnumber."|".$partdescription."|".$supplier."|".$partcost."|".$partprice."|".$qty."|".$tax."|".$partcategory."|".$partcode."|".$partprice
					if (r.indexOf("|") > 0) {
						rar = r.split("|")
						partnumber = rar[0]
						partdescription = rar[1]
						supplier = rar[2]
						partcost = rar[3]
						partprice = rar[4]
						qty = rar[5]
						tax = rar[6]
						partcategory = rar[7]
						partcode = rar[8]
						$('#addpartnumber').val(partnumber)
						$('#addpartdesc').val(partdescription)
						$('#addsupplier').val(supplier)
						$('#addcategory').val(partcategory)
						$('#addpartcode').val(partcode)
						$('#addpartcost').val(partcost)
						$('#addpartprice').val(partprice)
						$('#addquantity').val(qty)
						$('#cannedpartid').val(id)
						$('#addtaxable').val(tax)

						$('#addpartnumberlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
						$('#addpartdesclabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
						$('#addsupplierlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
						$('#addcategorylabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
						$('#addpartcodelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
						$('#addpartcostlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
						$('#addpartpricelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
						$('#addquantitylabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
						$('#addtaxablelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
					}
				}
			});

			$('#addpartmodal').modal('show')
			$('#editcannedmodal').css('opacity', '0.4')
			$('#addpartmodal').on("hidden.bs.modal", function() {
				$('#editcannedmodal').css('opacity', '1.0')
			});
		}

		function editJob(id, fp) {
			$('#editcannedmodal').modal('hide')
			$('#canneddetails').html('')
			$('#cannedjobid').val(id)
			ds = "type=main&shopid=<?php echo $shopid; ?>&id=" + id + "&fp=" + fp
			console.log(ds)
			$.ajax({

				data: ds,
				url: "cannedjobfiles/cannedjob_new.php",
				success: function(r) {
					console.log(r)
					if (r.indexOf("success") >= 0) {
						rar = r.split("|")
						jobname = rar[1]
						price = rar[2]
						console.log(price + ":" + rar[2])
						taxable = rar[3]
						$('#editcjname').val(jobname)
						//$('#editcjflat').val(0)//.css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
						$('#editcjcalc').val(price)
						$('#editcjtax').val(taxable.toLowerCase())
						console.log($('#editcjtax').val() + ":" + taxable)
						$('#editcjcalclabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
						$('#editcjnamelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
						$('#editcjflatlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
						$('#editcjtaxlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
						$('#cannedjobid').val(id)
					}
				}

			});
			setTimeout('getCannedJobDetails()', 500)
			$('#editcannedmodal').modal('show')

		}

		function showCreateCannedJob() {

			$('#cannedmodal').modal('show')
			setTimeout(function() {
				$('#cjname').focus()
			}, 700)

		}

		function addLabor() {

			$('#editcannedmodal').css('opacity', '0.4')
			$('#addlabormodal').modal('show')
			console.log($('#cannedjobid').val())
			$('#addlaborname').val("")
			$('#addlabortime').val("")
			$('#addlaborflatprice').val("")
			$('#cannedlaborid').val(0)

			setTimeout(function() {
				$('#addlaborname').focus()
			}, 500)
			$('#addlabormodal').on("hidden.bs.modal", function() {
				$('#editcannedmodal').css('opacity', '1.0')
			});

		}

		function editLabor(id, labor, hours, price) {

			$('#editcannedmodal').css('opacity', '0.4')
			$('#addlabormodal').modal('show')
			setTimeout(function() {
				$('#addlaborname').val(labor)
				$('#addlabortime').val(hours)
				$('#cannedlaborid').val(id)
				$('#addlaborflatprice').val(price)
				$('#editlabornamelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
				$('#editlaborpricelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
				$('#editlabortimelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
			}, 500)
			$('#addlabormodal').on("hidden.bs.modal", function() {
				$('#editcannedmodal').css('opacity', '1.0')
			});


		}

		function saveLabor() {
			$('#spinner').show()
			labor = encodeURIComponent($('#addlaborname').val())
			hours = $('#addlabortime').val()
			id = $('#cannedlaborid').val()
			cannedid = $('#cannedjobid').val()
			addlaborflatprice = $('#addlaborflatprice').val()

			ds = "addlaborflatprice=" + addlaborflatprice + "&cannedjobid=" + cannedid + "&type=editlabor&labor=" + labor + "&hours=" + hours + "&laborid=" + id + "&shopid=<?php echo $shopid; ?>"
			console.log("save labor:" + ds)
			$.ajax({
				data: ds,
				url: "cannedjobfiles/cannedjob_new.php",
				success: function(r) {
					console.log(r)
					if (r == "success") {
						$('#addlabormodal').modal('hide')
						$('#editcannedmodal').css('opacity', '1.0')
						//getCannedJobDetails()
						//editJob(cannedid)
						location.href = 'cannedjobs_new.php?newid=' + cannedid
						$('#spinner').hide()
						$('#cannedlaborid').val("0")

					}
				},
				error: function(xhr, ajaxOptions, thrownError) {
					console.log(xhr.status);
					console.log(xhr.responseText);
					console.log(thrownError);
				}

			});


		}

		function delLabor() {

			swal({
					title: "Are you sure?",
					text: "Are you sure you want to delete this labor item",
					type: "warning",
					showCancelButton: true,
					confirmButtonClass: "btn-danger",
					confirmButtonText: "Yes, delete it!",
					closeOnConfirm: true
				},
				function() {
					$('#spinner').show()
					id = $('#cannedlaborid').val()
					cannedid = $('#cannedjobid').val()
					ds = "cannedjobid=" + cannedid + "&type=dellabor&laborid=" + id + "&shopid=<?php echo $shopid; ?>"
					console.log(ds)
					$.ajax({
						data: ds,
						url: "cannedjobfiles/cannedjob_new.php",
						success: function(r) {
							if (r == "success") {
								$('#addlabormodal').modal('hide')
								$('#editcannedmodal').css('opacity', '1.0')
								//editJob(cannedid)
								//getCannedJobDetails()
								location.href = 'cannedjobs_new.php?newid=' + cannedid
								$('#spinner').hide()
								$('#cannedlaborid').val("0")

							}
						}
					});
				});


		}

		function delPart() {

			swal({
					title: "Are you sure?",
					text: "Are you sure you want to delete this part",
					type: "warning",
					showCancelButton: true,
					confirmButtonClass: "btn-danger",
					confirmButtonText: "Yes, delete it!",
					closeOnConfirm: true
				},
				function() {
					$('#spinner').show()
					id = $('#cannedpartid').val()
					cannedid = $('#cannedjobid').val()
					ds = "cannedjobid=" + cannedid + "&type=delpart&partid=" + id + "&shopid=<?php echo $shopid; ?>"
					console.log(ds)
					$.ajax({
						data: ds,
						url: "cannedjobfiles/cannedjob_new.php",
						success: function(r) {
							if (r == "success") {
								$('#addpartmodal').modal('hide')
								$('#editcannedmodal').css('opacity', '1.0')
								getCannedJobDetails()
								$('#spinner').hide()
								$('#cannedpartid').val("0")

							}
						}
					});
				});


		}


		$('#addlabormodal').on('hidden.bs.modal', function() {
			$('#editcannedmodal').modal('show')
		});

		function getCannedJobDetails() {
			$('#spinner').show()
			cannedid = $('#cannedjobid').val()
			console.log(cannedid)
			ds = "type=sub&shopid=<?php echo $shopid; ?>&id=" + cannedid
			//console.log("cannedjobfiles/cannedjob.php?type=sub&shopid=<?php echo $shopid; ?>&id="+cannedid)
			$.ajax({

				data: ds,
				url: "cannedjobfiles/cannedjob_new.php",
				success: function(r) {
					//console.log(r)
					$('#canneddetails').html(r)
					$('#spinner').hide()
				}
			});

		}
	</script>

</body>

</html>
<?php
mysqli_close($conn);
?>