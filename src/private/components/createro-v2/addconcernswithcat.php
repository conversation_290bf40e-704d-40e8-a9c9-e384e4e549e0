<?php

$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);

$date = new DateTime('now');
$component = "createro-v2";
// Page Variables
$title = 'Create RO';
$subtitle = "";

include getHeadGlobal('');
include getHeadComponent($component);
include getRulesGlobal($component);
echo "<body>";
include getHeaderGlobal($component);
include getMenuGlobal($component);

if (isset($_GET['oshopid'])) {
    $oshopid = filter_var($_GET['oshopid'], FILTER_SANITIZE_STRING);
} else {
    $oshopid = $shopid;
}
if (isset($_GET['cid'])) {
    $cid = filter_var($_GET['cid'], FILTER_SANITIZE_STRING);
} else {

    $cid = 0;
}

if (isset($_GET['schid']))
    $quotestr = "schid=" . filter_var($_GET['schid'], FILTER_SANITIZE_STRING) . "&";
elseif (isset($_GET['quoteid'])) {
    $quotestr = "quoteid=" . filter_var($_GET['quoteid'], FILTER_SANITIZE_STRING) . "&";
} else {
    $quotestr = "";
}

$currmiles = "0";
$milesout = "0";
$currentmileage = "0";

if (isset($_GET['vid'])) {
    $vid = filter_var($_GET['vid'], FILTER_SANITIZE_STRING);
    $stmt = "select currentmileage from vehicles where shopid = '$shopid' and vehid = $vid";
    //echo $stmt;
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->store_result();
        $query->bind_result($currmiles);
        $query->fetch();
    }

    $stmt = "select milesout from repairorders where shopid = '$shopid' and vehid = $vid and status = 'Closed' and rotype != 'NO APPROVAL' order by roid desc,statusdate desc limit 1";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->store_result();
        $query->bind_result($milesout);
        $query->fetch();
    }
} elseif (isset($_GET['vehid'])) {
    $vid = filter_var($_GET['vehid'], FILTER_SANITIZE_STRING);

    $stmt = "select currentmileage from vehicles where shopid = '$shopid' and vehid = $vid";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->store_result();
        $query->bind_result($currmiles);
    }

    $stmt = "select milesout from repairorders where shopid = '$shopid' and vehid = $vid and status = 'Closed' and rotype != 'NO APPROVAL' order by roid desc,statusdate desc limit 1";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->store_result();
        $query->bind_result($milesout);
        $query->fetch();
    }
}

$usepreviousmileage = "yes";
$stmt = "select notesalert,usepreviousmileage from settings where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($notesalert, $usepreviousmileage);
    $query->fetch();
    $query->close();
}

if (strtolower($usepreviousmileage) == "no")
    $currentmileage = '';
elseif ($currmiles >= $milesout) {
    $currentmileage = $currmiles;
} elseif ($milesout > $currmiles) {
    $currentmileage = $milesout;
}

$cstmt = "select catname from complaintcats where shopid = '$shopid' order by catname";
$comcats = "<option value='none'>No Category</option>";
if ($cquery = $conn->prepare($cstmt)) {
    $cquery->execute();
    $cresult = $cquery->get_result();
    $cquery->store_result();
    while ($crow = $cresult->fetch_assoc()) {
        $comcats .= "<option value='" . strtoupper($crow["catname"]) . "'>" . strtoupper($crow["catname"]) . "</option>";
    }
}

$stmt = "select notes from customer where shopid = ? and customerid=?";

$notes = '';
if ($query = $conn->prepare($stmt)) {

    $query->bind_param("ss", $shopid, $cid);
    $query->execute();
    $query->bind_result($notes);
    $query->fetch();
    $query->close();
}

?>

<style>

    body {
        height: auto !important;
    }

    main {
        min-height: 100vh;
    }

    #concern-table tr td {
        color: var(--textColor) !important;
    }
</style>

<!-- Main Container -->
<main id="reports">
    <div class="report">
        <div class="col-12">
            <div class="row">
                <div class="col-md-12 col-sm-12">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="#" class="text-secondary">Create RO</h2>
                            <span class="text-secondary ps-3 pe-3">/</span>
                            <a href="<?= COMPONENTS_PRIVATE ?>/v2/customer/customer-search.php" class="text-secondary">Customers</a>
                            <span class="text-secondary ps-3 pe-3">/</span>
                            <h2>
                                Add Concerns
                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" aria-label="Enter your Customers Concerns below. All parts and labor will be grouped by these Customer Concerns" data-mdb-original-title="Enter your Customers Concerns below. All parts and labor will be grouped by these Customer Concerns"></i>
                            </h2>
                        </a>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>

    <div class="row pt-2">
        <form name="mainform" id="mainform">
            <div class="row">
                <div class="col-md-8">
                        <?php
                        for ($i = 1; $i < 16; $i++) {
                            ?>
                        <div class="row">
                            <div class="col-md-8" id="g<?= $i?>" style="display: <?= $i>=6?'none':''?>">
                                <div class="form-outline mb-4">
                                    <textarea class="form-control conctext" rows="2" tabindex="1" id="c<?= $i?>" name="c<?= $i?>" ai-writing-tool></textarea>
                                    <label class="form-label" for="c<?= $i?>">Concern #<?= $i?></label>
                                </div>      
                            </div>
                            <div class="col-md-4" id="sg<?= $i?>" style="display: <?= $i>=6?'none':''?>">
                                <div class="form-row mb-4">
                                    <select class="<?= $i<=5?'select':''?> cat-select" id="s<?= $i?>" name="s<?= $i?>"><?php echo $comcats; ?></select>
                                    <label class="form-label select-label" for="s<?= $i?>">Select Category</label>
                                </div>  
                            </div>
                        </div>

                            <?php
                        }
                        ?>

                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-outline mb-4">
                                <input class="form-control" tabindex="1" type="text" id="currmileage" name="currmileage"
                                       value="<?php echo $currentmileage; ?>">
                                <label class="form-label" for="currmileage">Current
                                    Mileage</label>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-outline mb-4">
                                <input class="form-control" tabindex="1" type="text" id="tagnum" name="tagnum">
                                <label class="form-label" for="tagnum">Tag / Hat
                                    Number</label>
                            </div>
                        </div>

                        <div class="col-sm-12">
                            <button type="button" onclick="addBoxes()" class="btn btn-secondary mt-2">Add More Boxes
                            </button>
                            <button id="addconcernsbtn" type="button" onclick="addConcerns()"
                                    class="btn btn-primary btn-md mt-2 mx-sm-0 mx-lg-2">Add Customer Concerns
                            </button>
                            <button type="button" onclick="showRecRepairs(true)" class="btn btn-secondary mt-2">Show
                                Recommended Repairs
                            </button>
                        </div>


                        <div class="col-sm-12 text-secondary">
                            <?php
                            $tcnt = 0;
                            $tro = 0;
                            $sdate = "";
                            // now we fetch customer history
                            $stmt = "select count(*) c, sum(totalro) tro from repairorders where shopid = ? and customerid = ? and status = 'closed' and rotype != 'no approval'";
                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("si", $shopid, $cid);
                                $query->execute();
                                $query->bind_result($tcnt, $tro);
                                $query->fetch();
                                $query->close();
                            }

                            // get last visit
                            $stmt = "select statusdate from repairorders where shopid = ? and customerid = ? and status = 'closed' and rotype != 'no approval' order by statusdate desc,roid limit 1";
                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("si", $shopid, $cid);
                                $query->execute();
                                $query->bind_result($sdate);
                                $query->fetch();
                                $query->close();
                            }

                            if ($tcnt > 0) {
                                $tavg = round($tro / $tcnt, 2);
                                echo "<br><span>Customer Sales Data:<br>";
                                echo "RO Count: " . $tcnt . " @ " . $tavg . " = " . round($tro, 2);
                                echo "<br>Last RO: " . date("m/d/Y", strtotime($sdate)) . "</span>";
                            }
                            ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="row">
                                <div class="col-md-7 mb-2">
                                    <div class="form-outline">
                                        <input name="search" id="search" class="form-control" type="text"/>
                                        <label class="form-label" for="search">Search Master Concern List</label>
                                    </div>
                                </div>

                                <div class="col-md-5 text-end mb-2">
                                    <a href="#" class="text-primary" onclick="showListDialog()"><i
                                                class="fas fa-plus-circle"></i> Add to Master List</a>
                                </div>
                            </div>
                            <div class="row">
                                <div id="concerns" class="col-md-12 sbp-concerns"
                                     style="max-height:80vh;overflow-y:scroll"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </form>
</main>
<!-- END Main Container -->

<div id="prepaidmodal" class="modal fade" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content p-4">
            <div class="modal-header">
                <h4 class="modal-title">Prepaid Jobs Available</h4>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <table id="pptable" class="table table-sm table-striped table-bordered table-hover">
                </table>
            </div>
        </div>

    </div>
</div>

<div id="techmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <input id="recid" type="hidden">
    <div class="modal-dialog modal-md">
        <div class="modal-content p-4">
            <div class="modal-header">
                <h4 class="modal-title">Select Tech for Labor</h4>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>


            <div id="techinfo" class="modal-body scrollbox"></div>

            <div class="modal-footer d-flex justify-content-center">
                <button class="btn btn-md btn-primary" id="btn-tech" type="button" onclick="restoreIt()">
                    Restore
                </button>
            </div>
        </div>
    </div>
</div>

<div id="recrepairmodal" class="modal fade" role="dialog">
    <div class="modal-dialog modal-lg">

        <!-- Modal content-->
        <div class="modal-content p-4">
            <div class="modal-header">
                <h4 class="modal-title">Recommended Repairs</h4>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <table id="rectable" class="table table-sm table-bordered table-hover">
                    <thead>
                    <tr>
                        <td>&nbsp;</td>
                        <td>RO #</td>
                        <td>Description</td>
                        <td>Category</td>
                        <td class="text-right">Price</td>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>

    </div>
</div>

<div id="listmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <input id="customerid" type="hidden">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header">
                <h4 class="modal-title">Add to Master Concern List</h4>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-outline mb-4">
                            <input class="form-control" tabindex="1" type="text" id="job" name="job">
                            <label class="form-label" id="joblabel" for="material-text2">Customer Concern</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <div class="col d-flex justify-content-center align-items-center">
                    <button class="btn btn-md btn-primary" type="button" onclick="addCustomerConcern()">Add</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="cannedModal" class="modal fade" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content p-4">
            <div class="modal-header">
                <h4 class="modal-title" id="cannedModalTitle">Adding Canned Job Issue</h4>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">Select Technician</div>
                    <div class="col-md-6">
                        <select class="select" name="tech" id="tech">
                            <?php
                            $stmt = "select employeefirst,employeelast from employees where shopid = '$shopid' and active = 'yes' and showtechlist = 'yes'";
                            if ($query = $conn->prepare($stmt)) {
                                $query->execute();
                                $query->store_result();
                                $num_roid_rows = $query->num_rows;
                                if ($num_roid_rows > 0) {
                                    $result = $conn->query($stmt);
                                    while ($row = $result->fetch_array()) {
                                        echo "<option>" . $row["employeelast"] . ", " . $row["employeefirst"] . "</option>";
                                    }
                                }
                                $query->close();
                            }
                            ?>
                        </select>
                    </div>
                </div>
                <br>
                <div class="row">
                    <input type="hidden" id="cannedjobid" name="cannedjobid">
                    <input type="hidden" id="cannedjobname" name="cannedjobname">
                    <div class="col-md-6">Select Hourly Rate</div>
                    <div class="col-md-6">
                        <?php

                        $rquery = "select HourlyRate,hourlyrate2,hourlyrate3,hourlyrate4,hourlyrate5,hourlyrate6,hourlyrate7,hourlyrate8,hourlyrate9,hourlyrate10,hourlyrate1label,hourlyrate2label,hourlyrate3label,hourlyrate4label,hourlyrate5label,hourlyrate6label,hourlyrate7label,hourlyrate8label,hourlyrate9label,hourlyrate10label from company where shopid = ?";
                        if ($rstmt = $conn->prepare($rquery)) {
                            $rstmt->bind_param("s", $shopid);
                            $rstmt->execute();
                            $rresult = $rstmt->bind_result($hrate, $hrate2, $hrate3, $hrate4, $hrate5, $hrate6, $hrate7, $hrate8, $hrate9, $hrate10, $hlabel1, $hlabel2, $hlabel3, $hlabel4, $hlabel5, $hlabel6, $hlabel7, $hlabel8, $hlabel9, $hlabel10);
                            $rstmt->fetch();
                            $rstmt->close();
                        }
                        if (strlen($hlabel2) != '') {
                            ?>
                            <select class="select" name="hourlyrate" style="text-transform:uppercase" id="hourlyrate"
                                    onchange="$('#ratelabel').val(this.options[this.selectedIndex].getAttribute('lab'))">
                                <option lab="<?= $hlabel1 ?>"
                                        value="<?php echo $hrate; ?>"><?php echo strtoupper($hlabel1) . "-" . number_format($hrate, 2); ?></option>
                                <option lab="<?= $hlabel2 ?>"
                                        value="<?php echo $hrate2; ?>"><?php echo strtoupper($hlabel2) . "-" . number_format($hrate2, 2); ?></option>
                                <?php if ($hlabel3 != '') { ?>
                                    <option lab="<?= $hlabel3 ?>"
                                            value="<?php echo $hrate3; ?>"><?php echo strtoupper($hlabel3) . "-" . number_format($hrate3, 2); ?></option>
                                <?php } ?>
                                <?php if ($hlabel4 != '') { ?>
                                    <option lab="<?= $hlabel4 ?>"
                                            value="<?php echo $hrate4; ?>"><?php echo strtoupper($hlabel4) . "-" . number_format($hrate4, 2); ?></option>
                                <?php } ?>
                                <?php if ($hlabel5 != '') { ?>
                                    <option lab="<?= $hlabel5 ?>"
                                            value="<?php echo $hrate5; ?>"><?php echo strtoupper($hlabel5) . "-" . number_format($hrate5, 2); ?></option>
                                <?php } ?>
                                <?php if ($hlabel6 != '') { ?>
                                    <option lab="<?= $hlabel6 ?>"
                                            value="<?php echo $hrate6; ?>"><?php echo strtoupper($hlabel6) . "-" . number_format($hrate6, 2); ?></option>
                                <?php } ?>
                                <?php if ($hlabel7 != '') { ?>
                                    <option lab="<?= $hlabel7 ?>"
                                            value="<?php echo $hrate7; ?>"><?php echo strtoupper($hlabel7) . "-" . number_format($hrate7, 2); ?></option>
                                <?php } ?>
                                <?php if ($hlabel8 != '') { ?>
                                    <option lab="<?= $hlabel8 ?>"
                                            value="<?php echo $hrate8; ?>"><?php echo strtoupper($hlabel8) . "-" . number_format($hrate8, 2); ?></option>
                                <?php } ?>
                                <?php if ($hlabel9 != '') { ?>
                                    <option lab="<?= $hlabel9 ?>"
                                            value="<?php echo $hrate9; ?>"><?php echo strtoupper($hlabel9) . "-" . number_format($hrate9, 2); ?></option>
                                <?php } ?>
                                <?php if ($hlabel10 != '') { ?>
                                    <option lab="<?= $hlabel10 ?>"
                                            value="<?php echo $hrate10; ?>"><?php echo strtoupper($hlabel10) . "-" . number_format($hrate10, 2); ?></option>
                                <?php } ?>
                            </select>
                            <?php
                        } else {
                            ?>
                            <input required type="text" name="hourlyrate" id="hourlyrate"
                                   value="<?php echo number_format($hrate, 2); ?>" class="form-control">
                            <?php
                        }
                        ?>

                        <input type="hidden" name="ratelabel" id="ratelabel" value="<?= $hlabel1 ?>">
                    </div>
                </div>
                <br>
                <div class="row">
                    <div class="col-md-6">Select Category</div>
                    <div class="col-md-6">
                        <select class="select" name="cat" id="cat">
                            <?php echo $comcats; ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-md" onclick="saveCannedJob()">Save</button>
            </div>
        </div>
    </div>
</div>

<div id="recommed_compare" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header">
                <h4 class="modal-title" id="myModalLabel">Restore Recommended Repair</h4>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <img src="<?= IMAGE ?>/loaderbig.gif" id="" style="width: 120px">
                </div>
            </div>
        </div>
    </div>
</div>

<?php
include(AI_WRITING_TOOL);
include getScriptsGlobal('');
include getScriptsComponent($component);
include getFooterComponent($component);
?>
<script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
<!-- Page Plugins -->

<script>
    var rr_version = "old";

    function addConcerns() {
        showLoader();
        $('#addconcernsbtn').prop("disabled", true);

        <?php if (isset($_GET['quoteid'])) { ?>

        var cadd = false;

        $("textarea.conctext").each(function () {
            if ($(this).val() != '') cadd = true;
        })

        if (!cadd) {
            sbalert("You must choose a customer concern in order for quote to be converted")
            hideLoader();
            $('#addconcernsbtn').prop("disabled", false);
            return
        }

        <?php } ?>

        var recsjson = JSON.stringify(recids)
        var rectechsjson = JSON.stringify(rectechs)
        var cannedjson = JSON.stringify(cannedjobs)
        var recrepairsjson = JSON.stringify(recrepairs)
        waiter = localStorage.getItem("waiter")
        if (waiter)
        localStorage.removeItem("waiter")
        else
        waiter = ''

        ds = "waiter="+waiter+"&schid=<?= isset($_GET['schid']) ? $_GET['schid'] : '' ?>&oshopid=<?php echo $oshopid; ?>&cid=<?php echo $cid; ?>&vid=<?php echo $vid; ?>&shopid=<?php echo $shopid; ?>&" + $('#mainform').serialize()+"&cannedjson="+cannedjson+"&recrepairsjson="+recrepairsjson
console.log(waiter)
       $.ajax({
            data: ds,
            type: "post",
            url: "createro_action.php",
            success: function (r) {
                hideLoader();
                if (r.indexOf("|") > 0) {
                    rar = r.split("|")
                    var newroid = rar[1]
                    if (recrepairsjson != '[]') {

                        var param = "restore_existing"
                        if (rr_version == "new") {
                            var param = "restore_existing_new"
                        }
                        ds = "t=" + param + "&shopid=<?php echo $shopid; ?>&roid=" + newroid + "&recrepairsjson="+recrepairsjson+"&recs=" + recsjson + "&rectechs=" + rectechsjson
                        $.ajax({
                            data: ds,
                            type: "post",
                            url: "../ro/recrepairsedit.php",
                            success: function (r) {
                                if (cannedjson != '[]')
                                    processCannedjobs(newroid)
                                else {
                                         location.href = '<?= COMPONENTS_PRIVATE ?>/v2/ro/ro.php?<?php echo $quotestr; ?>roid=' + newroid
                                }
                            }

                        });
                    } else if (cannedjson != '[]')
                        processCannedjobs(newroid)
                    else {
                           location.href = '<?= COMPONENTS_PRIVATE ?>/v2/ro/ro.php?<?php echo $quotestr; ?>roid=' + newroid
                    }
                } else {
                    sbalert("You must have a valid vehicle and customer to create an RO")
                }
            }

        });


    }

    function processCannedjobs(roid) {
        var cannedjson = JSON.stringify(cannedjobs)

        ds = "shopid=<?php echo $shopid; ?>&roid=" + roid + "&cannedjson=" + cannedjson
        $.ajax({
            data: ds,
            type: "post",
            url: "addcannedjobs.php",
            success: function (r) {
                location.href = '<?= COMPONENTS_PRIVATE ?>/v2/ro/ro.php?<?php echo $quotestr; ?>roid=' + roid
            }

        });
    }

    function showListDialog(event) {
        $('#listmodal').modal('show')
        $('#job').val($('#search').val())
    }

    function addCustomerConcern() {
        j = encodeURIComponent($('#job').val())
        ds = "shopid=<?php echo $shopid; ?>&sd=" + j

        showLoader();

        $.ajax({
            data: ds,
            url: "addtoconcernlist.php",
            type: "post",
            success: function (r) {
                $.ajax({
                    data: "search=&shopid=<?php echo $shopid; ?>",
                    url: "getconcerns.php",
                    success: function (result) {
                        $('#concerns').html(result)
                        $('#listmodal').modal('hide')
                    }
                });
            }
        });

        hideLoader()
    }

    function resize_doc() {
        h = $(document).height() - 270 + "px"
        $('#concerns').height(h).css("max-height", h)
    }

    $(document).ready(function () {

        recids = new Array();
        rectechs = new Array();
        cannedjobs = new Array();
        concerns = new Array();
        recrepairs = new Array();

        if (typeof (Storage) !== "undefined") {
            c1 = $('textarea#c1').val()
            svc = localStorage.getItem("svc")
            if (svc) {
                if (c1.length > 0) {
                    $('textarea#c1').val($('#c1').val() + ", " + svc).focus()
                } else {
                    $('textarea#c1').val(svc).focus()
                }
            }
            localStorage.removeItem("svc");
        }

        showLoader();

        $.ajax({
            data: "search=&shopid=<?php echo $shopid; ?>",
            url: "getconcerns.php",
            success: function (r) {
                $('#concerns').html(r)
            }
        });

        hideLoader()

        $(window).resize(function () {
            resize_doc();
        });

        resize_doc();

        $('#createro').click();
        $('#search').focus()

        <?php if ($notesalert == 'yes' && !empty($notes)) { ?>
        sbalert("<?= preg_replace("/(\r|\n)/", " ", $notes) ?>")
        <?php } ?>

        showRecRepairs();

        $("#mainform textarea").on("keyup", function(e) {
         //   console.log("text area changed")
            if ($(this).val() == ""){
                orderToRemove = $(this).attr("id");
                //check if this was a canned job
                cannedjobs = cannedjobs.filter(job => job.field !== orderToRemove);
                recrepairs = recrepairs.filter(job => job.field !== orderToRemove);


            } else {
                fieldToUpdate = $(this).attr("id");
                const jobToUpdate = cannedjobs.find(job => job.field === fieldToUpdate);
                if (jobToUpdate) {
                    jobToUpdate.issue = encodeURIComponent($(this).val());
                }
                const recjobToUpdate = recrepairs.find(job => job.field === fieldToUpdate);
                if (recjobToUpdate) {
                    recjobToUpdate.issue = encodeURIComponent($(this).val());
                }
            }
        });

        $(".cat-select").on("change", function(e) {
          //  console.log("category changed");
            fieldToUpdate = "c"+$(this).attr("id").substring(1);
         //   console.log(fieldToUpdate);
            const jobToUpdate = cannedjobs.find(job => job.field === fieldToUpdate);
            if (jobToUpdate) {
          //      console.log("category updated to "+ $(this).val());
                jobToUpdate.category = $(this).val();
            }
            const recjobToUpdate = recrepairs.find(job => job.field === fieldToUpdate);
            if (recjobToUpdate) {
          //      console.log("category updated to "+ $(this).val());
                recjobToUpdate.category = $(this).val();
            }
        })
    });

    $('#search').keyup(function () {
        showLoader();
        searchTable($(this).val());
    });

    function searchTable(inputVal) {
        var table = $('#concern-table');

        table.find('tr').each(function (index, row) {
            var allCells = $(row).find('td');
            if (allCells.length > 0) {
                var found = false;
                allCells.each(function (index, td) {
                    var regExp = new RegExp(inputVal, 'i');
                    if (regExp.test($(td).text())) {
                        found = true;
                        return false;
                    }
                });
                if (found == true) {
                    $(row).show();
                    hideLoader();
                } else {
                    $(row).hide();
                    hideLoader();
                    $('#hide').toggle()
                }
            }
        });
    }

    function addBoxes() {
        if ($('#g').val() == 15){
         sbalert("Fifteen is the maximum number that can be added here.  To add more, you must add them from the RO")
        }
        if ($('#g').val() == 10){
            $('#g11').fadeIn()
            $('#g12').fadeIn()
            $('#g13').fadeIn()
            $('#g14').fadeIn()
            $('#g15').fadeIn()
            $('#sg11').fadeIn()
            $('#sg12').fadeIn()
            $('#sg13').fadeIn()
            $('#sg14').fadeIn()
            $('#sg15').fadeIn()
            $('#g').val(15)
            for(var i=11;i<=15;i++)
            new mdb.Select(document.getElementById('s'+i))  
        }
    
        if ($('#g').val() == 5){
            $('#g6').fadeIn()
            $('#g7').fadeIn()
            $('#g8').fadeIn()
            $('#g9').fadeIn()
            $('#g10').fadeIn()
            $('#sg6').fadeIn()
            $('#sg7').fadeIn()
            $('#sg8').fadeIn()
            $('#sg9').fadeIn()
            $('#sg10').fadeIn()
            $('#g').val(10)
            for(var i=6;i<=10;i++)
            new mdb.Select(document.getElementById('s'+i))  
        }
    }

    function addToList(v) {
        if (concerns.length == 15) {
            sbalert("Fifteen is the maximum number that can be added here.  To add more, you must add them from the RO")
            return false;
        }

        concerns.push(v);

        $('textarea.conctext').each(function () {
            if ($(this).val() == "") {
                $(this).val(v).toggleClass("active")
                return false;
            }
        });
    }

    function addCannedJob(id, jobname) {
        // get canned job info and open a modal
        $('#cannedModal').modal('show')
        $('#cannedModalTitle').html(jobname.toUpperCase())
        $('#cannedjobid').val(id)
        $('#cannedjobname').val(jobname)
    }

    function saveCannedJob() {
        let length = $('textarea.conctext').toArray().findIndex(el => $(el).val() === "") + 1;
        
        if (length == 15) {
            sbalert("Fifteen is the maximum number that can be added here.  To add more, you must add them from the RO")
            return false;
        }

        cannedid = $('#cannedjobid').val()
        tech = $('#tech').val()
        rate = $('#hourlyrate').val()
        ratelabel = $('#ratelabel').val()
        cat = $('#cat').val()
        issue = $('#cannedjobname').val()

        concerns.push({
            "id": cannedid,
            "rate": rate,
            "ratelabel": ratelabel,
            "tech": tech,
            "cat": cat
        });

        cannedjobs.push({
            "id": cannedid,
            "rate": rate,
            "ratelabel": ratelabel,
            "tech": tech,
            "cat": cat,
            "order": length,
            "field" : 'c'+length,
            "issue" : encodeURIComponent(issue),
            "category" : cat
        });

        $('[name=c' + length + ']').val(issue).toggleClass("active").focus();
        $('[name=c' + length + ']').blur();
      //  $('[name=c' + length + ']').attr('disabled', 'disabled');
        $('[name=s' + length + ']').val(cat).toggleClass("active").focus();
        $('[name=s' + length + ']').blur();
     //   $('[name=s' + length + ']').attr('disabled', 'disabled');

        $('#cannedModal').modal('hide')
    }

    function deleteMasterConcern(id) {
        sbconfirm(
            'Are you sure?',
            'Are you sure you want to delete this from your Master Concern List',
            function () {
                showLoader();
                $.ajax({
                    data: "shopid=<?php echo $shopid; ?>&jobid=" + id,
                    url: "delconcern.php",
                    type: "post",
                    success: function (r) {
                        if (r == "success") {
                            $.ajax({
                                data: "search=&shopid=<?php echo $shopid; ?>",
                                url: "getconcerns.php",
                                success: function (r) {
                                    $('#concerns').html(r)
                                }
                            });
                            hideLoader();
                        }
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        hideLoader();
                    },

                })
            }
        )
    }

    function showRecRepairs(clicked) {
        showLoader();
        let recrepids = recrepairs.map(obj => obj.id);
        $.ajax({
            data: "shopid=<?php echo $shopid; ?>&vid=<?php echo $vid; ?>&t=getrecrepairs&recs=" + JSON.stringify(recrepids),
            url: "recrepairs.php",
            type: "post",
            error: function (xhr, ajaxOptions, thrownError) {
                hideLoader();
            },
            success: function (r) {
                if (r.trim() != '') {
                    $('#rectable>tbody').html(r)
                    $('#recrepairmodal').modal('show');
                } else {
                    if (clicked) {
                        sbalert("No recommended repairs found for this vehicle");
                    }
                }
                hideLoader();
            }

        });

    }

    function addRecRepair(shopid, id, elem) {
        //GET comparison, if there is a difference show it otherwise regular flow
        ds = "t=get_comparison&shopid=" + shopid + "&id=" + id
        $(elem).prop("disabled", true);
        showLoader();
        $.ajax({
            data: ds,
            type: "POST",
            url: "recrepairs.php",
            success: function (r) {
                $("#recommed_compare .modal-body").html(r)
                hideLoader();
            },
        });
        $("#recrepairmodal").on('hidden.bs.modal', function (e) {
            $(elem).prop("disabled", false);
        });
        $("#recommed_compare").on('hidden.bs.modal', function (e) {
            $("#recommed_compare .modal-body").html('');
            $(elem).prop("disabled", false);
        });
        // get the list of labor and available techs.
    }

    function restoreIt() {
        // first make sure there are techs selected
        nonelist = ""
        showLoader();
        $('select.techbox').each(function () {
            if ($(this).val() == "none") {
                nonelist += "none,"
            } else {
                $techar = $(this).val().split("|")
                rectechs.push({
                    "id": $(this).attr("id").replace("techbox", ""),
                    "first": $techar[1],
                    "last": $techar[0],
                    "techid": $techar[2],
                    "techrate": $techar[3]
                })
            }
        })
        techjson = JSON.stringify(rectechs)
        if (nonelist.indexOf(",") >= 0) {
            sbalert("You must select a Tech for each job")
            hideLoader();
        } else {
            shopid = '<?php echo $shopid; ?>'
            recid = $('#recid').val()
            recids.push(recid)
            $('#btn-tech').attr('disabled', 'disabled')
            // add the maint list as complaint to complaints for the ro
            ds = "t=restore&shopid=" + shopid + "&recid=" + recid
            $.ajax({
                data: ds,
                type: "post",
                url: "recrepairs.php",
                success: function (r) {
                    if (r.indexOf("|") > 0) {
                        rar = r.split("|")
                        concerns.push(rar[1]);
                        var rcat = "none";
                        if (typeof rar[2] != "undefined"){
                            rcat = rar[2].trim();
                        }

                        length = concerns.length;

                        console.log(rcat, length,  $('[name=c' + length + ']'));

                        $('[name=s' + length + ']').val(rcat).toggleClass("active").focus();
                        $('[name=s' + length + ']').blur();

                        $('[name=c' + length + ']').val(rar[1]).toggleClass("active").focus().scrollTop(0);
                        $('[name=c' + length + ']').blur();

                     //   $('[name=c' + length + ']').attr('disabled', 'disabled');

                        recrepairs.push({
                            "id": recid,
                            "order": length,
                            "field" : 'c'+length,
                            "issue" : encodeURIComponent(rar[1]),
                            "techs" : rectechs,
                            "category" : rcat
                        });

                        $('#techmodal').modal('hide')
                        $('#btn-tech').attr('disabled', false)
                    }

                    $('#btn-tech').attr('disabled', false);
                    hideLoader();
                }
            });
            $('.form-control.techbox').remove()
            $('#techmodal').modal('hide')
        }

    }
</script>

<input type="hidden" id="g" value="5">
</body>
<?php 
    if (isset($conn)) {
        mysqli_close($conn);
    } 
?>

</html>