<?php
/*
 * This file is preloaded via PHP auto_prepend_file directive.
 */
//short constant to be used if needed. Recommended to be used as we are considering migration.
define('DS', DIRECTORY_SEPARATOR);
//Server Root URL.
define("ROOT", $_SERVER['SERVER_NAME']);
//path to /src folder
define("SRC", $_SERVER['DOCUMENT_ROOT'] . DS . "src");
//Path to private directory
define('PRIVATE_PATH', SRC . DS . "private");
//path to private components
define('COMPONENTS_PRIVATE_PATH', PRIVATE_PATH.DS."components");
//URL to private components
define("COMPONENTS_PRIVATE", 'https://' . $_SERVER['SERVER_NAME']);
//define("COMPONENTS_PRIVATE", 'https://' . $_SERVER['SERVER_NAME'] . '/src/private/components');
//URL to private includes
define("INCLUDES", "https://" . $_SERVER['SERVER_NAME'] . "/src/private/includes");
//path to private includes
define("INCLUDES_DIR", $_SERVER['DOCUMENT_ROOT'] . "/src/private/includes");
//URL to pages to contain singular pages
define("PAGES", "https://" . $_SERVER['SERVER_NAME'] . "/src/private/includes/pages/");
//Path to public directory
define('PUBLIC_PATH', SRC . DS . "public");
//public components path
define('COMPONENTS_PUBLIC_PATH', PUBLIC_PATH . DS . "components");
//public components URL
define("COMPONENTS_PUBLIC", 'https://' . $_SERVER['SERVER_NAME'] );
//define("COMPONENTS_PUBLIC", 'https://' . $_SERVER['SERVER_NAME'] . '/src/public/components');
//Path to integrations directory
define("INTEGRATIONS_PATH", $_SERVER['DOCUMENT_ROOT'] . '/src/private/integrations');
//URL for integrations directory.
define("INTEGRATIONS", 'https://' . $_SERVER['SERVER_NAME'] . '/src/private/integrations');
//URL for endpoints directory.
define("END_POINTS", 'https://' . $_SERVER['SERVER_NAME'] . '/src/public/endpoints');
//URL to SBPI2 directory
define("SBPI2", 'https://' . $_SERVER['SERVER_NAME'] . '/sbpi2/');
//URL to SBP directory
define("SBP", 'https://' . $_SERVER['SERVER_NAME'] . '/sbp/');
define("SECURITY", $_SERVER['DOCUMENT_ROOT'] . '/src/private/config/security.php');

//matco homepage for matcosms.com
define("MATCO_HOME", 'https://' . $_SERVER['SERVER_NAME'] . '/src/public/components/matco/home');



//CONFIGURATION
// The if defined is only used to make this work with our local systems using our own connection strings for local DB.
if (!defined('CONN')) {
    //Path to connection file. This file is called when use has logged in otherwise this will redirect users to a login page.
    define("CONN", $_SERVER['DOCUMENT_ROOT'] . '/src/private/config/sbconn.php');
}
if (!defined('CONNWOSHOPID')) {
    //CONNWOSHOPID ==> Connection With Out SHOPID. Use this constant for pages that do not require a shop to be logged in.
    define("CONNWOSHOPID", $_SERVER['DOCUMENT_ROOT'] . '/src/private/config/sbconnwoshopid.php');
}
if (!defined('CONNCONTROL')) {
    define("CONNCONTROL", $_SERVER['DOCUMENT_ROOT'] . '/src/private/config/sbconncontrol.php');
}

define("REDISCONN",$_SERVER['DOCUMENT_ROOT'] . '/src/private/config/redis.php');

//Encryption key
define("ENCRYPTIONKEY","8bb6e345df719f40c1aef9330d2eafac1c393f206f71a12614234c026d636937");


//Contains DB credentials. Need to be implmented further might be more useful for multi shop login.
define("DB", $_SERVER['DOCUMENT_ROOT'] . '/src/private/config/db.php');
// MYSQL Credentials
define("USER", 'superadmin');
define("PASS", 'sBp7e1cff-A563-4a6c%$3b3-e3a*0a#94a+6');

if(stripos($_SERVER['SERVER_NAME'], 'staging') !== false)
{
 define('HOST', 'db.dev.shopboss-aws.com');
 //Stax
 define("STAXHOST","https://services.dev.platform.vehlo.io");
 define("STAXBEARERTOKEN","shopboss_zekFC5NT9A9cjeJJeyk6fVQKsYaXxvxPFiNY6NurHpYkJRJyMMU5Sj6pFTXoqfTY");
 define("CRUSERNAME","BBE6C7AA00A348648D152F263A475FAF");
 define("CRPASSWORD","5B282DBF3FFC4734AD900846A1644082");
}
else
{
 define('HOST', 'db.prod.shopboss-aws.com');
 //Stax
 define("STAXHOST","https://services.platform.vehlo.io");
 define("STAXBEARERTOKEN","shopboss_xB99bKuF3uCWYTt6hukvdQZMYwkutRaWEBlgqcsu2d2zKYhWuag89xjl8PaF5Cw2");
 define("CRUSERNAME","88AC168269084FD98AF9F826F8A1CDB4");
 define("CRPASSWORD","69BCCA8A84684C3886E2DB06BAFD432D");
}

define('DBNAME', 'shopboss');

//Keys for Servicecloud
define('SC_CIPHER','AES-256-CBC');
define('SC_KEY','U75taC@I3yW2DO*ViIODRWSeuE5#58MHw3hMzT0U0aV$IQzLLomKGFkC1TDs!wrkcKf17TApxqMJQ56RHCUDmnzif7g5a!1yD9Din1!l@nrCrW^FhL!eZ7n@9j93Z@KK');
define('SC_URL','https://poc.vehlo.io/servicecloud/newcase');

//Path to base config file. At the time it is not being used nor needed be but at a later stage load all application configuration
// including and not limited to Paths, Cache info, app wide API keys, Loggers, Security salt, Cipher keys etc.
// define('BASE_CONFIG', $_SERVER['DOCUMENT_ROOT'] . '/src/private/includes/config.php');
//END CONFIGURATION

//ASSETS PATH
define("CSS_DIR", $_SERVER['DOCUMENT_ROOT'] . '/src/public/css/');
define("CSS", 'https://' . $_SERVER['SERVER_NAME'] . '/src/public/css');
define("JS_DIR", $_SERVER['DOCUMENT_ROOT'] . '/src/public/js/');
define("SCRIPT", 'https://' . $_SERVER['SERVER_NAME'] . '/src/public/js');
define("ASSETS", 'https://' . $_SERVER['SERVER_NAME'] . '/src/public/assets');
define("IMAGE", 'https://' . $_SERVER['SERVER_NAME'] . '/src/public/assets/img');
define("IMG_DIR", $_SERVER['DOCUMENT_ROOT'] . '/src/public/assets/img/');
define("MDB", 'https://' . $_SERVER['SERVER_NAME'] . '/src/public/MDB');
define("MDB5", 'https://' . $_SERVER['SERVER_NAME'] . '/src/public/MDB5');
if (!defined('SBPUI')) {
    define("SBPUI", 'https://' . $_SERVER['SERVER_NAME'] . '/src/public/js/core/sbpUI');
}

if (!defined('SBPUI_BOSS_BOARD')) {
    define("SBPUI_BOSS_BOARD", 'https://' . $_SERVER['SERVER_NAME'] . '/src/public/js/core/boss_board');
}

if (!defined('SBPUI_BOSS_INSPECT')) {
    define("SBPUI_BOSS_INSPECT", 'https://' . $_SERVER['SERVER_NAME'] . '/src/public/js/core/boss_inspect');
}

// FRAMEWORK
define("HEADER", $_SERVER['DOCUMENT_ROOT'].'/src/private/includes/headers');
define("HEAD", $_SERVER['DOCUMENT_ROOT'].'/src/private/includes/heads');
define("NAV", $_SERVER['DOCUMENT_ROOT'].'/src/private/includes/nav');
define("NAVBAR", $_SERVER['DOCUMENT_ROOT'].'/src/private/includes/navbar');
define("MENU", $_SERVER['DOCUMENT_ROOT'].'/src/private/includes/menus');
define("SCRIPTS", $_SERVER['DOCUMENT_ROOT'].'/src/private/includes/scripts');
define("FOOTER", $_SERVER['DOCUMENT_ROOT'].'/src/private/includes/footers');
define("VARS", $_SERVER['DOCUMENT_ROOT'].'/src/private/includes/variables');
define("RULES", $_SERVER['DOCUMENT_ROOT'].'/src/private/includes/rules');
define("FUNCTIONS", $_SERVER['DOCUMENT_ROOT'].'/src/private/includes/functions');
define('HEADS', $_SERVER['DOCUMENT_ROOT'].'/src/private/includes/heads');
define('DIALOGS', $_SERVER['DOCUMENT_ROOT'].'/src/private/includes/dialogs');


define('SBP_PAGES', $_SERVER['DOCUMENT_ROOT'].'/src/private/components');
define('GLOBAL_PAGES', $_SERVER['DOCUMENT_ROOT'].'/src/private/components/global');
define('GLOBAL_SB', $_SERVER['DOCUMENT_ROOT'].'/src/private/components/global');


if (!defined('SBPUI_MAIN')) {
    define('SBPUI_MAIN', 'main.min.js');
}

if (!defined('SBPUI_MAIN_2')) {
    define('SBPUI_MAIN_2', 'main2.min.js');
}
//END FRAMEWORK

//HELPER CONSTANTS
//These constants are not necessary, Paths can be constructed from base structure however these will serve as helper constants and more will be added.
define("STATUS", 'https://' . $_SERVER['SERVER_NAME'] . '/src/public/components/status/');
define("APPOINTMENTS", 'https://' . $_SERVER['SERVER_NAME'] . '/src/public/components/appointments/');
define("VIEW_INVOICE", 'https://' . $_SERVER['SERVER_NAME'] . '/src/public/components/viewinvoice/');
//INTEGRATIONS
define("PHPEXCEL", PRIVATE_PATH . '/integrations/php_excel/PHPExcel.php');

//END HELPER CONSTANTS

//PATHS for resources

//define("PDFINVOICES_PATH", PUBLIC_PATH.DS."pdfinvoices");
define("PDFINVOICES_PATH", COMPONENTS_PUBLIC_PATH.DS."invoices");
//define("PDFINVOICES_URL", "https://" . $_SERVER['SERVER_NAME'] ."/src/public/pdfinvoices");
define("PDFINVOICES_URL", "https://" . $_SERVER['SERVER_NAME'] . "/src/public/components/invoices");

//define("SAVEDINVOICES_PATH", $_SERVER["DOCUMENT_ROOT"].DS."sbp".DS."savedinvoices");
define("SAVEDINVOICES_PATH", "\\fs.shopboss.aws\share\savedinvoices");
define("SAVEDINVOICES_URL", SBP."savedinvoices");

define("TEMP_PATH", PUBLIC_PATH.DS."temp");
define("TEMP_URL", "https://" . $_SERVER['SERVER_NAME'] ."/src/public/temp");

define("UPLOAD_PATH", "\\\\fs.shopboss.aws\\share\\upload\\");
define("UPLOAD_URL", "https://" . $_SERVER['SERVER_NAME'] ."/sbp/upload");

define("KIOSK_PATH", PUBLIC_PATH.DS."assets".DS."_kiosk");
define("KIOSK_URL", "https://" . $_SERVER['SERVER_NAME'] ."/src/public/assets/_kiosk");

//New Custom reports
define("CR_CORE_PATH", COMPONENTS_PRIVATE_PATH. DS ."customreports". DS ."core");
define("CR_REPORTS_PATH", COMPONENTS_PRIVATE_PATH . DS . "customreports" . DS ."reports");
define("CR_TEMPLATES_PATH", COMPONENTS_PRIVATE_PATH. DS. "customreports". DS ."templates");
//end new custom reports

// AI-WRITING-TOOL
define("AI_WRITING_TOOL", $_SERVER['DOCUMENT_ROOT'] . '/src/public/js/plugins/ai-writing-tool/ai-writing-tool.php');
/*
 * putting this towards the end so the files below can make use of constants if code is run without a call.
 * I am not sure why this has been put here as this file was reserved for constants. It will work But I would advise to include ..
 * this in /src/index.php if and only if you are only using this for Migrated files and the URL is coming through the URL-Rewrite.
 * This will be loaded automatically through the rewrite rule.
 */
include($_SERVER['DOCUMENT_ROOT'] . '/src/private/config/functions.global.php');
sb_autoloader('constants');