<template>
  <div>
    <div class="border-gray border-bottom-0">
      <div class="bg-lightgray border-bottom-gray px-3 d-flex align-items-center" :id="`header${issue.id}`">
        <h6 class="font-weight-bold my-3 flex-grow-1">Customer Concern: {{ issue.complaint }}. </h6>
        <sbp-customer-vehicle-issue-status :status="issue.status" />
      </div>
      <div class="p-3">
        <p class="note note-success" v-if="printTechStory === 'true' && issue.techReport">
          Technician Notes: {{ issue.techReport }}
        </p>
        <p class="note note-warning" v-if="printScanResult === 'true' && issue.scanReport">
          <b>Scan Tool Results:</b> <span v-html="issue.scanReport"></span>
        </p>
        <div class="d-flex flex-wrap justify-content-between mb-3" v-for="(part, index) in issue.parts" :key="index" v-if="showparts=='yes'">
          <div class="d-flex flex-wrap">
            <div class="mr-3"><b>Part:</b> {{ part.description }}</div>
          </div>
          <div class="status-part-quantity">
            <div>Quantity: {{ part.quantity }}</div>
            <span v-if="showprice=='yes'">${{ part.ttlprice }}</span>
          </div>
        </div>
        <div class="d-flex flex-wrap justify-content-between mb-3" v-for="(labor, index) in issue.labors" :key="index">
          <div>
            <b>Labor:</b> {{ labor.description }}
          </div>
          <div class="text-right" v-if="showprice=='yes'">
            ${{ labor.ttlprice }}
          </div>
        </div>
        <div class="d-flex flex-wrap justify-content-between mb-3" v-for="(sublet, index) in issue.sublets" :key="index">
          <div>
            <b>Sublet: {{ sublet.description }}</b>
          </div>
          <div class="text-right" v-if="showprice=='yes'">
            ${{ sublet.ttlprice }}
          </div>
        </div>
        <sbp-button-container v-if="issue.status === 'pending'" halign="end">
          <sbp-button
            color="success"
            icon="thumbs-up"
            size="sm"
            @click="$emit('approve-decline-issue', issue.id, issue.isLocked)"
          >
            Approve {{ issue.isLocked === 'no' ? '/ Decline' : '' }} repair
          </sbp-button>
        </sbp-button-container>
      </div>
    </div>
    <div class="bg-gray px-3 py-2 border-gray d-flex justify-content-end mb-2">
      <div>Total for this concern: <b>{{ issue.ttlprice | moneyWithCents }}</b></div>
    </div>
  </div>
</template>

<script>
import mixin from '../../mixin';
import SBPButton from '../sbp-button';
import SBPButtonContainer from '../sbp-button-container';
import CustomerVehicleIssueStatus from './sbp-customer-vehicle-issue-status';

export default {
  name: 'sbp-customer-vehicle-issue',
  mixins: [mixin],
  components: {
    'sbp-button': SBPButton,
    'sbp-button-container': SBPButtonContainer,
    'sbp-customer-vehicle-issue-status': CustomerVehicleIssueStatus
  },
  props: {
    issue: Object,
    printTechStory: {
      type: String,
      default: 'true'
    },
    printScanResult: {
      type: String,
      default: 'false'
    },
    showprice: String,
    showparts: String
  }
}
</script>

<style>

</style>