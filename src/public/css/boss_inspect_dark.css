body, .navbar{
    background: var(--bodybackground) !important;
    color:var(--textColor) !important;
}
#RO a:hover, #fixed-ro-menu a:hover, #tech-activities-toast .toast-header strong, .btn-close:hover, .btn-secondary a, .btn-secondary a:hover, .form-control .select-arrow, .form-outline .form-control:focus~.form-label, .nav-item-border, .secondary-links a:hover, .select-arrow, .select-input.focused~.select-arrow, .text-primary, .text-secondary, .timeclock a:hover, a, h1, h2, h3, h4, h5, i:hover, td.cucell {
    color: var(--primary) !important;
}
.btn-primary i:hover, .btn-primary:hover{
    color: white !important;
}
.bg-lightgray{
    background-color: transparent !important;
    color: var(--primary);
}
.btn-secondary {
    background-color: #00000029 !important;
}
i.fa-times, i.handle, i.fa-grip-horizontal, i.fa-print{
    color: var(--primary) !important;
}
i.fa-arrow-up{
    color: var(--primary) !important;
}
.table-striped-columns>:not(caption)>tr>:nth-child(2n), .table-striped>tbody>tr:nth-of-type(odd)>* {
    color: var(--textColor) !important;
}
.text-success{
    color: var(--textColor) !important;
}
.bg-success{
    background-color: var(--primary) !important;
}
.card{
    box-shadow: none !important;
}
.card-header{
    background-color: transparent !important;
}
.text-muted{
    color:var(--textColor) !important;
}
.bg-danger{
    background-color: red !important;
}
.text-danger{
    color: red !important;
}
:root{
    --xaxis-color: #c8ccd3 !important;
    --yaxis-color: #C8CCD384 !important;
}
#dvi-dashboard .navbar{
    border-radius: 0px !important;
    border-bottom: solid var(--secondary) 1.5px;
}
.btm{
    box-shadow: none !important;
}
.btn-primary{
    background-color: var(--primary) !important;
    box-shadow: none !important;
    color: rgb(200, 204, 211) !important;
}
.btn-default{
    font-size: .9rem;
    border: 1px solid var(--primary) !important;
    color: var(--primary) !important;
    background-color: #00000029;
    box-shadow: none !important;
}
.btn-success{
    background-color: var(--primary) !important;
    color: var(--white) !important;
    box-shadow: none !important;
}
.btn-primary:hover,.btn-success:hover {
    box-shadow: none !important;
    color: white !important;
    background-color: var(--primary);
}
.btn:first-child:hover, .btn:focus-visible, .btn:hover, :not(.btn-check)+.btn:hover {
    box-shadow: none !important;
}
.btn:hover{
    background-size: 100% 100%, auto;
}
.dvi-body{
    padding-top: 10px
}
.form-outline {
    --mdb-form-outline-select-notch-border-color: var(--primary);
}
.form-label {
    font-size: .9rem !important;
    color: var(--textColor) !important;
}
.form-control-sm:focus, .form-control:focus, .form-select-sm:focus, .form-select:focus {
    border-color: var(--primary);
    box-shadow: inset 0 0 0 1px var(--primary);
}
.form-control:focus, .form-control, .form-select {
    background-color: var(--bodybackground);
    color: var(--textColor);
}
.modal-content{
    background-color: var(--bodybackground);
}
label{
    color: var(--primary) !important;
}
.form-control-sm:focus, .form-control:focus, .form-select-sm:focus, .form-select:focus {
    border-color: var(--primary);
    box-shadow: inset 0 0 0 1px var(--primary);
}
.form-outline {
    --mdb-form-outline-select-notch-border-color: var(--primary);
}
.form-outline .form-control:focus~.form-notch .form-notch-leading {
    border-color:  var(--primary) !important;
    border-top: .125rem solid var(--primary) !important;
    border-bottom: .125rem solid var(--primary) !important;
    border-left: .125rem solid var(--primary) !important;
}
.form-outline .form-control:focus~.form-notch .form-notch-middle {
    border-bottom: .125rem solid var(--primary) !important;
    border-top: 1px solid transparent;
    box-shadow: none !important;
}
.form-outline .form-control:focus~.form-notch .form-notch-trailing {
    border-top: .125rem solid var(--primary) !important;
    border-right: .125rem solid var(--primary) !important;
    border-bottom: .125rem solid var(--primary) !important;
}
.form-control:focus~.form-notch div:first-child, .form-control:focus~.form-notch div:last-child {
    box-shadow: none !important;
    clip-path: inset(0 0 0 0);
    border-color: var(--primary) !important;
}
.bg-light {
    background-color: var(--bodybackground) !important;
}
.ml-2{
    margin-left: .5rem !important;
}
#dvi-inspection-sidenav{
    scrollbar-width: none;
    -ms-overflow-style: none;
    background-color: var(--sidenav);
}
#top_menu a.btn span{
    color: var(--mdb-btn-color) !important;
}
.breadcrumb{
    flex-wrap: wrap;
    padding: .5rem 1rem;
    margin-bottom: 1rem;
    list-style: none;
    border-radius: .25rem
}
.border-primary{
    border-color: var(--primary) !important;
}
.bg-primary{
    background-color: var(--primary) !important;
}
#top_menu a.active{
    color: var(--textColor) !important;
    backdrop-filter: brightness(50%);
}
input[type="radio"], .form-check-input[type=radio] {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    width: 16px !important;
    height: 16px !important;
    border-radius: 50% !important;
    border: 1px solid var(--primary) !important;
    box-shadow: 0 0 0 2px white inset !important;
    background-color: white !important;
    cursor: pointer;
}

input[type="radio"]:checked, .form-check-input[type=radio]:checked{
    background-color: var(--primary) !important;
}
.form-check-input:checked:focus:before {
    box-shadow: 0 0 0 13px var(--primary) !important;
    transform: scale(1);
    transition: box-shadow .2s, transform .2s;
}
.form-check-input[type=radio]:checked:after {
    border-radius: 50%;
    width: .625rem;
    height: .625rem;
    border-color:  var(--primary) !important;
    background-color:  var(--primary) !important;
    transition: border-color;
    transform: translate(-50%, -50%);
    position: absolute;
    left: 50%;
    top: 50%;
}

.text-body{
    color: var(--textColor) !important;
}

.form-outline .form-control[disabled] {
    color: var(--textColor);
    background-color: #00000029;
}

.form-outline .form-control[disabled] {
    color: var(--textColor);
    cursor: not-allowed;
}
.form-outline .form-control.disabled, .form-outline .form-control:disabled, .form-outline .form-control[disabled] {
    background-color: #00000029;
}
.bg-primary, .form-check-input[type=checkbox]:checked, .status-approved {
    background-color: var(--primary) !important;
}
.form-check-input:checked, .form-check-input:focus {
    background-color: var(--primary) !important;
    border: 1px solid var(--primary) !important;
}
.btn-secondary {
    color: var(--primary) !important;
    background-color: #fff;
}
.btn-secondary:hover {
    box-shadow: none !important;
    background-color: transparent;
}
table tbody tr:nth-of-type(even), table tr:nth-of-type(even) {

}
.table-striped>tbody>tr:nth-of-type(even)>* {
    color: var(--textColor) !important;
    background-color: rgba(0, 0, 0, .05);
}
table tr:hover{
    background-color: var(--secondary) !important;
}
@media (min-width: 992px) {
    .d-lg-inline-block {
        display: inline-block !important;
    }
}