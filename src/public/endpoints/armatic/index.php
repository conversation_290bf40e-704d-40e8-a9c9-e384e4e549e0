<?php
require(CONNWOSHOPID);

$input = file_get_contents("php://input");
//file_put_contents(__DIR__."\\test.log",$input);

/*$input = "invoiceCountry=&invoiceZipCode=77066&invoiceFirstName=marcus&invoiceFaxNumber=&invoiceState=&invoiceAddress2=&invoiceEmail=bridgeharborshipyard%40gmail.com&invoiceLastName=griffin&invoiceAddress1=&invoiceCompany=&invoiceCity=&invoiceExtension=&invoiceMobilePhone=&invoiceWorkPhone=&invoiceTitle=&lastName=griffin&country=&zipCode=77066&extension=&personalId=&address2=&city=&address1=&homePhone=&brazilianId=&title=&accountId=*********&firstName=marcus&mobilePhone=&faxNumber=&company=&workPhone=&state=&email=bridgeharborshipyard%40gmail.com&username=16510085713368709204620453047&shippingCity=&shippingLastName=griffin&shippingCountry=&shippingZipCode=77066&shippingMethod=&shippingFirstName=marcus&shippingState=&shippingAddress2=&shippingAddress1=&contractChargePrice=350.00&quantity=1&productId=1111092&invoiceChargeCurrency=USD&overridePrice=350.00&invoiceAmount=350.00&invoiceChargeAmount=350.00&language=ENGLISH&invoiceAmountUSD=350.00&templateId=-99&productName=N%2FA&referrer=&contractOwner=1244652&referenceNumber=*********&contractPrice=350.00&testMode=N&contractId=3975466&contractName=N%2FA&currency=USD&invoiceInfoURL=https%3A%2F%2Fshoppers.bluesnap.com%2Fjsp%2Forder_locator_info.jsp%3FrefId%3D17B17286B090E063659E25D04C9DC1AF%26acd%3D847F85066ED3A28399339F83BB5A4524&remoteAddress=&creditCardExpDate=1%2F2026&dpanLastFourDigits=&binCategory=COMMERCIAL&dpanExpDate=&creditCardLastFourDigits=2582&cardSubType=DEBIT&cardCategory=BUSINESS&creditCardType=VISA&regulatedCard=N&addCD=N&untilDate=04%2F26%2F2022+02%3A29+PM&paymentMethod=CC&paymentType=CC&invoiceLocalAmount=350.00&invoiceURL=https%3A%2F%2Fcp.bluesnap.com%2Fjsp%2Fshow_invoice.jsp%3Fref%3D17B17286B090E063659E25D04C9DC1AF&shopperOrderUrl=https%3A%2F%2Fshoppers.bluesnap.com%2Fjsp%2Forder_locator_info.jsp%3FrefId%3D17B17286B090E063659E25D04C9DC1AF%26acd%3D847F85066ED3A28399339F83BB5A4524&originalRequestUrl=&contractLocalPrice=350.00&shopperAdminUrl=https%3A%2F%2Fshoppers.bluesnap.com%2Fjsp%2Forder_locator_info.jsp%3FrefId%3D17B17286B090E063659E25D04C9DC1AF%26acd%3D847F85066ED3A28399339F83BB5A4524&invoiceLocalCurrency=USD&recurringDisclaimer=N&targetBalance=BLUESNAP_ACCOUNT&promoteContractsNum=0&EDWPeriod=&EDWSurcharge=&EDWAmountUSD=&EDWContractId=&EDWSurchargeUSD=&EDWAmount=&vatId=&taxAmountUSD=0.00&taxRate=&taxChargeAmount=0.00&authKey=834e81397227b8d922b1a8aa955b3de5&transactionType=CHARGE&captureReferenceNumber=*********&issuerAuthCode=593765&bluesnapNode=2&plimusNode=2&couponCode=&licenseKey=&cvvSent=N&avsSent=Y&cvvResponse=Not+processed+&avsResponse=5-digit+ZIP+matches%2C+address+does+not+&transactionDate=04%2F26%2F2022+02%3A29+PM&merchantTransactionId=INV-047700%2CINV-047701&vendorId=&vendorName=&sepaMandateId=&sepaIban=&sepaMandateDate=&cardIssuingOrg=NAVY+F.C.U.&binNumber=400023&cardIssuingCountry=us&3DStatus=3DS+Not+Enabled&ipnId=619dfd3f-7839-4b40-a2da-3d103c556c0a";*/
parse_str($input, $data);

$stmt = "insert into armatic_log (input) values (?)";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$input);
    $query->execute();
    $conn->commit();
    $query->close();
}

$shopid = $dbshopid = "";
$bluesnapid = $data['accountId'];
if(empty($bluesnapid))die();
$txnid = $data['merchantTransactionId'];
$transtype = strtolower($data['transactionType']);
$amount = $data['invoiceChargeAmount'];

$url = "https://api.armatic.com/customers?query=".$bluesnapid;

$curl = curl_init($url);
curl_setopt($curl, CURLOPT_URL, $url);
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

$headers = array(
 "Accept: application/json",
 "Authorization: Bearer YcRuU2VlFBs25r99avyhJC1fsV25Uoia8cOJwWHshq9u_sJCCs9y0vJ1sGOjxaiJ8ljKfbgqCrqpW25Z6eCgfAPgfo8VeBE1WXg=",
);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($curl, CURLOPT_USERAGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36");

$res = curl_exec($curl);
curl_close($curl);

$json = json_decode($res,true);

if(isset($json['customers']))
{
  foreach($json['customers'] as $shop)
  {
      if($shop['vaulted_shopper_id']==$bluesnapid && !empty($shop['account_number']))
      {
          $shopid = $shop['account_number'];
          break;
      }
  }
}

if(empty($shopid))die();

$stmt = "select shopid,lower(status),companyname,upper(newpackagetype),matco from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($dbshopid,$status,$companyname,$package,$matco);
    $query->fetch();
    $query->close();
}

if($dbshopid==$shopid){

    if($transtype == 'decline' || stripos($transtype,'failed') !==false)
    {
        $stmt = "update settings set failedpayment='yes' where shopid = ?";
        if ($query = $conn->prepare($stmt)){
            $query->bind_param("s",$shopid);
            $query->execute();
            $conn->commit();
            $query->close();
        }
    }

    elseif(!empty($txnid))
    {
        $stmt = "select id from payments where shopid = ? and approvalcode = ? and amount = ?";
        if ($query = $conn->prepare($stmt)){
            $query->bind_param("sss",$shopid,$txnid,$amount);
            $query->execute();
            $query->store_result();
            $numrows = $query->num_rows();
         }
        if ($numrows == 0){
            $paymentdate = date('Y-m-d',strtotime($data['transactionDate']));
            if(stripos($transtype,'charge') !==false){
                $stmt = "insert into payments set shopid = ?,paymentdate=?,amount=?,approvalcode=?,approvaldescription='APPROVED',subscriptionmonth='WH'";
                if ($query = $conn->prepare($stmt)){
                    $query->bind_param("ssss",$shopid,$paymentdate,$amount,$txnid);
                    $query->execute();
                    $conn->commit();
                    $query->close();
                }
                $stmt = "update company set lastpaymentdate = ?,status = 'ACTIVE', datesuspended = '0000-00-00' where shopid = ?";
                if ($query = $conn->prepare($stmt)){
                    $query->bind_param("ss",$paymentdate,$shopid);
                    $query->execute();
                    $conn->commit();
                    $query->close();
                }

                $stmt = "update settings set failedpayment='no' where shopid = ?";
                if ($query = $conn->prepare($stmt)){
                    $query->bind_param("s",$shopid);
                    $query->execute();
                    $conn->commit();
                    $query->close();
                }

                if($status=='SUSPENDED')
                {
                    $slackdata = "Shop ID: $shopid\nShop Name: ".strtoupper($companyname)."\nPackage: $package\nDate Added: ".date('m/d/Y')."\nBluesnap ID: $bluesnapid\nMatco: ".ucfirst($matco);

                    $data = array('text'=>$slackdata);
                    $jsonEncodedData = json_encode($data);
                    $curl = curl_init();
                    $opts = array(
                        CURLOPT_URL             => '*******************************************************************************',
                        CURLOPT_RETURNTRANSFER  => true,
                        CURLOPT_CUSTOMREQUEST   => 'POST',
                        CURLOPT_POST            => 1,
                        CURLOPT_POSTFIELDS      => $jsonEncodedData,
                        CURLOPT_HTTPHEADER  => array('Content-Type: application/json','Content-Length: ' . strlen($jsonEncodedData))
                    );
                    curl_setopt_array($curl, $opts);
                    $result = curl_exec($curl);
                    curl_close($curl);

                    $subject = "Renewed Account";
                    $message = "The following Shop has RENEWED THEIR ACCOUNT. ******  DO NOT CHARGE THEIR CARD  ********** \n\n";
                    $message .= $companyname . " \n";
                    $message .= "SHOP ID: " . $shopid . " \n";

                    $res = sendEmailMandrill("<EMAIL>",$subject,$message,"Renewed Account","<EMAIL>");
                }
            }elseif($transtype == 'refund'){
                $stmt = "insert into payments set shopid = ?,paymentdate=?,amount=?,approvalcode=?,approvaldescription='APPROVED'";
                if ($query = $conn->prepare($stmt)){
                    $query->bind_param("ssss",$shopid,$paymentdate,$amount,$txnid);
                    $query->execute();
                    $conn->commit();
                    $query->close();
                }
            }
        } 
    }
}elseif(!empty($txnid)){
    $stmt = "select shopid from sbpent.company where shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->bind_result($dbshopid);
        $query->fetch();
        $query->close();
    }

    if($dbshopid==$shopid){
        $stmt = "select id from sbpent.payments where shopid = ? and approvalcode = ? and amount = ?";
        if ($query = $conn->prepare($stmt)){
            $query->bind_param("sss",$shopid,$txnid,$amount);
            $query->execute();
            $query->store_result();
            $numrows = $query->num_rows();
        }
        if ($numrows == 0){
            $paymentdate = date('Y-m-d',strtotime($data['transactionDate']));
            $amount = $data['invoiceChargeAmount'];
            if(stripos($transtype,'charge') !==false){
                $stmt = "insert into sbpent.payments set shopid = ?,paymentdate=?,amount=?,approvalcode=?,approvaldescription='APPROVED',subscriptionmonth='WH'";
                if ($query = $conn->prepare($stmt)){
                    $query->bind_param("ssss",$shopid,$paymentdate,$amount,$txnid);
                    $query->execute();
                    $conn->commit();
                    $query->close();
                }
                $stmt = "update sbpent.company set lastpaymentdate = ? where shopid = ?";
                if ($query = $conn->prepare($stmt)){
                    $query->bind_param("ss",$paymentdate,$shopid);
                    $query->execute();
                    $conn->commit();
                    $query->close();
                }
            }elseif($transtype =='refund'){
                $stmt = "insert into sbpent.payments set shopid = ?,paymentdate=?,amount=?,approvalcode=?,approvaldescription='APPROVED'";
                if ($query = $conn->prepare($stmt)){
                    $query->bind_param("ssss",$shopid,$paymentdate,$amount,$txnid);
                    $query->execute();
                    $conn->commit();
                    $query->close();
                }
            }
        }
    }
}