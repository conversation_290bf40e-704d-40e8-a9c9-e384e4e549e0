<?php
require_once CONNWOSHOPID;
require(PRIVATE_PATH."/integrations/mandrill/src/Mandrill.php");

$shopid = isset($_REQUEST['shopid']) ? filter_var($_REQUEST['shopid'], FILTER_SANITIZE_STRING) : "";
$radio1 = isset($_POST['radio1']) ? filter_var($_POST['radio1'], FILTER_SANITIZE_STRING) : "";
$blastname = isset($_POST['blastname']) ? filter_var($_POST['blastname'], FILTER_SANITIZE_STRING) : "";
$bfirstname = isset($_POST['bfirstname']) ? filter_var($_POST['bfirstname'], FILTER_SANITIZE_STRING) : "";
$token = isset($_POST['token']) ? filter_var($_POST['token'], FILTER_SANITIZE_STRING) : "";
$expdate = isset($_POST['exp']) ? filter_var($_POST['exp'], FILTER_SANITIZE_STRING) : "";
$billingaddress = isset($_POST['billingaddress']) ? filter_var($_POST['billingaddress'], FILTER_SANITIZE_STRING) : "";
$billingstate = isset($_POST['billingstate']) ? filter_var($_POST['billingstate'], FILTER_SANITIZE_STRING) : "";
$billingcity = isset($_POST['billingcity']) ? filter_var($_POST['billingcity'], FILTER_SANITIZE_STRING) : "";
$billingzip = isset($_POST['billingzip']) ? filter_var($_POST['billingzip'], FILTER_SANITIZE_STRING) : "";
$roid = isset($_POST['roid']) ? filter_var($_POST['roid'], FILTER_SANITIZE_STRING) : "";
$datestarted = isset($_POST['datestarted']) ? filter_var($_POST['datestarted'], FILTER_SANITIZE_STRING) : "";
$bluesnapusername="API_16299867132592130162459";
$bluesnappassword="Toby1dog";
$errmsg = "";

$stmt = "select * from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $results = $query->get_result();
    $rs = $results->fetch_assoc();
    $query->close();
} else {
    echo "Company prepare failed l34" . $conn->error;
}
$sn = $rs['CompanyName'];
$companyemail = $rs['companyemail'];
$bluesnapid = $rs['bluesnapid'];
$shopname = $rs["CompanyName"];
$matco = $rs['matco'];

if (empty($shopid) || empty($sn)) {
    // echo "Invalid shop id provided or the shop doesn't exists";
    header("Location:https://" . $_SERVER['SERVER_NAME'] . "/login.php");
    exit();
}

$stmt = "select shopid from deniedlist where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->store_result();
    if ($query->num_rows > 0) {
        header("Location:renewbyphone.php");
        exit();
    }
    $query->close();
}
if (!empty($token)) {
    //var_dump($_REQUEST);
    //   exit();
    if (strtolower($radio1) == "silver") {
        $flatprice = 109.00;
    } else {
        if (strtolower($radio1) == "gold") {
            $flatprice = 175.00;
        } else {
            if (strtolower($radio1) == "platinum") {
                $flatprice = 285.00;
            }
        }
    }

    if(!empty($bluesnapid))
    {
        $url = "https://ws.bluesnap.com/services/2/vaulted-shoppers/".$bluesnapid;
        $stmt = 'select cc from company where shopid = ?';
      if ($query = $conn->prepare($stmt))
      {
        $query->bind_param("s",$shopid);
        $query->execute();
        $query->bind_result($cc);
        $query->fetch();
        $query->close();
      }
      $ccarr = explode(';',$cc);
      $last4old = $ccarr[0];
      $cardtypeold = $ccarr[1]??'';
        $data = array
       (
       "firstName" => $bfirstname,
       "lastName" => $blastname,
       "paymentSources" => array("creditCardInfo"=>array(0 => array("creditCard" => array("cardType" => $cardtypeold,"cardLastFourDigits" => $last4old),"status"=>"D")))
       );

        $jsonEncodedData = json_encode($data);
        $curl = curl_init();
        $opts = array(
            CURLOPT_URL             => $url,
            CURLOPT_RETURNTRANSFER  => true,
            CURLOPT_CUSTOMREQUEST   => 'PUT',
            CURLOPT_POST            => 1,
            CURLOPT_POSTFIELDS      => $jsonEncodedData,
            CURLOPT_SSL_VERIFYHOST  => 0,
            CURLOPT_SSL_VERIFYPEER  => 0,
            CURLOPT_HTTPHEADER  => array('Accept: application/json','Content-Type: application/json','Authorization: Basic '. base64_encode("$bluesnapusername:$bluesnappassword"),'Content-Length: ' . strlen($jsonEncodedData))
        );
        curl_setopt_array($curl, $opts);
        $res=curl_exec($curl);
    }
    else
    $url = "https://ws.bluesnap.com/services/2/vaulted-shoppers";

     $data = array
     (
       "firstName" => $bfirstname,
       "lastName" => $blastname,
       "zip" => $billingzip,
       "merchantShopperId" => $shopid,
       "companyName" => strtoupper($shopname),
       "paymentSources" => array("creditCardInfo"=>[array("pfToken" => $token)])
     );

    $jsonEncodedData = json_encode($data);
    $curl = curl_init();
    $opts = array(
        CURLOPT_URL             => $url,
        CURLOPT_RETURNTRANSFER  => true,
        CURLOPT_CUSTOMREQUEST   => (!empty($bluesnapid)?'PUT':'POST'),
        CURLOPT_POST            => 1,
        CURLOPT_POSTFIELDS      => $jsonEncodedData,
        CURLOPT_SSL_VERIFYHOST  => 0,
        CURLOPT_SSL_VERIFYPEER  => 0,
        CURLOPT_HTTPHEADER  => array('Accept: application/json','Content-Type: application/json','Authorization: Basic '. base64_encode("$bluesnapusername:$bluesnappassword"),'Content-Length: ' . strlen($jsonEncodedData))
    );
    curl_setopt_array($curl, $opts);
    $res = curl_exec($curl);
    $result = json_decode($res);

    if(isset($result->message[0]))
    {
    if($result->message[0]->code=='14040') $errmsg = "Token expired. Please try again.";
    else $errmsg = $result->message[0]->description;
    }
    elseif(isset($result->vaultedShopperId))
   {

    $cc = $_POST['last4'].';'.$_POST['cardtype'];
    $bluesnapid = $result->vaultedShopperId;
    $exparr = explode('/', $expdate);
    $expdate = trim($exparr[0]).'/'.substr($exparr[1],-2);
    
    $date = date('Y-m-d');

    $stmt = "update company set newpackagetype = ?, flatprice = ?, blastname = ?, bfirstname = ?, howpaying = 'cc', cc = ?, expdate = ?, billingaddress = ?, billingstate = ?, billingcity = ?, billingzip = ?, `status` = 'ACTIVE', package = 'Paid', dateofacceptance = ?, initialdoa = ?, bluesnapid = ? where shopid = ?";

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("sdssssssssssss", $radio1, $flatprice, $blastname, $bfirstname, $cc, $expdate, $billingaddress, $billingstate, $billingcity, $billingzip, $date, $date, $bluesnapid, $shopid);
        if ($query->execute()) {
            $conn->commit();
        } else {
            echo "Error";
            die($conn->error . " # " . $conn->errno);
        }
        $query->close();
    } else {
        echo "Company prepare failed " . $conn->error;
    }

    $stmt = "insert into package_change_log (sbemp,currentpackage,shopid,oldpackage) values (?,?,?,'trial')";
    if ($query = $conn->prepare($stmt))
    {
        $query->bind_param('sss',$shopid,$radio1,$shopid);
        $query->execute();
        $conn->commit();
        $query->close();
    }

    $dts = localTimeStamp($shopid);
    $temppackage = strtoupper($radio1);
    $usr = $shopid;
    $cat = "Account Update";
    $ev = "Your Shop Boss Account was update to $temppackage";

    $stmt = "insert into `audit` (shopid,`category`,`event`,`useraccount`,`eventdatetime`) values ('$shopid','$cat','$ev','$usr','$dts')";
    if ($query = $conn->prepare($stmt)){
        if ($query->execute()){
            $conn->commit();
        }else{
            echo $conn->errno;
        }
    }

    $slackdata = "Shop ID: $shopid\nShop Name: ".strtoupper($sn)."\nPackage: ".strtoupper($radio1)."\nDate Added: ".date('m/d/Y')."\nBluesnap ID: $bluesnapid\nMatco: ".ucfirst($matco);

    $data = array('text'=>$slackdata);
    $jsonEncodedData = json_encode($data);
    $curl = curl_init();
    $opts = array(
        CURLOPT_URL             => '*******************************************************************************',
        CURLOPT_RETURNTRANSFER  => true,
        CURLOPT_CUSTOMREQUEST   => 'POST',
        CURLOPT_POST            => 1,
        CURLOPT_POSTFIELDS      => $jsonEncodedData,
        CURLOPT_HTTPHEADER  => array('Content-Type: application/json','Content-Length: ' . strlen($jsonEncodedData))
    );
    curl_setopt_array($curl, $opts);
    $result = curl_exec($curl);
    curl_close($curl);

    $url = "https://hooks.zapier.com/hooks/catch/2237757/okb08kt/?shopid=" . $shopid;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    $output = curl_exec($ch);
    curl_close($ch);

    $zapier_output = json_decode($output, true);

    //SF Update
        $data = array('ShopId'=>$shopid,'DateOfAcceptance'=>date('m/d/Y'),'email'=>$companyemail);
        $jsonEncodedData = json_encode($data);
        $curl = curl_init();
        $opts = array(
    CURLOPT_URL             => 'https://endpoint.scribesoft.com/v1/orgs/47078/requests/20228?accesstoken=03fdbdf1-0c25-4fc8-84fb-472f7a90e4d6',
    CURLOPT_RETURNTRANSFER  => true,
    CURLOPT_CUSTOMREQUEST   => 'POST',
    CURLOPT_POST            => 1,
    CURLOPT_POSTFIELDS      => $jsonEncodedData,
    CURLOPT_HTTPHEADER  => array('Content-Type: application/json','Content-Length: ' . strlen($jsonEncodedData))
        );
       curl_setopt_array($curl, $opts);
       $result = curl_exec($curl);
       curl_close($curl);

    //  print_r($zapier_output);

    $cmess = "<span style='color:green;font-weight:bold;font-size:16px;'>Your account information has been updated.  Thank you</span>";

    $subject = "Activated Account";

    $message = "The following Shop has activated their account.  You do not need to charge their account.\n\n" . $sn . "\n" . $shopid . "\n";

    $res = sendEmailMandrill("<EMAIL>",$subject,$message,"Activated Account","<EMAIL>");

    header("Location:https://" . $_SERVER['SERVER_NAME'] . "/login.php?activated");
    exit();
   }
   else
   $errmsg = "Failed";
}

$susdate = $rs["datesuspended"];

if (strtolower($rs["estguide"]) == "yes") {
    if ($rs["alldatarepair"] == "yes") {
        $alldatachg = 99.95;
    } else {
        $alldatachg = 29.95;
    }
} else {
    $alldatachg = 0;
}

$datestartedTS = strtotime($rs['datestarted']);
$origin = date_create('6/03/2013');
$target = date_create($rs['datestarted']);
$interval = date_diff($origin, $target);
$ds = $interval->format('%a');
// echo "Date diff " . $ds;
//  echo "<br />";
if (!empty($susdate) && $susdate != "0000-00-00") {
    //   echo "here";
    $susdateobj = date_create($susdate);
    $dateowed = date_add($susdateobj, date_interval_create_from_date_string("-30 days"));
    $monthdue = $dateowed->format("m");
    if ($monthdue = "12") {
        $yeardue = intval(date("yyyy", strtotime($susdate))) - 1;
    } else {
        $yeardue = date("yyyy", strtotime($susdate));
    }
} else {
    //    echo "else here";
    $stmt = "select roid,datein from repairorders where shopid = ? order by datein desc limit 1";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->bind_result($roid, $datein);
        $query->fetch();
        $query->close();
    } else {
        //     echo "RO prepare failed " . $conn->error;
    }
    if (!empty($datein)) {
        //   echo "din : " . $datein;
        //   echo "<br />";
        $lro = date_create($datein);
        $dateowed = date_add($lro, date_interval_create_from_date_string("-0 days"));
        $monthdue = $dateowed->format("m");
        $yeardue = $dateowed->format("Y");
        //   echo "lro : " . $lro->format('M-d-Y');
    } else {
        header("Location:renewbyphone.php");
        exit();
    }
}

// echo "Month Due : " . $monthdue;

$eom = cal_days_in_month(CAL_GREGORIAN, $monthdue, $yeardue);


$sd = $yeardue . "-" . $monthdue . "-01";
$ed = $yeardue . "-" . $monthdue . "-" . $eom;

$stmt = "select count(*) as c from repairorders where shopid = ? and datein >= ? and datein <= ?";

$rocount = 0;

if ($query = $conn->prepare($stmt)) {
    $query->bind_param("sss", $shopid, $sd, $ed);
    $query->execute();
    $query->bind_result($rocount);
    $query->fetch();
    $query->close();
} else {
    //   echo "RO prepare failed";
}

$chg = 0;

if ($ds <= 0) {
    if ($rocount > 0 && $rocount <= $rs["freelimit"]) {
        $chg = 0;
    } else {
        if ($rocount > $rs["freelimit"] && $rocount <= 10) {
            $chg = 14.95;
        } else {
            if ($rocount > $rs["freelimit"] && $rocount <= 30) {
                $chg = 29.95;
            } else {
                if ($rocount >= 31 && $rocount <= 55) {
                    $chg = 69.95;
                } else {
                    if ($rocount > 55) {
                        $chg = 99.95;
                    }
                }
            }
        }
    }
}

if ($ds > 0) {
    if ($rocount > 0 && $rocount <= $rs["freelimit"]) {
        $chg = 0;
    } else {
        if ($rocount > $rs["freelimit"] && $rocount <= 30) {
            $chg = 29.95;
        } else {
            if ($rocount >= 31 and $rocount <= 55) {
                $chg = 69.95;
            } else {
                if ($rocount > 55) {
                    $chg = 99.95;
                }
            }
        }
    }
}

$totalchg = $chg + $alldatachg;

$cchide = "";

$url = "https://ws.bluesnap.com/services/2/payment-fields-tokens";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_HEADER, 1);
curl_setopt($ch, CURLOPT_NOBODY, 0);
curl_setopt($ch, CURLOPT_HEADERFUNCTION,
    function ($curl, $header) use (&$headers) {
        $len = strlen($header);
        $header = explode(':', $header, 2);
        if (count($header) < 2) // ignore invalid headers
            return $len;

        $headers[strtolower(trim($header[0]))][] = trim($header[1]);

        return $len;
    }
);
curl_setopt($ch, CURLOPT_HTTPHEADER, array('Accept: application/json','Content-Type: application/json','Authorization: Basic '. base64_encode("$bluesnapusername:$bluesnappassword")));
$response = curl_exec($ch);
$tokenlocation = $headers['location'][0];
$tokenarr = explode("payment-fields-tokens/",$tokenlocation);
$token = $tokenarr[1];
?>
<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">

    <title>Activate Account</title>
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.11.2/css/all.css"/>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap"/>

    <link rel="stylesheet" href="<?= MDB ?>/css/mdb.min.css" type="text/css">
    <link rel="stylesheet" href="<?= SBPUI ?>/main.css"></link>
    <link rel="stylesheet" href="<?= CSS ?>/sbp-common.css" type="text/css">

    <!-- <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/card/card.css"> -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/select2/select2.css" type="text/css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/select2/select2-bootstrap4.css" type="text/css">
    <?php
    ?>
</head>
<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
<div id="app" class="pricing-plan">
    <sbp-page-container>
        <template #banner>
            <div class="alert alert-danger text-center font-weight-bolder">You have reached the end of your trial
                account.&nbsp; To activate your Shop Boss account, please update your credit card information below
            </div>
        </template>
        <template #header>
            <?= strtoupper($sn) ?>
            <?= !empty($errmsg)?"<div class='alert alert-danger'>$errmsg</div>":''?>
        </template>
        <template #content>
            <!-- <div class="text-center">
                <small class="text-muted">
                    Be sure that information shown below is accurate. The information entered will be retained for
                    future
                    billing.
                </small>
            </div> -->
            <!-- <sbp-pricing-card></sbp-pricing-card> -->
            <form action="activatetrialaccount.php" name="bankform" method="post">
                <input type="hidden" name="totalchg" value="<?= $totalchg ?>">
                <input name="dateofacceptance" value="<?= date('Y-m-d') ?>" type="hidden"/>
                <input name="howpaying" value="mobank" type="hidden"/>
                <input name="shopid" value="<?= $shopid; ?>" type="hidden"/>
                <input name="token" id="token" value="<?php echo $token; ?>" type="hidden" />
                <input name="last4" id="last4" type="hidden" />
                <input name="exp" id="exp" type="hidden" />
                <input name="cardtype" id="cardtype" type="hidden" />
               
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-10 offset-md-1">
                        <div class="d-flex flex-column-reverse flex-md-row">
                            <div class="col-12 col-md-6">
                                <div class="row">
                                    <div class="col-12 col-md-12">
                                        <div class="input-group">
                                            <div class="form-control" id="cc" data-bluesnap="ccn"></div>
                                            <div class="input-group-append">
                                                    <span class="input-group-addon">
                                                    <img class="d-inline" src="<?= IMAGE ?>/card_strip.png"
                                                         alt="Card Stripe">
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12 col-md-6 mt-2">
                                        <input name="bfirstname" id="bfirstname" type="text"
                                               placeholder="First Name On Card" class="form-control"/>
                                    </div>
                                    <div class="col-12 col-md-6 mt-2">
                                        <input name="blastname" id="blastname" placeholder="Last Name On Card"
                                               type="text" class="form-control"/>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-6 col-md-6">
                                        <div class="input-group">
                                        <div class="form-control" id="expdate" data-bluesnap="exp"></div>  
                                        </div>
                                    </div>
                                    <div class="col-6 col-md-6">
                                        <div class="input-group">
                                        <div class="form-control" id="cvv" data-bluesnap="cvv"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12 col-md-12">
                                        <input name="billingaddress" id="billingaddress" value='' placeholder="Address"
                                               type="text" class="form-control"/>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12 col-md-4">
                                        <input name="billingcity" id="billingcity" value='' placeholder="City"
                                               type="text"
                                               class="form-control mb-2"/>
                                    </div>
                                    <div class="col-12 col-md-4">
                                        <input name="billingstate" id="billingstate" value='' placeholder="State"
                                               type="text"
                                               class="form-control mb-2"/>
                                    </div>
                                    <div class="col-12 col-md-4 mt-2 mt-md-0">
                                        <input name="billingzip" id="billingzip" value='' type="text" placeholder="Zip"
                                               class="form-control"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 pl-md-2">
                                <div class="d-flex justify-content-center align-items-center p-2">
                                    <div id="card-wrapper"></div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="mt-4">
                            <h5 class="text-center">Please select your desired package.</h5>
                            <div class="row text-center mt-4">
                                <div class="col-12 col-sm-12 col-md-4">
                                    <div class="plan plan-silver">
                                        <sbp-pricing-plan-card plan-id="silver" :selected-plan-id="selectedPlanId"
                                                               @choose="handleChoosePlan"></sbp-pricing-plan-card>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-12 col-md-4">
                                    <div class="plan plan-gold">
                                        <sbp-pricing-plan-card plan-id="gold" :selected-plan-id="selectedPlanId"
                                                               @choose="handleChoosePlan"></sbp-pricing-plan-card>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-12 col-md-4">
                                    <div class="plan plan-platinum">
                                        <sbp-pricing-plan-card plan-id="platinum" :selected-plan-id="selectedPlanId"
                                                               @choose="handleChoosePlan"></sbp-pricing-plan-card>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12 text-center">
                                <div class="alert alert-danger" style="display: none;" id="message">Please choose a
                                    plan
                                </div>
                            </div>
                        </div>
                        <div class="row mt-4">
                            <div class="col-12 text-center">
                                <input name="radio1" type="hidden" value="" id="Radio1">
                                <sbp-button-container>
                                    <sbp-button color="success" icon="credit-card" @click="checkForm()">Activate My
                                        Account
                                    </sbp-button>
                                    <sbp-button color="light" icon="times"
                                                @click="location.href='https://<?php echo $_SERVER['SERVER_NAME']; ?>/login.php'">
                                        Cancel
                                    </sbp-button>
                                </sbp-button-container>
                            </div>
                        </div> -->
                    </div>
                </div>
            </form>
        </template>
    </sbp-page-container>
</div>
</body>

<script src="<?= SBPUI ?>/vue.min.js"></script>
<script src="<?= SBPUI ?>/<?= SBPUI_MAIN ?>"></script>
<script src="<?= SCRIPT ?>/jquery.js"></script>
<!-- <script src="<?= SCRIPT; ?>/plugins/card/card.js"></script>-->
<script src="<?= SCRIPT; ?>/plugins/select2/select2.js"></script>
<script type="text/javascript" src="https://ws.bluesnap.com/web-sdk/4/bluesnap.js"></script>

<script>
          var bsObj = {
            token: "<?= $token ?>",
            onFieldEventHandler: {
              onFocus: function (tagId) {}, // Handle focus
              onBlur: function (tagId) {}, // Handle blur
              onError: function (tagId, errorCode , errorDescription) {
                alert(errorDescription.toUpperCase())
              },
              onType: function (tagId, cardType, cardData) {
              },
              onEnter: function(tagId) {}, // Will trigger when shopper presses enter while inside one of the inputs
              onValid: function (tagId) {} // Handle a change in validation
            },
            style: {
            "input": {
              "font-size": "14px",
              "font-family":
                "RobotoDraft,Roboto,Helvetica Neue,Helvetica,Arial,sans-serif",
              "line-height": "1.6",
              "color": "#616161"
            },
            ":focus": {
              "color": "#616161"
            }
              },
            ccnPlaceHolder: "(XXXX-XXXX-XXXX-XXXX)",
            cvvPlaceHolder: "CVV", 
            expPlaceHolder: "MM/YY", 
          };
             
</script>
<script>
    new Vue({
        el: '#app',
        components: sbpUI.take([
            'sbp-page-container',
            'sbp-pricing-plan-card',
            'sbp-button-container',
            'sbp-button'
        ]),
        data() {
            return {
                selectedPlanId: null
            };
        },
        methods: {
            handleChoosePlan(planId) {
                $("#Radio1").val(planId);
                this.selectedPlanId = planId;
            }
        }
    });
    $(document).ready(function () {
        /*var card = new Card({
            // a selector or DOM element for the form where users will
            // be entering their information
            form: 'form', // *required*
            // a selector or DOM element for the container
            // where you want the card to appear
            container: '#card-wrapper', // *required*

            formSelectors: {
                numberInput: 'div#cc', // optional — default input[name="number"]
                expiryInput: 'div#expdate', // optional — default input[name="expiry"]
                cvcInput: 'div#cvv', // optional — default input[name="cvc"]
                nameInput: 'input#bfirstname, input#blastname' // optional - defaults input[name="name"]
            },

            //   width: '100rem', // optional — default 350px
            formatting: true, // optional - default true

            // Strings for translation - optional
            messages: {
                validDate: 'valid\ndate', // optional - default 'valid\nthru'
                monthYear: 'mm/yyyy', // optional - default 'month/year'
            },

            // Default placeholders for rendered fields - optional
            placeholders: {
                number: '•••• •••• •••• ••••',
                name: 'Full Name',
                expiry: '••/••',
                cvc: '•••'
            },

            masks: {
                //  cardNumber: '•' // optional - mask card number
            },

            // if true, will log helpful messages for setting up Card
            debug: true // optional - default false
        });*/
        bluesnap.hostedPaymentFieldsCreate(bsObj);
    });
</script>
<script language="javascript">
    function changeCharge(v) {
        d0 = document.getElementById("cc0")
        d1 = document.getElementById("cc1")
        d2 = document.getElementById("cc2")
        d3 = document.getElementById("cc3")
        d4 = document.getElementById("cc4")
        d5 = document.getElementById("cc5")
        d6 = document.getElementById("cc6")
        b1 = document.getElementById("ba1")
        b2 = document.getElementById("ba2")
        b3 = document.getElementById("ba3")
        b4 = document.getElementById("ba4")
        if (v == "cc") {
            b1.style.visibility = "hidden"
            b2.style.visibility = "hidden"
            b3.style.visibility = "hidden"
            b4.style.visibility = "hidden"
            d0.style.visibility = "visible"
            d1.style.visibility = "visible"
            d2.style.visibility = "visible"
            d3.style.visibility = "visible"
            d4.style.visibility = "visible"
            d5.style.visibility = "visible"
            d6.style.visibility = "visible"
        }
        if (v == "mobank") {
            b1.style.visibility = "visible"
            b2.style.visibility = "visible"
            b3.style.visibility = "visible"
            b4.style.visibility = "visible"
            d0.style.visibility = "hidden"
            d1.style.visibility = "hidden"
            d2.style.visibility = "hidden"
            d3.style.visibility = "hidden"
            d4.style.visibility = "hidden"
            d5.style.visibility = "hidden"
            d6.style.visibility = "hidden"
        }

    }

    function checkForm() {
        $("#message").fadeOut();
        if ($("#Radio1").val() == "") {
            $("#message").text("Please select your desired package above (Gold, Silver or Platinum)").fadeIn();
            return
        }
        if (document.bankform.billingaddress.value == "") {
            $("#message").text("Billing Address is required").fadeIn();
            return
        }
        if (document.bankform.blastname.value == "") {
            $("#message").text("Billing Last Name is required").fadeIn();
            return
        }
        if (document.bankform.bfirstname.value == "") {
            $("#message").text("Billing First Name is required").fadeIn();
            return
        }
        if (document.bankform.billingcity.value == "") {
            $("#message").text("Billing City is required").fadeIn();
            return
        }
        if (document.bankform.billingstate.value == "") {
            $("#message").text("Billing State is required").fadeIn();
            return
        }
        if (document.bankform.billingzip.value == "") {
            $("#message").text("Billing Zip Code is required").fadeIn();
            return
        }
        
        bluesnap.hostedPaymentFieldsSubmitData( function(callback)
        {
            if (null != callback.cardData) {
                var fraudSessionId = callback.transactionFraudInfo.fraudSessionId;

                $('#last4').val(callback.cardData.last4Digits);
                $('#exp').val(callback.cardData.exp);
                $('#cardtype').val(callback.cardData.ccType);
                document.bankform.submit();
            } else {
                var errorArray = callback.error;
                for (i in errorArray) {
                    console.log("Received error: tagId= " +
                        errorArray[i].tagId + ", errorCode= " +
                        errorArray[i].errorCode + ", errorDescription= " +
                        errorArray[i].errorDescription);
                }
            }
        });
        
    }

</script>
</html>