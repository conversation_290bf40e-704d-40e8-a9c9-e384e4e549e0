<?php
session_start();
require CONNWOSHOPID;

$expiration = time() + (24 * 3600 * 7);

if ((strpos($_SERVER['SERVER_NAME'], 'localhost') !== false)) { //for local environments
    $servername = $_SERVER['SERVER_NAME'];
}elseif ((strpos($_SERVER['SERVER_NAME'], 'matcosms.com') !== false)) { //for Matco
    $servername = '.matcosms.com';
}elseif ((strpos($_SERVER['SERVER_NAME'], 'protractorgo.com') !== false)) { //for Protractor
    $servername = '.protractorgo.com';
}else {
    $servername = '.shopbosspro.com'; //servername could be used here...
}
$shopid = trim(filter_var($_POST['shopid'], FILTER_SANITIZE_STRING));
$email = filter_var($_POST['email'], FILTER_SANITIZE_STRING);
$password = filter_var($_POST['password'], FILTER_SANITIZE_STRING);
$ui = $_POST['ui'];

if(empty($shopid) || empty($email) || empty($password))
{
	echo(json_encode(array('status'=>'failed','msg'=>"Missing login parameters")));
	exit;
}

if (!filter_var($email, FILTER_VALIDATE_EMAIL))
{
	echo(json_encode(array('status'=>'failed','msg'=>"Invalid Email")));
	exit;
}

$stmt = "select id,first_name,last_name,email,theme from adminlogin where email = ? and password = ?";
if ($query = $conn->prepare($stmt)) 
{
    $query->bind_param("ss", $email, $password);
    $query->execute();
    $result = $query->get_result();
    $numrows = $result->num_rows;
    $query->close();

    if($numrows>0)
    {
        $adrs = $result->fetch_assoc();
        $tmrs = '';
        $stmt = "select * from company where shopid = ?";
        if ($query = $conn->prepare($stmt)) 
        {
           $query->bind_param("s", $shopid);
           $query->execute();
           $result = $query->get_result();
           $numrows = $result->num_rows;
           if($numrows<1)
           {
             echo(json_encode(array('status'=>'failed','msg'=>"Invalid Shop ID")));
             exit;
           }
           $tmrs = $result->fetch_assoc();
           $query->close();
        }

        $status = $tmrs['status'];
        $package = $tmrs['package']; 
        $readonly = $tmrs['readonly'];

        if (strtolower($status) == "suspended" && $readonly == 'no')
        {
         if(strtolower($package) == "trial")
         {
            echo(json_encode(array('status'=>'trialsus','msg'=>"Trial Suspended")));
            exit;
         }
         elseif(strtolower($package) == "paid")
         {
          echo(json_encode(array('status'=>'paidsus','msg'=>"Paid Suspended")));
          exit;
         }
        }

        if(strtolower($tmrs['newpackagetype']) == 'platinum')
        {
            $stmt = "select id from roinspection where shopid = ? limit 1";
            if ($query = $conn->prepare($stmt))
            {
                $query->bind_param("s",$shopid);
                $query->execute();
                $query->store_result();
                $numrows = $query->num_rows();
                if ($numrows > 0)
                setcookie("dvilite", "yes", $expiration, "/", $servername);
                else
                setcookie("dvilite", "no", $expiration, "/", $servername);
            } 
        }
        else
        setcookie("dvilite", "yes", $expiration, "/", $servername);

        if($ui=='new')setcookie("newui", "yes", $expiration, "/", $servername);
        $e_empid = encryptData('Admin');
        $e_shopid = encryptData($shopid);
    	setcookie("username", 'Admin', $expiration, "/", $servername);
    	setcookie("empid", 'Admin', $expiration, "/", $servername);
        $_SESSION['empid'] = 'Admin';
        setcookie("e_empid", $e_empid, $expiration, "/", $servername,true, true);
        setcookie("shopid", $shopid, $expiration, "/", $servername);
        $_SESSION['shopid'] = $shopid;
        setcookie("e_shopid", $e_shopid, $expiration, "/", $servername, true, true);
        setcookie("admindata", implode(',', $adrs) , $expiration, "/", $servername);
        setcookie("adminid", $adrs['id'], $expiration, "/", $servername);
        setcookie("theme", $adrs['theme'], $expiration, "/", $servername);
            setcookie("usr", 'Admin', $expiration, "/", $servername);
            $_SESSION['usr'] = 'Admin';
            setcookie("cleared", 'yes', $expiration, "/", $servername);
            setcookie("createro", 'yes', $expiration, "/", $servername);
            setcookie("companyaccess", 'yes', $expiration, "/", $servername);
            setcookie("employeeaccess", 'yes', $expiration, "/", $servername);
            setcookie("reportaccess", 'yes', $expiration, "/", $servername);
            setcookie("createps", 'yes', $expiration, "/", $servername);
            setcookie("editsupplier", 'yes', $expiration, "/", $servername);
            setcookie("inventorylookup", 'yes', $expiration, "/", $servername);
            setcookie("viewsecuritylog", 'yes', $expiration, "/", $servername);
            setcookie("editinventory", 'yes', $expiration, "/", $servername);
            setcookie("reopen", 'yes', $expiration, "/", $servername);
            setcookie("changesec", 'yes', $expiration, "/", $servername);
            setcookie("matrix", 'yes', $expiration, "/", $servername);
            setcookie("partcodes", 'yes', $expiration, "/", $servername);
            setcookie("jobdesc", 'yes', $expiration, "/", $servername);
            setcookie("sources", 'yes', $expiration, "/", $servername);
            setcookie("rotype", 'yes', $expiration, "/", $servername);
            setcookie("mode", 'full', $expiration, "/", $servername);
            $_SESSION['mode'] = 'full';
            
            setcookie("usestddiscounts", 'yes', $expiration, "/", $servername);
            setcookie("accounting", 'yes', $expiration, "/", $servername);
            setcookie("deletecustomer", 'yes', $expiration, "/", $servername);
            setcookie("sendupdates", 'yes', $expiration, "/", $servername);
            setcookie("plan", 'platinum', $expiration, "/", $servername);

            setcookie("usestddiscounts", $tmrs['usestddiscounts'], $expiration, "/", $servername);
            setcookie("useaccounting", $tmrs['useaccounting'], $expiration, "/", $servername);
            setcookie("shopstate", $tmrs['CompanyState'], $expiration, "/", $servername);
            setcookie("merch", $tmrs['merchantaccount'], $expiration, "/", $servername);
            setcookie("useim", $tmrs['useim'], $expiration, "/", $servername);
            setcookie("matco", $tmrs['matco'], $expiration, "/", $servername);
            setcookie("protractor", $tmrs['protractor'], $expiration, "/", $servername);
            setcookie("shopname", $tmrs['CompanyName'], $expiration, "/", $servername);
            $_SESSION['shopname'] = $tmrs['CompanyName'];
            setcookie("masterinterface", 'modern', $expiration, "/", $servername);

            echo(json_encode(array('status'=>'success')));
    }
    else
    {
     echo(json_encode(array('status'=>'failed','msg'=>"Incorrect Login Credentials")));
	 exit;
    }
}
